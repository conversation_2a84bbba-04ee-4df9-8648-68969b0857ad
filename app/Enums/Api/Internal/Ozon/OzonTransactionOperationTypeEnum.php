<?php

namespace App\Enums\Api\Internal\Ozon;

enum OzonTransactionOperationTypeEnum: string
{
      case MarketplaceNotDeliveredCostItem = 'MarketplaceNotDeliveredCostItem'; // Возврат невостребованного товара от покупателя на склад.
      case MarketplaceReturnAfterDeliveryCostItem = 'MarketplaceReturnAfterDeliveryCostItem'; // Возврат от покупателя на склад после доставки.
      case MarketplaceDeliveryCostItem = 'MarketplaceDeliveryCostItem'; // Доставка товара до покупателя.
      case MarketplaceSaleReviewsItem = 'MarketplaceSaleReviewsItem'; // Приобретение отзывов на платформе.
      case ItemAdvertisementForSupplierLogistic = 'ItemAdvertisementForSupplierLogistic'; // Доставка товаров на склад Ozon — кросс-докинг.
      case OperationMarketplaceServiceStorage = 'OperationMarketplaceServiceStorage'; // Размещения товаров.
      case MarketplaceMarketingActionCostItem = 'MarketplaceMarketingActionCostItem'; // Продвижение товаров.
      case MarketplaceServiceItemInstallment = 'MarketplaceServiceItemInstallment'; // Продвижение и продажа в рассрочку.
      case MarketplaceServiceItemMarkingItems = 'MarketplaceServiceItemMarkingItems'; // Обязательная маркировка товаров.
      case MarketplaceServiceItemFlexiblePaymentSchedule = 'MarketplaceServiceItemFlexiblePaymentSchedule'; // Гибкий график выплат.
      case MarketplaceServiceItemReturnFromStock = 'MarketplaceServiceItemReturnFromStock'; // Комплектация товаров для вывоза продавцом.
      case ItemAdvertisementForSupplierLogisticSeller = 'ItemAdvertisementForSupplierLogisticSeller'; // Транспортно-экспедиционные услуги.
      case ItemAgentServiceStarsMembership = 'ItemAgentServiceStarsMembership'; // Вознаграждение за услугу «Звёздные товары».
      case MarketplaceServiceItemDelivToCustomer = 'MarketplaceServiceItemDelivToCustomer'; // Последняя миля.
      case MarketplaceServiceItemDirectFlowTrans = 'MarketplaceServiceItemDirectFlowTrans'; // Магистраль.
      case MarketplaceServiceItemDropoffFF = 'MarketplaceServiceItemDropoffFF'; // Обработка отправления.
      case MarketplaceServiceItemDropoffPVZ = 'MarketplaceServiceItemDropoffPVZ'; // Обработка отправления.
      case MarketplaceServiceItemDropoffSC = 'MarketplaceServiceItemDropoffSC'; // Обработка отправления.
      case MarketplaceServiceItemFulfillment = 'MarketplaceServiceItemFulfillment'; // Сборка заказа.
      case MarketplaceServiceItemPickup = 'MarketplaceServiceItemPickup'; // Выезд транспортного средства по адресу продавца для забора отправлений — Pick-up.
      case MarketplaceServiceItemReturnAfterDelivToCustomer = 'MarketplaceServiceItemReturnAfterDelivToCustomer'; // Обработка возврата.
      case MarketplaceServiceItemReturnFlowTrans = 'MarketplaceServiceItemReturnFlowTrans'; // Обратная магистраль.
      case MarketplaceServiceItemReturnNotDelivToCustomer = 'MarketplaceServiceItemReturnNotDelivToCustomer'; // Обработка отмен.
      case MarketplaceServiceItemReturnPartGoodsCustomer = 'MarketplaceServiceItemReturnPartGoodsCustomer'; // Обработка невыкупа.
      case MarketplaceRedistributionOfAcquiringOperation = 'MarketplaceRedistributionOfAcquiringOperation'; // Оплата эквайринга.
      case MarketplaceReturnStorageServiceAtThePickupPointFbsItem = 'MarketplaceReturnStorageServiceAtThePickupPointFbsItem'; // Краткосрочное размещение возврата FBS.
      case MarketplaceReturnStorageServiceInTheWarehouseFbsItem = 'MarketplaceReturnStorageServiceInTheWarehouseFbsItem'; // Долгосрочное размещение возврата FBS.
      case MarketplaceServiceItemDeliveryKGT = 'MarketplaceServiceItemDeliveryKGT'; // Доставка крупногабаритного товара (КГТ).
      case MarketplaceServiceItemDirectFlowLogistic = 'MarketplaceServiceItemDirectFlowLogistic'; // Логистика.
      case MarketplaceServiceItemReturnFlowLogistic = 'MarketplaceServiceItemReturnFlowLogistic'; // Обратная логистика.
      case MarketplaceServicePremiumCashbackIndividualPoints = 'MarketplaceServicePremiumCashbackIndividualPoints'; // Услуга продвижения «Бонусы продавца».
      case MarketplaceServicePremiumPromotion = 'MarketplaceServicePremiumPromotion'; // Услуга продвижение Premium, фиксированная комиссия.
      case OperationMarketplaceWithHoldingForUndeliverableGoods = 'OperationMarketplaceWithHoldingForUndeliverableGoods'; // Удержание за недовложение товара.
      case MarketplaceServiceItemDropoffPPZ = 'MarketplaceServiceItemDropoffPPZ'; // Услуга drop-off в пункте приёма заказов.
      case MarketplaceServiceItemRedistributionReturnsPVZ = 'MarketplaceServiceItemRedistributionReturnsPVZ'; // Перевыставление возвратов на ПВЗ.
      case OperationMarketplaceAgencyFeeAggregator3PLGlobal = 'OperationMarketplaceAgencyFeeAggregator3PLGlobal'; // Тарификация агентской услуги Agregator 3PL Global.
      case MarketplaceServiceItemDirectFlowLogisticVDC = 'MarketplaceServiceItemDirectFlowLogisticVDC'; // Логистика вРЦ.
  
      public function getType(): array
      {
          return match($this) {
              self::MarketplaceNotDeliveredCostItem => __('Возврат невостребованного товара от покупателя на склад.'),
              self::MarketplaceReturnAfterDeliveryCostItem => __('Возврат от покупателя на склад после доставки.'),
              self::MarketplaceDeliveryCostItem => __('Доставка товара до покупателя.'),
              self::MarketplaceSaleReviewsItem => __('Приобретение отзывов на платформе.'),
              self::ItemAdvertisementForSupplierLogistic => __('Доставка товаров на склад Ozon — кросс-докинг.'),
              self::OperationMarketplaceServiceStorage => __('Размещения товаров.'),
              self::MarketplaceMarketingActionCostItem => __('Продвижение товаров.'),
              self::MarketplaceServiceItemInstallment => __('Продвижение и продажа в рассрочку.'),
              self::MarketplaceServiceItemMarkingItems => __('Обязательная маркировка товаров.'),
              self::MarketplaceServiceItemFlexiblePaymentSchedule => __('Гибкий график выплат.'),
              self::MarketplaceServiceItemReturnFromStock => __('Комплектация товаров для вывоза продавцом.'),
              self::ItemAdvertisementForSupplierLogisticSeller => __('Транспортно-экспедиционные услуги.'),
              self::ItemAgentServiceStarsMembership => __('Вознаграждение за услугу «Звёздные товары».'),
              self::MarketplaceServiceItemDelivToCustomer => __('Последняя миля.'),
              self::MarketplaceServiceItemDirectFlowTrans => __('Магистраль.'),
              self::MarketplaceServiceItemDropoffFF => __('Обработка отправления.'),
              self::MarketplaceServiceItemDropoffPVZ => __('Обработка отправления.'),
              self::MarketplaceServiceItemDropoffSC => __('Обработка отправления.'),
              self::MarketplaceServiceItemFulfillment => __('Сборка заказа.'),
              self::MarketplaceServiceItemPickup => __('Выезд транспортного средства по адресу продавца для забора отправлений — Pick-up.'),
              self::MarketplaceServiceItemReturnAfterDelivToCustomer => __('Обработка возврата.'),
              self::MarketplaceServiceItemReturnFlowTrans => __('Обратная магистраль.'),
              self::MarketplaceServiceItemReturnNotDelivToCustomer => __('Обработка отмен.'),
              self::MarketplaceServiceItemReturnPartGoodsCustomer => __('Обработка невыкупа.'),
              self::MarketplaceRedistributionOfAcquiringOperation => __('Оплата эквайринга.'),
              self::MarketplaceReturnStorageServiceAtThePickupPointFbsItem => __('Краткосрочное размещение возврата FBS.'),
              self::MarketplaceReturnStorageServiceInTheWarehouseFbsItem => __('Долгосрочное размещение возврата FBS.'),
              self::MarketplaceServiceItemDeliveryKGT => __('Доставка крупногабаритного товара (КГТ).'),
              self::MarketplaceServiceItemDirectFlowLogistic => __('Логистика.'),
              self::MarketplaceServiceItemReturnFlowLogistic => __('Обратная логистика.'),
              self::MarketplaceServicePremiumCashbackIndividualPoints => __('Услуга продвижения «Бонусы продавца».'),
              self::MarketplaceServicePremiumPromotion => __('Услуга продвижение Premium, фиксированная комиссия.'),
              self::OperationMarketplaceWithHoldingForUndeliverableGoods => __('Удержание за недовложение товара.'),
              self::MarketplaceServiceItemDropoffPPZ => __('Услуга drop-off в пункте приёма заказов.'),
              self::MarketplaceServiceItemRedistributionReturnsPVZ => __('Перевыставление возвратов на ПВЗ.'),
              self::OperationMarketplaceAgencyFeeAggregator3PLGlobal => __('Тарификация агентской услуги Agregator 3PL Global.'),
              self::MarketplaceServiceItemDirectFlowLogisticVDC => __('Логистика вРЦ.'),
          };
      }
}
