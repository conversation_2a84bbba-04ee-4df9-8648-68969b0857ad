<?php

namespace App\Http\Requests\Api\Internal\Barcodes;

use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use Illuminate\Foundation\Http\FormRequest;

class BarcodeUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'barcodes' => 'required|array',
            'barcodes.value' => 'required|string|max:48',  // штрихкода
        ];
    }

    public function toDTO(): BarcodeDTO
    {
        return BarcodeDTO::fromArray(array_merge(
            $this->validated(),
            ['resource_id' => $this->route('id')]
        ));
    }
}
