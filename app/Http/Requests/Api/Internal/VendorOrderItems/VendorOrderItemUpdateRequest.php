<?php

namespace App\Http\Requests\Api\Internal\VendorOrderItems;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO\VendorOrderItemDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class VendorOrderItemUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:1',
            'discount' => 'nullable|numeric|min:0',
            'vat_rate_id' => 'nullable|UUID',
            'currency_id' => 'required|UUID',
            'price_in_currency' => 'required|numeric|min:0',
            'currency_rate_to_base' => 'required|numeric|min:0',
        ];
    }

    public function toDTO(): VendorOrderItemDTO
    {
        return VendorOrderItemDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
