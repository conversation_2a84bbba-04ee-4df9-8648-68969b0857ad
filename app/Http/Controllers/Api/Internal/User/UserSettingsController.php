<?php

namespace App\Http\Controllers\Api\Internal\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\UserSettingsRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class UserSettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {

        return response()->json(Auth()->user()->load('userSettings'), 200);

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserSettingsRequest $request): JsonResponse
    {
        $validator = $request->validated();

        $user = $request->user();

        DB::table('users')
            ->where('id', $user->id)
            ->update([
                'lastname' => $validator['lastname'] ?? null,
                'firstname' => $validator['firstname'],
                'patronymic' => $validator['patronymic'] ?? null,
                'tel' => $validator['tel'] ?? null,
                'email' => $validator['email'] ?? $user->email,
                'password' => isset($validator['password']) ? Hash::make($validator['password']) : $user->password,
                'updated_at' => Carbon::now(),
            ]);

        $image = $request->file('image');

        if ($image) {
            $path = Storage::disk('s3-images')->put('user_avatars', $image);
        }

        DB::table('user_settings')
            ->where('user_id', $user->id)
            ->update([
                'inn' => $validator['inn'] ?? null,
                'language' => $validator['language'] ?? null,
                'printing_documents' => $validator['printing_documents'] ?? false,
                'additional_fields' => $validator['additional_fields'] ?? 0,
                'start_screen' => $validator['start_screen'] ?? null,
                'update_reports_automatically' => $validator['update_reports_automatically'] ?? false,
                'signature_sent_emails' => $validator['signature_sent_emails'] ?? null,
                'updated_at' => Carbon::now(),
                'image' => $path ?? null
            ]);

        return response()->json([], 204);
    }

    public function discount(string $userId, Request $request): JsonResponse
    {
        DB::table('user_settings')
            ->where('user_id', $userId)
            ->update([
                'discount' => $request['discount'],
                'updated_at' => Carbon::now(),
            ]);

        return response()->json([], 204);

    }

}
