<?php

namespace App\Http\Controllers\Api\Internal\WarehouseControllers\Contacts;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehousePhonePolicyContract;
use App\Contracts\Services\Internal\Warehouses\WarehousePhonesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Warehouses\Contacts\Phone\WarehousePhoneIndexRequest;
use App\Http\Requests\Api\Internal\Warehouses\Contacts\Phone\WarehousePhoneStoreRequest;
use App\Http\Requests\Api\Internal\Warehouses\Contacts\Phone\WarehousePhoneUpdateRequest;
use App\Http\Resources\Warehouses\Contacts\WarehousePhoneIndexCollection;
use App\Http\Resources\Warehouses\Contacts\WarehousePhoneShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehousePhoneController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehousePhonesServiceContract $service,
        private readonly WarehousePhonePolicyContract $policy
    ) {
    }

    /**
     * @response WarehousePhoneIndexCollection<WarehousePhoneIndexResource>
     */
    public function index(WarehousePhoneIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            $data = $this->service->index($data);
            $collection = new WarehousePhoneIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(WarehousePhoneStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response WarehousePhoneShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(WarehousePhoneShowResource::make($data));
        });
    }

    public function update(WarehousePhoneUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
