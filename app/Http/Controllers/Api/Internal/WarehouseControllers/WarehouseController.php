<?php

namespace App\Http\Controllers\Api\Internal\WarehouseControllers;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehousePolicyContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Warehouses\WarehouseIndexRequest;
use App\Http\Requests\Api\Internal\Warehouses\WarehouseStoreRequest;
use App\Http\Requests\Api\Internal\Warehouses\WarehouseUpdateRequest;
use App\Http\Resources\Warehouses\WarehouseIndexCollection;
use App\Http\Resources\Warehouses\WarehouseShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseServiceContract $service,
        private readonly WarehousePolicyContract $policy
    ) {
    }

    /**
     * @response WarehouseIndexCollection<WarehouseIndexResource>
     */
    public function index(WarehouseIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $data = $this->service->index($data);
            $collection = new WarehouseIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(WarehouseStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response WarehouseShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(WarehouseShowResource::make($data));
        });
    }

    public function update(WarehouseUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
