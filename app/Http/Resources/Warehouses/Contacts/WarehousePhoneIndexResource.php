<?php

namespace App\Http\Resources\Warehouses\Contacts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehousePhoneIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор телефона склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $phone Номер телефона */
            'phone' => $this->when(
                property_exists($this->resource, 'phone'),
                function () {
                    return $this->resource->phone;
                }
            ),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function () {
                    return $this->resource->comment;
                }
            ),
        ];
    }
}
