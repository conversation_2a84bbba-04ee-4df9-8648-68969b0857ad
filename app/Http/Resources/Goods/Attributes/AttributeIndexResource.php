<?php

namespace App\Http\Resources\Goods\Attributes;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttributeIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор атрибута */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string|null $attribute_groups_id Идентификатор группы атрибутов */
            'attribute_groups_id' => $this->when(
                property_exists($this->resource, 'attribute_groups_id'),
                function() {
                    return $this->resource->attribute_groups_id;
                }
            ),
            /** @var string $name Название атрибута */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string|null $description Описание атрибута */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function() {
                    return $this->resource->description;
                }
            ),
            /** @var int $sort_order Порядок сортировки */
            'sort_order' => $this->when(
                property_exists($this->resource, 'sort_order'),
                function() {
                    return (int) $this->resource->sort_order;
                }
            ),
            /** @var bool $status Статус активности */
            'status' => $this->when(
                property_exists($this->resource, 'status'),
                function() {
                    return (bool) $this->resource->status;
                }
            ),
        ];
    }
}
