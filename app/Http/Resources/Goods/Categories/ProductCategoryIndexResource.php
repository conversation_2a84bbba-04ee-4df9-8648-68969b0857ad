<?php

namespace App\Http\Resources\Goods\Categories;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductCategoryIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор категории */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string|null $parent_id Идентификатор родительской категории */
            'parent_id' => $this->when(
                property_exists($this->resource, 'parent_id'),
                function() {
                    return $this->resource->parent_id;
                }
            ),
            /** @var string $name Название категории */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return (string) $this->resource->name;
                }
            ),
        ];
    }
}
