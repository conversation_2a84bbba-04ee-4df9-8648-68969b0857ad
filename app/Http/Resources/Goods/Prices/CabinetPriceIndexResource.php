<?php

namespace App\Http\Resources\Goods\Prices;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CabinetPriceIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор цены */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $name Название цены */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return (string) $this->resource->name;
                }
            ),
            /** @var int $sort Порядок сортировки */
            'sort' => $this->when(
                property_exists($this->resource, 'sort'),
                function() {
                    return (int) $this->resource->sort;
                }
            ),
        ];
    }
}
