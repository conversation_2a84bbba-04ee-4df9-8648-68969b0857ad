<?php

namespace App\Http\Resources\Goods\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductSizIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var array{id: string, name: string}[] $sizNames Названия размеров */
            'sizNames' => $this->when(
                isset($this['sizNames']),
                function() {
                    return array_map(function($sizName) {
                        return [
                            'id' => $sizName->id,
                            'name' => (string) $sizName->name,
                            'created_at' => $sizName->created_at,
                            'updated_at' => $sizName->updated_at,
                            'deleted_at' => $sizName->deleted_at,
                        ];
                    }, $this['sizNames']->toArray());
                }
            ),
            /** @var array{id: string, name: string}[] $sizTypes Типы размеров */
            'sizTypes' => $this->when(
                isset($this['sizTypes']),
                function() {
                    return array_map(function($sizType) {
                        return [
                            'id' => $sizType->id,
                            'product_siz_name_id' => (string) $sizType->product_siz_name_id,
                            'code' => (string) $sizType->code,
                            'title' => (string) $sizType->title,
                            'created_at' => $sizType->created_at,
                            'updated_at' => $sizType->updated_at,
                            'deleted_at' => $sizType->deleted_at,
                        ];
                    }, $this['sizTypes']->toArray());
                }
            ),
        ];
    }
}
