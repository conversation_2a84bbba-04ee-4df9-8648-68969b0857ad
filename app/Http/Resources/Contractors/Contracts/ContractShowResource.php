<?php

namespace App\Http\Resources\Contractors\Contracts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор контракта */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $number Номер контракта */
            'number' => (string) $this->number,
            /** @var string $date_from Дата контракта */
            'date_from' => $this->date_from,
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->currency_id,
            /** @var string $type Тип контракта */
            'type' => (string) $this->type,
            /** @var string|null $code Код */
            'code' => $this->code,
            /** @var string|null $amount Сумма */
            'amount' => $this->amount ? (string) $this->amount : null,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $shared_access Общий доступ */
            'shared_access' => (bool) $this->shared_access,
            /** @var bool $is_printed Напечатан */
            'is_printed' => (bool) $this->is_printed,
            /** @var bool $is_sended Отправлен */
            'is_sended' => (bool) $this->is_sended,
        ];
    }
}
