<?php

namespace App\Http\Resources\References\LegalEntities;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LegalEntityIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор юридического лица */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function() {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $short_name Краткое название */
            'short_name' => (string) $this->short_name,
            /** @var string|null $code Код */
            'code' => $this->when(
                property_exists($this->resource, 'code'),
                function() {
                    return $this->resource->code;
                }
            ),
            /** @var string|null $phone Телефон */
            'phone' => $this->when(
                property_exists($this->resource, 'phone'),
                function() {
                    return $this->resource->phone;
                }
            ),
            /** @var string|null $fax Факс */
            'fax' => $this->when(
                property_exists($this->resource, 'fax'),
                function() {
                    return $this->resource->fax;
                }
            ),
            /** @var string|null $email Email */
            'email' => $this->when(
                property_exists($this->resource, 'email'),
                function() {
                    return $this->resource->email;
                }
            ),
            /** @var string|null $discount_card Дисконтная карта */
            'discount_card' => $this->when(
                property_exists($this->resource, 'discount_card'),
                function() {
                    return $this->resource->discount_card;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var bool $is_default По умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var bool $shared_access Общий доступ */
            'shared_access' => (bool) $this->shared_access,
            /** @var array{id: string, name: string, path: string, size: int, mime_type: string}|null $logo Логотип */
            'logo' => $this->when(!empty($this->logo), [
                'id' => $this->logo['id'],
                'name' => $this->when(isset($this->logo['name']), $this->logo['name']),
                'path' => $this->when(isset($this->logo['path']), $this->logo['path']),
                'size' => (int) $this->logo['size'],
                'mime_type' => $this->when(isset($this->logo['mime_type']), $this->logo['mime_type']),
            ]),
        ];
    }
}
