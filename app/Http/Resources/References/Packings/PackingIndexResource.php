<?php

namespace App\Http\Resources\References\Packings;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PackingIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор упаковки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var string $name Название упаковки */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string|null $description Описание */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function() {
                    return $this->resource->description;
                }
            ),
            /** @var string|null $length Длина */
            'length' => $this->when(
                property_exists($this->resource, 'length'),
                function() {
                    return $this->resource->length ? (string) $this->resource->length : null;
                }
            ),
            /** @var string|null $width Ширина */
            'width' => $this->when(
                property_exists($this->resource, 'width'),
                function() {
                    return $this->resource->width ? (string) $this->resource->width : null;
                }
            ),
            /** @var string|null $height Высота */
            'height' => $this->when(
                property_exists($this->resource, 'height'),
                function() {
                    return $this->resource->height ? (string) $this->resource->height : null;
                }
            ),
            /** @var string $measurement_unit_size_id Идентификатор единицы измерения размера */
            'measurement_unit_size_id' => $this->when(
                property_exists($this->resource, 'measurement_unit_size_id'),
                function() {
                    return $this->resource->measurement_unit_size_id;
                }
            ),
            /** @var string|null $weight Вес */
            'weight' => $this->when(
                property_exists($this->resource, 'weight'),
                function() {
                    return $this->resource->weight ? (string) $this->resource->weight : null;
                }
            ),
            /** @var string $measurement_unit_weight_id Идентификатор единицы измерения веса */
            'measurement_unit_weight_id' => $this->when(
                property_exists($this->resource, 'measurement_unit_weight_id'),
                function() {
                    return $this->resource->measurement_unit_weight_id;
                }
            ),
            /** @var string|null $volume Объем */
            'volume' => $this->when(
                property_exists($this->resource, 'volume'),
                function() {
                    return $this->resource->volume ? (string) $this->resource->volume : null;
                }
            ),
            /** @var string $measurement_unit_volume_id Идентификатор единицы измерения объема */
            'measurement_unit_volume_id' => $this->when(
                property_exists($this->resource, 'measurement_unit_volume_id'),
                function() {
                    return $this->resource->measurement_unit_volume_id;
                }
            ),
        ];
    }
}
