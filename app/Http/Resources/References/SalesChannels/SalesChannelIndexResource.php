<?php

namespace App\Http\Resources\References\SalesChannels;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalesChannelIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор канала продаж */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function() {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $name Название канала продаж */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string $sales_channel_type_id Идентификатор типа канала продаж */
            'sales_channel_type_id' => $this->when(
                property_exists($this->resource, 'sales_channel_type_id'),
                function() {
                    return $this->resource->sales_channel_type_id;
                }
            ),
            /** @var string|null $description Описание */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function() {
                    return $this->resource->description;
                }
            ),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function() {
                    return (bool) $this->resource->is_default;
                }
            ),
            /** @var bool $is_common Общий */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function() {
                    return (bool) $this->resource->is_common;
                }
            ),
            /** @var int $sort Порядок сортировки */
            'sort' => $this->when(
                property_exists($this->resource, 'sort'),
                function() {
                    return (int) $this->resource->sort;
                }
            ),
            /** @var array{id: string, firstname: string, lastname: string|null, email: string}|null $employee Сотрудник */
            'employee' => $this->when(
                property_exists($this->resource, 'employee'),
                function() {
                    return [
                        'id' => $this->employee['id'],
                        'firstname' => $this->when(isset($this->employee['firstname']), $this->employee['firstname']),
                        'lastname' => $this->employee['lastname'] ?? null,
                        'email' => $this->when(isset($this->employee['email']), $this->employee['email']),
                    ];
                }
            ),
            /** @var array{id: string, name: string}|null $department Отдел */
            'department' => $this->when(
                property_exists($this->resource, 'department'),
                function() {
                    return [
                        'id' => $this->department['id'],
                        'name' => $this->when(isset($this->department['name']), $this->department['name']),
                    ];
                }
            ),
            /** @var array{id: string, name: string}|null $type Тип канала продаж */
            'type' => $this->when(
                property_exists($this->resource, 'type'),
                function() {
                    return [
                        'id' => $this->type['id'],
                        'name' => $this->when(isset($this->type['name']), $this->type['name']),
                    ];
                }
            ),
        ];
    }
}
