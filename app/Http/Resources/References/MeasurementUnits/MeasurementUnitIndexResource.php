<?php

namespace App\Http\Resources\References\MeasurementUnits;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MeasurementUnitIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор единицы измерения */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $name Полное название единицы измерения */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return $this->resource->name;
                }
            ),
            /** @var string|null $short_name Краткое название единицы измерения */
            'short_name' => $this->when(
                property_exists($this->resource, 'short_name'),
                function() {
                    return $this->resource->short_name;
                }
            ),
            /** @var string|null $code Код единицы измерения */
            'code' => $this->when(
                property_exists($this->resource, 'code'),
                function() {
                    return $this->resource->code;
                }
            ),
            /** @var string $conversion_factor Коэффициент пересчета */
            'conversion_factor' => (string) $this->conversion_factor,
            /** @var string|null $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $group_id Идентификатор группы единиц измерения */
            'group_id' => $this->when(
                property_exists($this->resource, 'group_id'),
                function() {
                    return $this->resource->group_id;
                }
            ),
            /** @var bool $is_default Единица измерения по умолчанию */
            'is_default' => (bool) $this->is_default,
        ];
    }
}
