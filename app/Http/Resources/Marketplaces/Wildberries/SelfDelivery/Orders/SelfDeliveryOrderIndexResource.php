<?php

namespace App\Http\Resources\Marketplaces\Wildberries\SelfDelivery\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SelfDeliveryOrderIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор заказа */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->when(
                property_exists($this->resource, 'integration_id'),
                function() {
                    return $this->resource->integration_id;
                }
            ),
            /** @var string|null $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(
                property_exists($this->resource, 'legal_entity_id'),
                function() {
                    return $this->resource->legal_entity_id;
                }
            ),
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(
                property_exists($this->resource, 'contractor_id'),
                function() {
                    return $this->resource->contractor_id;
                }
            ),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string|null $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->when(
                property_exists($this->resource, 'warehouse_id'),
                function() {
                    return $this->resource->warehouse_id;
                }
            ),
            /** @var string|null $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function() {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string|null $customer_order_id Идентификатор заказа покупателя */
            'customer_order_id' => $this->when(
                property_exists($this->resource, 'customer_order_id'),
                function() {
                    return $this->resource->customer_order_id;
                }
            ),
            /** @var string $module_status Статус в нашей системе */
            'module_status' => $this->when(
                property_exists($this->resource, 'module_status'),
                function() {
                    return $this->resource->module_status;
                }
            ),
            /** @var string|null $supplier_status Статус поставщика */
            'supplier_status' => $this->when(
                property_exists($this->resource, 'supplier_status'),
                function() {
                    return $this->resource->supplier_status;
                }
            ),
            /** @var string|null $wb_status Статус в Wildberries */
            'wb_status' => $this->when(
                property_exists($this->resource, 'wb_status'),
                function() {
                    return $this->resource->wb_status;
                }
            ),
            /** @var bool $has_unmatched_items Признак несопоставленных товаров */
            'has_unmatched_items' => $this->when(
                property_exists($this->resource, 'has_unmatched_items'),
                function() {
                    return (bool) $this->resource->has_unmatched_items;
                }
            ),
            /** @var bool $needs_warehouse_mapping Требуется сопоставление склада */
            'needs_warehouse_mapping' => $this->when(
                property_exists($this->resource, 'needs_warehouse_mapping'),
                function() {
                    return (bool) $this->resource->needs_warehouse_mapping;
                }
            ),
            /** @var string|null $number Номер заказа в нашей системе */
            'number' => $this->when(
                property_exists($this->resource, 'number'),
                function() {
                    return $this->resource->number;
                }
            ),
            /** @var int|null $wb_number Номер заказа в Wildberries */
            'wb_number' => $this->when(
                property_exists($this->resource, 'wb_number'),
                function() {
                    return $this->resource->wb_number;
                }
            ),
            /** @var string|null $tracking_number Номер отслеживания */
            'tracking_number' => $this->when(
                property_exists($this->resource, 'tracking_number'),
                function() {
                    return $this->resource->tracking_number;
                }
            ),
            /** @var string|null $comment Комментарий к заказу */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function() {
                    return $this->resource->comment;
                }
            ),
            /** @var string|null $total_price Общая сумма заказа */
            'total_price' => $this->when(
                property_exists($this->resource, 'total_price'),
                function() {
                    return $this->resource->total_price ? (string) $this->resource->total_price : null;
                }
            ),
            /** @var bool $reserve Признак резервирования */
            'reserve' => $this->when(
                property_exists($this->resource, 'reserve'),
                function() {
                    return (bool) $this->resource->reserve;
                }
            ),
            /** @var string|null $delivery_date Дата доставки */
            'delivery_date' => $this->when(
                property_exists($this->resource, 'delivery_date'),
                function() {
                    return $this->resource->delivery_date;
                }
            ),
            /** @var string|null $delivery_type Тип доставки */
            'delivery_type' => $this->when(
                property_exists($this->resource, 'delivery_type'),
                function() {
                    return $this->resource->delivery_type;
                }
            ),
            /** @var int|null $cargo_type Тип груза */
            'cargo_type' => $this->when(
                property_exists($this->resource, 'cargo_type'),
                function() {
                    return $this->resource->cargo_type;
                }
            ),
            /** @var int|null $wb_warehouse_id Идентификатор склада WB */
            'wb_warehouse_id' => $this->when(
                property_exists($this->resource, 'wb_warehouse_id'),
                function() {
                    return $this->resource->wb_warehouse_id;
                }
            ),
            /** @var string|null $office_list Список офисов */
            'office_list' => $this->when(
                property_exists($this->resource, 'office_list'),
                function() {
                    return $this->resource->office_list;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),

            // Специфичные поля для заказов самовывоза
            /** @var string|null $delivery_phone Телефон для доставки */
            'delivery_phone' => $this->when(
                property_exists($this->resource, 'delivery_phone'),
                function() {
                    return $this->resource->delivery_phone;
                }
            ),
            /** @var string|null $first_name Имя получателя */
            'first_name' => $this->when(
                property_exists($this->resource, 'first_name'),
                function() {
                    return $this->resource->first_name;
                }
            ),
            /** @var string|null $warehouse_address Адрес склада */
            'warehouse_address' => $this->when(
                property_exists($this->resource, 'warehouse_address'),
                function() {
                    return $this->resource->warehouse_address;
                }
            ),
            /** @var string $pay_mode Способ оплаты */
            'pay_mode' => $this->when(
                property_exists($this->resource, 'pay_mode'),
                function() {
                    return $this->resource->pay_mode ?? 'unknown';
                }
            ),
        ];
    }
}
