<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Supplies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupplyIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор поставки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->when(
                property_exists($this->resource, 'integration_id'),
                function() {
                    return $this->resource->integration_id;
                }
            ),
            /** @var string|null $supply_id ID поставки в Wildberries */
            'supply_id' => $this->when(
                property_exists($this->resource, 'supply_id'),
                function() {
                    return $this->resource->supply_id;
                }
            ),
            /** @var string $name Название поставки */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return $this->resource->name;
                }
            ),
            /** @var bool $done Флаг завершения поставки */
            'done' => $this->when(
                property_exists($this->resource, 'done'),
                function() {
                    return (bool) $this->resource->done;
                }
            ),
            /** @var string|null $closed_at Дата закрытия поставки */
            'closed_at' => $this->when(
                property_exists($this->resource, 'closed_at'),
                function() {
                    return $this->resource->closed_at;
                }
            ),
            /** @var string|null $scanned_at Дата сканирования */
            'scanned_at' => $this->when(
                property_exists($this->resource, 'scanned_at'),
                function() {
                    return $this->resource->scanned_at;
                }
            ),
            /** @var int|null $cargo_type Габаритный тип поставки */
            'cargo_type' => $this->when(
                property_exists($this->resource, 'cargo_type'),
                function() {
                    return $this->resource->cargo_type;
                }
            ),
            /** @var string|null $created_at_wb Дата создания поставки в Wildberries */
            'created_at_wb' => $this->when(
                property_exists($this->resource, 'created_at_wb'),
                function() {
                    return $this->resource->created_at_wb;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
        ];
    }
}
