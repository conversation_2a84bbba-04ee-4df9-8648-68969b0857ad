<?php

namespace App\Http\Resources\Money\OutgoingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OutgoingPaymentIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор исходящего платежа */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(
                property_exists($this->resource, 'status_id'),
                function() {
                    return $this->resource->status_id;
                }
            ),
            /** @var string|null $number Номер платежа */
            'number' => $this->when(
                property_exists($this->resource, 'number'),
                function() {
                    return $this->resource->number;
                }
            ),
            /** @var string|null $date_from Дата платежа */
            'date_from' => $this->when(
                property_exists($this->resource, 'date_from'),
                function() {
                    return $this->resource->date_from;
                }
            ),
            /** @var bool $held Проведен */
            'held' => $this->when(
                property_exists($this->resource, 'held'),
                function() {
                    return (bool) $this->resource->held;
                }
            ),
            /** @var bool $without_closing_documents Без закрывающих документов */
            'without_closing_documents' => $this->when(
                property_exists($this->resource, 'without_closing_documents'),
                function() {
                    return (bool) $this->resource->without_closing_documents;
                }
            ),
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(
                property_exists($this->resource, 'legal_entity_id'),
                function() {
                    return $this->resource->legal_entity_id;
                }
            ),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(
                property_exists($this->resource, 'contractor_id'),
                function() {
                    return $this->resource->contractor_id;
                }
            ),
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->when(
                property_exists($this->resource, 'sales_channel_id'),
                function() {
                    return $this->resource->sales_channel_id;
                }
            ),
            /** @var string $sum Сумма */
            'sum' => $this->when(
                property_exists($this->resource, 'sum'),
                function() {
                    return (string) $this->resource->sum;
                }
            ),
            /** @var string $included_vat НДС включен */
            'included_vat' => $this->when(
                property_exists($this->resource, 'included_vat'),
                function() {
                    return (string) $this->resource->included_vat;
                }
            ),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function() {
                    return $this->resource->comment;
                }
            ),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function() {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string $currency_value Курс валюты */
            'currency_value' => $this->when(
                property_exists($this->resource, 'currency_value'),
                function() {
                    return (string) $this->resource->currency_value;
                }
            ),
            /** @var string $bounded_sum Связанная сумма */
            'bounded_sum' => $this->when(
                property_exists($this->resource, 'bounded_sum'),
                function() {
                    return (string) $this->resource->bounded_sum;
                }
            ),
            /** @var string $not_bounded_sum Несвязанная сумма */
            'not_bounded_sum' => $this->when(
                property_exists($this->resource, 'not_bounded_sum'),
                function() {
                    return (string) $this->resource->not_bounded_sum;
                }
            ),
            /** @var bool $is_imported Импортирован */
            'is_imported' => $this->when(
                property_exists($this->resource, 'is_imported'),
                function() {
                    return (bool) $this->resource->is_imported;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
        ];
    }
}
