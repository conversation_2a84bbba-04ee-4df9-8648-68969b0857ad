<?php

namespace App\Http\Resources\Money\OutgoingPayments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OutgoingPaymentItemIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор позиции исходящего платежа */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $outgoing_payment_id Идентификатор исходящего платежа */
            'outgoing_payment_id' => $this->when(
                property_exists($this->resource, 'outgoing_payment_id'),
                function() {
                    return $this->resource->outgoing_payment_id;
                }
            ),
            /** @var string $document_id Идентификатор документа */
            'document_id' => $this->when(
                property_exists($this->resource, 'document_id'),
                function() {
                    return $this->resource->document_id;
                }
            ),
            /** @var string $paid_in Сумма к оплате */
            'paid_in' => $this->when(
                property_exists($this->resource, 'paid_in'),
                function() {
                    return (string) $this->resource->paid_in;
                }
            ),
        ];
    }
}
