<?php

namespace App\Http\Resources\Procurement\VendorOrders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorOrderItemIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $order_id Идентификатор заказа поставщика */
            'order_id' => $this->when(
                property_exists($this->resource, 'order_id'),
                function () {
                    return $this->resource->order_id;
                }
            ),
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->when(
                property_exists($this->resource, 'product_id'),
                function () {
                    return $this->resource->product_id;
                }
            ),
            /** @var int $quantity Количество товара */
            'quantity' => $this->when(
                property_exists($this->resource, 'quantity'),
                function () {
                    return $this->resource->quantity;
                }
            ),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function () {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string $price_in_currency Цена в валюте */
            'price_in_currency' => $this->when(
                property_exists($this->resource, 'price_in_currency'),
                function () {
                    return (string) $this->resource->price_in_currency;
                }
            ),
            /** @var string $currency_rate_to_base Курс валюты к базовой */
            'currency_rate_to_base' => $this->when(
                property_exists($this->resource, 'currency_rate_to_base'),
                function () {
                    return (string) $this->resource->currency_rate_to_base;
                }
            ),
            /** @var string $price_in_base Цена в базовой валюте */
            'price_in_base' => $this->when(
                property_exists($this->resource, 'price_in_base'),
                function () {
                    return (string) $this->resource->price_in_base;
                }
            ),
            /** @var string $amount_in_base Сумма в базовой валюте */
            'amount_in_base' => $this->when(
                property_exists($this->resource, 'amount_in_base'),
                function () {
                    return (string) $this->resource->amount_in_base;
                }
            ),
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            'vat_rate_id' => $this->when(
                property_exists($this->resource, 'vat_rate_id'),
                function () {
                    return $this->resource->vat_rate_id;
                }
            ),
            /** @var string $discount Размер скидки */
            'discount' => $this->when(
                property_exists($this->resource, 'discount'),
                function () {
                    return (string) $this->resource->discount;
                }
            ),
            /** @var int $accepted_quantity Принятое количество (вычисляемое поле) */
            'accepted_quantity' => $this->when(
                property_exists($this->resource, 'accepted_quantity'),
                function () {
                    return (int) $this->resource->accepted_quantity;
                }
            ),
            /** @var int $available_quantity Доступное количество на складе (вычисляемое поле) */
            'available_quantity' => $this->when(
                property_exists($this->resource, 'available_quantity'),
                function () {
                    return (int) $this->resource->available_quantity;
                }
            ),
            /** @var int $recidual Остаток на складе (вычисляемое поле) */
            'recidual' => $this->when(
                property_exists($this->resource, 'recidual'),
                function () {
                    return (int) $this->resource->recidual;
                }
            ),
        ];
    }
}
