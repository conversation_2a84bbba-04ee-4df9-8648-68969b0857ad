<?php

namespace App\Http\Resources\Sales\ComissionReports;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IssuedComissionReportIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор отчета */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(
                property_exists($this->resource, 'status_id'),
                function() {
                    return $this->resource->status_id;
                }
            ),
            /** @var string $number Номер отчета */
            'number' => (string) $this->number,
            /** @var string $date_from Дата отчета */
            'date_from' => $this->when(
                property_exists($this->resource, 'date_from'),
                function() {
                    return $this->resource->date_from;
                }
            ),
            /** @var bool $is_held Проведен */
            'is_held' => (bool) $this->is_held,
            /** @var bool $is_printed Напечатан */
            'is_printed' => (bool) $this->is_printed,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(
                property_exists($this->resource, 'legal_entity_id'),
                function() {
                    return $this->resource->legal_entity_id;
                }
            ),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(
                property_exists($this->resource, 'contractor_id'),
                function() {
                    return $this->resource->contractor_id;
                }
            ),
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->when(
                property_exists($this->resource, 'sales_channel_id'),
                function() {
                    return $this->resource->sales_channel_id;
                }
            ),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function() {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string $sum Сумма */
            'sum' => (string) $this->sum,
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function() {
                    return $this->resource->comment;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var string $comission_type Тип комиссии */
            'comission_type' => (string) $this->comission_type,
            /** @var string $comission_value Значение комиссии */
            'comission_value' => (string) $this->comission_value,
            /** @var bool $is_common Общий отчет */
            'is_common' => (bool) $this->is_common,
            /** @var string $period_from Период с */
            'period_from' => $this->when(
                property_exists($this->resource, 'period_from'),
                function() {
                    return $this->resource->period_from;
                }
            ),
            /** @var string $period_to Период по */
            'period_to' => $this->when(
                property_exists($this->resource, 'period_to'),
                function() {
                    return $this->resource->period_to;
                }
            ),
            /** @var string $contract_id Идентификатор контракта */
            'contract_id' => $this->when(
                property_exists($this->resource, 'contract_id'),
                function() {
                    return $this->resource->contract_id;
                }
            ),
        ];
    }
}
