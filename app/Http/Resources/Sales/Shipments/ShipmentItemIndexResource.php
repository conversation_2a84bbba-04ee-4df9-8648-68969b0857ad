<?php

namespace App\Http\Resources\Sales\Shipments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShipmentItemIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $result = [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $shipment_id Идентификатор отгрузки */
            'shipment_id' => $this->when(
                property_exists($this->resource, 'shipment_id'),
                function () {
                    return $this->resource->shipment_id;
                }
            ),
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->when(
                property_exists($this->resource, 'product_id'),
                function () {
                    return $this->resource->product_id;
                }
            ),
            /** @var int $quantity Количество товара */
            'quantity' => $this->when(
                property_exists($this->resource, 'quantity'),
                function () {
                    return (int) $this->resource->quantity;
                }
            ),
            /** @var string $price Цена товара */
            'price' => $this->when(
                property_exists($this->resource, 'price'),
                function () {
                    return (string) $this->resource->price;
                }
            ),
            /** @var string $cost Себестоимость */
            'cost' => $this->when(
                property_exists($this->resource, 'cost'),
                function () {
                    return (string) $this->resource->cost;
                }
            ),
            /** @var string $total_cost Общая себестоимость */
            'total_cost' => $this->when(
                property_exists($this->resource, 'total_cost'),
                function () {
                    return (string) $this->resource->total_cost;
                }
            ),
            /** @var string $total_price Общая стоимость */
            'total_price' => $this->when(
                property_exists($this->resource, 'total_price'),
                function () {
                    return (string) $this->resource->total_price;
                }
            ),
            /** @var string $profit Прибыль */
            'profit' => $this->when(
                property_exists($this->resource, 'profit'),
                function () {
                    return (string) $this->resource->profit;
                }
            ),
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            'vat_rate_id' => $this->when(
                property_exists($this->resource, 'vat_rate_id'),
                function () {
                    return $this->resource->vat_rate_id;
                }
            ),
            /** @var string $discount Размер скидки */
            'discount' => $this->when(
                property_exists($this->resource, 'discount'),
                function () {
                    return (string) $this->resource->discount;
                }
            ),
            /** @var int $recidual Остаток (вычисляемое поле) */
            'recidual' => $this->when(
                property_exists($this->resource, 'recidual'),
                function () {
                    return (int) $this->resource->recidual;
                }
            ),
            /** @var array{id: string, title: string, code: string, article: string}|null $product Информация о товаре */
            'product' => $this->when(
                property_exists($this->resource, 'product'),
                function () {
                    return [
                        'id' => $this->product['id'],
                        'title' => $this->when(isset($this->product['title']), $this->product['title']),
                        'code' => $this->when(isset($this->product['code']), $this->product['code']),
                        'article' => $this->when(isset($this->product['article']), $this->product['article']),
                    ];
                }
            ),
        ];

        return $result;
    }
}
