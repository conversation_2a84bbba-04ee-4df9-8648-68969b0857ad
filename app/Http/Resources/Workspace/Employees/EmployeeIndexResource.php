<?php

namespace App\Http\Resources\Workspace\Employees;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор сотрудника */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function() {
                    return $this->resource->archived_at;
                }
            ),
            /** @var int $user_id Идентификатор пользователя */
            'user_id' => (int) $this->user_id,
            /** @var string|null $lastname Фамилия */
            'lastname' => $this->when(
                property_exists($this->resource, 'lastname'),
                function() {
                    return $this->resource->lastname;
                }
            ),
            /** @var string $firstname Имя */
            'firstname' => (string) $this->firstname,
            /** @var string|null $patronymic Отчество */
            'patronymic' => $this->when(
                property_exists($this->resource, 'patronymic'),
                function() {
                    return $this->resource->patronymic;
                }
            ),
            /** @var string|null $telephone Телефон */
            'telephone' => $this->when(
                property_exists($this->resource, 'telephone'),
                function() {
                    return $this->resource->telephone;
                }
            ),
            /** @var string $email Email */
            'email' => (string) $this->email,
            /** @var string $status Статус */
            'status' => (string) $this->status,
            /** @var string|null $job_number Табельный номер */
            'job_number' => $this->when(
                property_exists($this->resource, 'job_number'),
                function() {
                    return $this->resource->job_number;
                }
            ),
            /** @var string|null $citizenship Гражданство */
            'citizenship' => $this->when(
                property_exists($this->resource, 'citizenship'),
                function() {
                    return $this->resource->citizenship;
                }
            ),
            /** @var string|null $gender Пол */
            'gender' => $this->when(
                property_exists($this->resource, 'gender'),
                function() {
                    return $this->resource->gender;
                }
            ),
            /** @var string|null $inn ИНН */
            'inn' => $this->when(
                property_exists($this->resource, 'inn'),
                function() {
                    return $this->resource->inn;
                }
            ),
            /** @var string|null $id_card Удостоверение личности */
            'id_card' => $this->when(
                property_exists($this->resource, 'id_card'),
                function() {
                    return $this->resource->id_card;
                }
            ),
            /** @var string|null $passport_series Серия паспорта */
            'passport_series' => $this->when(
                property_exists($this->resource, 'passport_series'),
                function() {
                    return $this->resource->passport_series;
                }
            ),
            /** @var string|null $passport_number Номер паспорта */
            'passport_number' => $this->when(
                property_exists($this->resource, 'passport_number'),
                function() {
                    return $this->resource->passport_number;
                }
            ),
            /** @var string|null $passport_issue_date Дата выдачи паспорта */
            'passport_issue_date' => $this->when(
                property_exists($this->resource, 'passport_issue_date'),
                function() {
                    return $this->resource->passport_issue_date;
                }
            ),
            /** @var string|null $who_issued_passport Кем выдан паспорт */
            'who_issued_passport' => $this->when(
                property_exists($this->resource, 'who_issued_passport'),
                function() {
                    return $this->resource->who_issued_passport;
                }
            ),
            /** @var string|null $division_code Код подразделения */
            'division_code' => $this->when(
                property_exists($this->resource, 'division_code'),
                function() {
                    return $this->resource->division_code;
                }
            ),
            /** @var string|null $registration_address Адрес регистрации */
            'registration_address' => $this->when(
                property_exists($this->resource, 'registration_address'),
                function() {
                    return $this->resource->registration_address;
                }
            ),
            /** @var string|null $temporary_registration_address Адрес временной регистрации */
            'temporary_registration_address' => $this->when(
                property_exists($this->resource, 'temporary_registration_address'),
                function() {
                    return $this->resource->temporary_registration_address;
                }
            ),
            /** @var string|null $driver_license_series Серия водительского удостоверения */
            'driver_license_series' => $this->when(
                property_exists($this->resource, 'driver_license_series'),
                function() {
                    return $this->resource->driver_license_series;
                }
            ),
            /** @var string|null $driver_license_number Номер водительского удостоверения */
            'driver_license_number' => $this->when(
                property_exists($this->resource, 'driver_license_number'),
                function() {
                    return $this->resource->driver_license_number;
                }
            ),
            /** @var string|null $driver_license_issue_date Дата выдачи водительского удостоверения */
            'driver_license_issue_date' => $this->when(
                property_exists($this->resource, 'driver_license_issue_date'),
                function() {
                    return $this->resource->driver_license_issue_date;
                }
            ),
            /** @var string|null $driver_license_expiration_date Дата окончания водительского удостоверения */
            'driver_license_expiration_date' => $this->when(
                property_exists($this->resource, 'driver_license_expiration_date'),
                function() {
                    return $this->resource->driver_license_expiration_date;
                }
            ),
            /** @var string|null $driver_license_category Категория водительского удостоверения */
            'driver_license_category' => $this->when(
                property_exists($this->resource, 'driver_license_category'),
                function() {
                    return $this->resource->driver_license_category;
                }
            ),
            /** @var string|null $military_card Военный билет */
            'military_card' => $this->when(
                property_exists($this->resource, 'military_card'),
                function() {
                    return $this->resource->military_card;
                }
            ),
            /** @var string|null $hire_date Дата приема на работу */
            'hire_date' => $this->when(
                property_exists($this->resource, 'hire_date'),
                function() {
                    return $this->resource->hire_date;
                }
            ),
            /** @var string|null $dismissal_date Дата увольнения */
            'dismissal_date' => $this->when(
                property_exists($this->resource, 'dismissal_date'),
                function() {
                    return $this->resource->dismissal_date;
                }
            ),
            /** @var string|null $position Должность */
            'position' => $this->when(
                property_exists($this->resource, 'position'),
                function() {
                    return $this->resource->position;
                }
            ),
            /** @var string|null $salary Зарплата */
            'salary' => $this->when(
                property_exists($this->resource, 'salary'),
                function() {
                    return $this->resource->salary ? (string) $this->resource->salary : null;
                }
            ),
            /** @var string|null $labor_fund Фонд оплаты труда */
            'labor_fund' => $this->when(
                property_exists($this->resource, 'labor_fund'),
                function() {
                    return $this->resource->labor_fund;
                }
            ),
            /** @var string|null $planned_advance Планируемый аванс */
            'planned_advance' => $this->when(
                property_exists($this->resource, 'planned_advance'),
                function() {
                    return $this->resource->planned_advance ? (string) $this->resource->planned_advance : null;
                }
            ),
            /** @var array $work_schedule График работы */
            'work_schedule' => $this->when(
                property_exists($this->resource, 'work_schedule'),
                function() {
                    return $this->resource->work_schedule ? (array) $this->resource->work_schedule : [];
                }
            ),
            /** @var string|null $role_id Идентификатор роли */
            'role_id' => $this->when(
                property_exists($this->resource, 'role_id'),
                function() {
                    return $this->resource->role_id;
                }
            ),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
        ];
    }
}
