<?php

namespace App\Http\Resources\Excel;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExportExcelIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор экспорта */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $title Заголовок экспорта */
            'title' => $this->when(
                property_exists($this->resource, 'title'),
                function() {
                    return (string) $this->resource->title;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string|null $description Описание */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function() {
                    return $this->resource->description;
                }
            ),
            /** @var string $file Имя файла */
            'file' => $this->when(
                property_exists($this->resource, 'file'),
                function() {
                    return (string) $this->resource->file;
                }
            ),
            /** @var string $path Путь к файлу */
            'path' => $this->when(
                property_exists($this->resource, 'path'),
                function() {
                    return (string) $this->resource->path;
                }
            ),
            /** @var string $mime_type MIME тип файла */
            'mime_type' => $this->when(
                property_exists($this->resource, 'mime_type'),
                function() {
                    return (string) $this->resource->mime_type;
                }
            ),
            /** @var string|null $log Лог операции */
            'log' => $this->when(
                property_exists($this->resource, 'log'),
                function() {
                    return $this->resource->log;
                }
            ),
            /** @var int $status Статус экспорта */
            'status' => $this->when(
                property_exists($this->resource, 'status'),
                function() {
                    return (int) $this->resource->status;
                }
            ),
        ];
    }
}
