<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders;

use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderDeliveryTypeEnum;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders\Base\BaseOrdersSyncJob;
use DateTime;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Job для синхронизации DBS заказов из Wildberries
 */
class SyncDBSOrdersJob extends BaseOrdersSyncJob
{
    /**
     * Основной метод обработки DBS заказов
     * @throws Throwable
     */
    public function handle(): void
    {
        try {
            $this->preloadData();
            $this->processDbsOrders();
        } catch (Throwable $e) {
            Log::error('Error in SyncDBSOrdersJob: ' . $e->getMessage(), [
                'exception' => $e,
                'integration_id' => $this->integrationId,
                'cabinet_id' => $this->cabinetId
            ]);

            throw $e;
        }
    }

    /**
     * Обработка DBS заказов в streaming режиме для избежания проблем с памятью
     */
    private function processDbsOrders(): void
    {
        try {
            // Обрабатываем заказы в streaming режиме
            $this->processDbsOrdersStreaming();
        } catch (Exception $e) {
            Log::error('Error processing DBS orders: ' . $e->getMessage(), [
                'exception' => $e,
                'integration_id' => $this->integrationId,
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true)
            ]);
            throw $e;
        }
    }

    /**
     * Обработка DBS заказов в streaming режиме
     * @throws Exception
     */
    private function processDbsOrdersStreaming(): void
    {
        if ($this->dateFrom && $this->dateTo) {
            $dateFrom = new DateTime($this->dateFrom);
            $dateTo = new DateTime($this->dateTo);

            $this->fetchAndProcessOrdersWithPagination($dateFrom, $dateTo);
        } else {
            // Обрабатываем новые заказы
            $this->processNewDbsOrders();

            // Обрабатываем заказы за период
            $dateFrom = new DateTime('-' . self::DEFAULT_DAYS_PERIOD . ' days');
            $dateTo = new DateTime();

            $this->fetchAndProcessOrdersWithPagination($dateFrom, $dateTo);
        }
    }

    /**
     * Обработка новых DBS заказов
     */
    private function processNewDbsOrders(): void
    {
        $newOrders = $this->fetchOrdersWithRateLimit(
            fn () => $this->api->Marketplace()->DBS()->getNewOrders(),
            'marketplace'
        );

        if (!empty($newOrders)) {
            $this->processOrdersBatch($newOrders, OrderDeliveryTypeEnum::DBS->value);
            unset($newOrders);
        }
    }

    /**
     * Получение и обработка заказов с пагинацией в streaming режиме для DBS
     *
     * @param DateTime $dateFrom
     * @param DateTime $dateTo
     * @throws Exception
     */
    private function fetchAndProcessOrdersWithPagination(DateTime $dateFrom, DateTime $dateTo): void
    {
        $next = 0;
        $processedCount = 0;

        do {
            // Применяем рейт-лимитер перед каждым запросом
            $response = $this->fetchOrdersWithRateLimit(
                fn () => $this->api->Marketplace()->DBS()->getOrders(self::API_LIMIT, $next, $dateFrom, $dateTo),
                'marketplace'
            );

            if (empty($response)) {
                break;
            }

            // Проверяем, есть ли пагинация в ответе
            if (isset($response['orders'], $response['next'])) {
                // Ответ с пагинацией
                $orders = $response['orders'];
                $next = $response['next'];
            } else {
                // Обратная совместимость - ответ без пагинации
                $orders = $response;
                $next = null;
            }

            if (!empty($orders)) {
                // Обрабатываем этот батч заказов немедленно
                $this->processOrdersBatch($orders, OrderDeliveryTypeEnum::DBS->value);
                $processedCount += count($orders);

                // Очищаем заказы из памяти
                unset($orders);
            }

            // Если next равен 0 или null, значит данных больше нет
        } while ($next !== null && $next > 0);
    }

    /**
     * Получить имя таблицы заказов для DBS
     */
    protected function getOrdersTableName(): string
    {
        return 'wildberries_dbs_orders';
    }

    /**
     * Получить имя таблицы товаров заказов для DBS
     */
    protected function getOrderItemsTableName(): string
    {
        return 'wildberries_dbs_order_items';
    }

    /**
     * Получить имя таблицы информации о доставке для DBS
     */
    protected function getOrderDeliveryInfoTableName(): string
    {
        return 'wildberries_dbs_order_delivery_infos';
    }

    /**
     * Получить тип заказа для DBS
     */
    protected function getOrderType(): string
    {
        return OrderDeliveryTypeEnum::DBS->value;
    }

    protected function preloadOrders(): void
    {
        $this->preloadOrdersFromTable($this->getOrdersTableName());
    }
}
