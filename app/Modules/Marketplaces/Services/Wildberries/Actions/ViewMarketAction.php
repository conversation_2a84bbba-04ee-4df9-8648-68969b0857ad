<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions;

use Illuminate\Support\Facades\DB;

readonly class ViewMarketAction
{
    /**
     * @throws \JsonException
     */
    public function run(string $id): ?object
    {
        $result = DB::table('wildberries_integrations as oi')
            ->join('wildberries_order_settings as oos', 'oos.integration_id', '=', 'oi.id')
            ->join('wildberries_price_settings as ops', 'ops.integration_id', '=', 'oi.id')
            ->join('wildberries_report_settings as ors', 'ors.integration_id', '=', 'oi.id')
            ->select([
                'oi.*',

                DB::raw('COALESCE(
                    jsonb_build_object(
                        \'numbering_type\', oos.numbering_type,
                        \'add_prefix_to_orders\', oos.add_prefix_to_orders,
                        \'order_prefix\', oos.order_prefix,
                        \'use_common_agreement\', oos.use_common_agreement,
                        \'sync_statuses\', oos.sync_statuses,
                        \'reserve_from_inventory\', oos.reserve_from_inventory,
                        \'send_mark_codes\', oos.send_mark_codes,
                        \'print_label_size\', oos.print_label_size,
                        \'auto_sync\', oos.auto_sync,
                        \'sync_status\', oos.sync_status
                    ),
                    \'{}\'::jsonb
                ) AS order_sync'),

                DB::raw('COALESCE(
                    jsonb_build_object(
                        \'price_id\', ops.price_id,
                        \'discount_price_id\', ops.discount_price_id,
                        \'auto_sync\', ops.auto_sync,
                        \'sync_status\', ops.sync_status
                    ),
                    \'{}\'::jsonb
                ) AS price_sync'),

                DB::raw('COALESCE(
                    jsonb_build_object(
                        \'auto_sync\', ors.auto_sync,
                        \'sync_status\', ors.sync_status
                    ),
                    \'{}\'::jsonb
                ) AS report_sync'),

            ])

            ->where('oi.id', $id)
            ->first();

        if ($result) {
            $result->token = decrypt($result->token);
            $result->order_sync = json_decode($result->order_sync, true, 512, JSON_THROW_ON_ERROR);
            $result->price_sync = json_decode($result->price_sync, true, 512, JSON_THROW_ON_ERROR);
            $result->report_sync = json_decode($result->report_sync, true, 512, JSON_THROW_ON_ERROR);
        }

        return $result;
    }
}
