<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\Handlers;

use App\Contracts\Repositories\OzonOrderItemsRepositoryContract;
use App\Enums\Api\Internal\Ozon\IsPremiumEnum;
use App\Enums\Api\Internal\Ozon\PostingFbsListStatusesEnum;
use App\Enums\Api\Internal\Ozon\YesNoEnum;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class OzonApiOrderItemsChunksHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        private readonly OzonOrderItemsRepositoryContract $ozonOrderItemsRepositoryContract,
    ) {
    }

    public function run(Collection $soldAmount, int $limit, Collection $orderIds): void
    {
        $soldAmount->chunk($limit)->each(fn ($chunk) => $this->processChunk($chunk, $orderIds));
    }

    private function processChunk(Collection $chunk, Collection $orderIds): void
    {
        $orderItems = $chunk->flatMap(fn ($item) => $this->mapOrderItems($item, $orderIds))->toArray();
        $this->saveOrderItems($orderItems);
    }

    private function mapOrderItems(array $item, Collection $orderIds): Collection
    {
        return collect($item['products'])->map(fn ($product) => $this->formatOrderItem($item, $product, $orderIds));
    }

    private function formatOrderItem(array $item, array $product, Collection $orderIds): array
    {
        $innerOrderNumber = $this->generateInnerOrderNumber($item, $product);
        return [
            'id'                        => $this->generateUuid(),
            'ozon_order_id'             => $orderIds[$innerOrderNumber] ?? null,
            'posting_number'            => $item['posting_number'] ?? null,
            'inner_posting_number'      => $innerOrderNumber,
            'in_process_at'             => $this->formatDate($item['in_process_at'] ?? null),
            'shipment_date'             => $this->formatDate($item['shipment_date'] ?? null),
            'status'                    => PostingFbsListStatusesEnum::getTranslation($item['status'] ?? ''),
            'delivering_date'           => $this->formatDate($item['delivering_date'] ?? null),
            'delivery_date_end'         => $this->formatDate($item['analytics_data']['delivery_date_end'] ?? null),
            'amount'                    => $this->calculateAmount($product),
            'currency_code'             => $product['currency_code'] ?? null,
            'name'                      => $product['name'] ?? null,
            'sku'                       => $product['sku'] ?? null,
            'offer_id'                  => $product['offer_id'] ?? null,
            'products_price'            => $this->convertToKopecks($product['price'] ?? null),
            'currency_code_products'    => $product['currency_code'] ?? null,
            'quantity'                  => (int)($product['quantity'] ?? 0),
            'old_price'                 => $this->convertToKopecks($item['financial_data']['products'][0]['old_price'] ?? null),
            'total_discount_percent'    => $this->parseDiscountPercent($item['financial_data']['products'][0]['total_discount_percent'] ?? null),
            'total_discount_value'      => $this->calculateTotalDiscount($item, $product),
            'actions'                   => $this->encodeJson($item['financial_data']['products'][0]['actions'] ?? null),
            'upper_barcode'             => $item['barcodes']['upper_barcode'] ?? null,
            'lower_barcode'             => $item['barcodes']['lower_barcode'] ?? null,
            'cluster_from'              => $item['financial_data']['cluster_from'] ?? null,
            'cluster_to'                => $item['financial_data']['cluster_to'] ?? null,
            'region'                    => $item['analytics_data']['region'] ?? null,
            'city'                      => $item['analytics_data']['city'] ?? null,
            'delivery_type'             => $item['analytics_data']['delivery_type'] ?? null,
            'is_premium'                => IsPremiumEnum::getStatus($item['analytics_data']['is_premium'] ?? null),
            'payment_type_group_name'   => $item['analytics_data']['payment_type_group_name'] ?? null,
            'is_legal'                  => YesNoEnum::getStatus($item['analytics_data']['is_legal'] ?? null),
            'tpl_provider'              => $item['delivery_method']['tpl_provider'] ?? null,
            'delivery_method_name'      => $item['delivery_method']['name'] ?? null,
            'created_at'                => now(),
            'updated_at'                => now(),
        ];
    }

    private function generateInnerOrderNumber(array $item, array $product): ?string
    {
        return isset($product['sku']) ? $item['order_number'] . '--' . $item['posting_number'] . '___' . $product['sku'] : null;
    }

    private function formatDate(?string $date): ?string
    {
        return $date ? Carbon::parse($date)->format('Y-m-d H:i:s') : null;
    }

    private function convertToKopecks(?int $price): ?int
    {
        return isset($price) ? $this->rublesInKopeck($price) : null;
    }

    private function calculateAmount(array $product): ?int
    {
        return isset($product['price'], $product['quantity']) ? $this->rublesInKopeck((int)$product['price']) * (int)$product['quantity'] : null;
    }

    private function calculateTotalDiscount(array $item, array $product): ?int
    {
        return isset($item['financial_data']['products'][0]['total_discount_value']) ? $this->rublesInKopeck((int)$item['financial_data']['products'][0]['total_discount_value'] * (int)$product['quantity']) : null;
    }

    private function parseDiscountPercent(?string $discount): ?float
    {
        return isset($discount) ? (float)rtrim($discount, '%') : null;
    }

    private function encodeJson(?array $data): ?string
    {
        return isset($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : null;
    }

    private function saveOrderItems(array $orderItems): void
    {
        $this->ozonOrderItemsRepositoryContract->upsert($orderItems);
    }
}
