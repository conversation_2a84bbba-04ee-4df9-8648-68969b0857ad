<?php

namespace App\Services\Api\Internal\Goods\Products\ProductsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ProductAccountingFeaturesRepositoryContract;
use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use App\Contracts\Repositories\ProductPricesRepositoryContract;
use App\Contracts\Repositories\ProductsRepositoryContract;
use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use App\Enums\Api\Internal\ProductThresholdsEnum;
use App\Exceptions\InvalidUuidException;
use App\Helpers\DiffId;
use App\Helpers\SetIdHandler;
use App\Services\Api\Internal\Goods\Products\ProductsService\DTO\ProductDto;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

// use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;

class ProductUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ProductsRepositoryContract $productRepository,
        private readonly ProductPricesRepositoryContract $productPriceRepository,
        private readonly ProductAccountingFeaturesRepositoryContract $productAccountingFeaturesRepositoryContract,
        private readonly ProductThresholdsRepositoryContract $productThresholdsRepositoryContract,
        // private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepositoryContract,
        private readonly ProductAttributeRepositoryContract $productAttributeRepositoryContract,
    ) {
    }

    /**
     * @throws InvalidUuidException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof ProductDto) {
            $dto1 = $dto;
            $resourceId = $dto1->resourceId;
            if (!$this->validateUuid($dto1->resourceId)) {
                throw new InvalidUuidException();
            }

            $this->productRepository->update(
                $dto1->resourceId,
                $dto1->toUpdateArray(),
            );


            /*if (!empty($this->dto->images)) {
                $this->updateFiles(
                    $this->dto->employeeId,
                    $this->dto->images,
                    'images', // disc s3 images или docs
                    'image' // type image или file и др.
                );
            }

            if (!empty($this->dto->files)) {
                $this->updateFiles(
                    $this->dto->employeeId,
                    $this->dto->files,
                    'docs', // disc s3 images или docs
                    'file' // type image или file и др.
                );
            }*/

            $getAllAttribute = $this->productAttributeRepositoryContract->getWhereProductId($resourceId);
            $getAllPrice = $this->productPriceRepository->getWhereProductId($resourceId);
            $getFirstForProductAccountingFeatures = $this->productAccountingFeaturesRepositoryContract->getFirstForProduct(
                $resourceId
            );
            // $getAllEgaisCode = $this->productEgaisCodeRepositoryContract->getWhereProductId($this->resourceId);
            $getAllThresholds = $this->productThresholdsRepositoryContract->getWhereProductId($resourceId);

            if (!$getAllThresholds->isEmpty() || !empty($dto->toInsertArrayProductThresholds()['thresholds'])) {

                $thresholds = $dto->toInsertArrayProductThresholds()['thresholds'];

                if ($thresholds['type'] == ProductThresholdsEnum::SET_FOR_EACH_WAREHOUSE->value) {

                    $oldProductThresholdsIds = $this->productThresholdsRepositoryContract->oldProductThresholdIdsForProducts(
                        $resourceId
                    );

                    $setIdDataThresholds = SetIdHandler::setIdData(
                        $thresholds,
                        [
                            'product_id' => $resourceId,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ],
                        'warehouses'
                    );

                    $toDeleteOldThresholds  = DiffId::toDeleteOldId($oldProductThresholdsIds, $setIdDataThresholds);

                    $this->productThresholdsRepositoryContract->deleteArray($toDeleteOldThresholds);

                    $this->productThresholdsRepositoryContract->upsert($setIdDataThresholds);

                }

            }


            // TODO временно отключены
            // if (!$getAllEgaisCode->isEmpty() || !empty($dto->toInsertArrayProductEgaisCode()['egais_code'])) {

            //     $oldProductEgaisCodeIds = $this->productEgaisCodeRepositoryContract->oldProductEgaisCodeIdsForProducts($this->resourceId);

            //     $setIdProductEgaisCode = SetIdHandler::setIdData(
            //                 $dto->toInsertArrayProductEgaisCode(),
            //                 [
            //                     'product_id' => $this->resourceId,
            //                     'created_at' => now(),
            //                     'updated_at' => now(),
            //                 ],
            //                 'egais_code');

            //     $toDeleteOldProductEgaisCode  = DiffId::toDeleteOldId($oldProductEgaisCodeIds, $setIdProductEgaisCode);

            //     $this->productEgaisCodeRepositoryContract->deleteArray($toDeleteOldProductEgaisCode);

            //     $this->productEgaisCodeRepositoryContract->upsert($setIdProductEgaisCode);

            // }


            if (!empty($getFirstForProductAccountingFeatures)) {

                $this->productAccountingFeaturesRepositoryContract->update($getFirstForProductAccountingFeatures->id, $dto->toInsertArrayProductAccountingFeatures(
                    $resourceId
                ));

            } else {

                $productAccountingFeature = $dto->toInsertArrayProductAccountingFeatures($resourceId);

                $productAccountingFeature['id'] = $this->generateUuid();

                $this->productAccountingFeaturesRepositoryContract->insert($productAccountingFeature);
            }


            if (!$getAllPrice->isEmpty() || !empty($dto->toInsertArrayProductPrice()['sale_price'])) {

                $oldProductPricesIds = $this->productPriceRepository->oldProductPricesIdsForProducts($resourceId);

                $productPrices = SetIdHandler::setIdData(
                    $dto->toInsertArrayProductPrice(),
                    [
                        'product_id' => $resourceId,
                        // 'cabinet_id' => $dto->cabinetId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ],
                    'sale_price'
                );

                $toDeleteOldProductPrices  = DiffId::toDeleteOldId($oldProductPricesIds, $productPrices);

                $this->productPriceRepository->deleteArray($toDeleteOldProductPrices);

                $this->productPriceRepository->upsert($productPrices);

            }


            if (!$getAllAttribute->isEmpty() || !empty($dto->toInsertArrayProductAttribute()['attributes'])) {

                $oldProductAttributesIds = $this->productAttributeRepositoryContract->oldProductAttributesIdsForProducts(
                    $resourceId
                );

                $attributes = SetIdHandler::setIdData(
                    $dto->toInsertArrayProductAttribute(),
                    [
                        'product_id' => $resourceId,
                    ],
                    'attributes'
                );

                $toDeleteOldProductAttributes  = DiffId::toDeleteOldId($oldProductAttributesIds, $attributes);

                $this->productAttributeRepositoryContract->deleteArray($toDeleteOldProductAttributes);

                $this->productAttributeRepositoryContract->upsert($attributes);

            }

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');
    }
}
