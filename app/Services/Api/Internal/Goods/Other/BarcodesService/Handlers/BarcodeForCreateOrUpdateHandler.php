<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Enums\Api\Internal\BarcodeEnum;
use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use App\Traits\HasBarcodes;
use App\Traits\HasOrderedUuid;
use Exception;
use RuntimeException;

class BarcodeForCreateOrUpdateHandler
{
    use HasOrderedUuid;
    use HasBarcodes;

    public function __construct(
        private readonly BarcodesRepositoryContract $repository
    ) {
    }

    public function run(BarcodeDTO $dto): array
    {

        $errors = [];

        $barcode = $dto->resourceId !== null ? $this->repository->show($dto->resourceId) : null;

        $code = $dto->barcodes['value'] ?? null;
        $type = $barcode?->type ?? $dto->barcodes['type'];

        $resultBarcode = [
            'type' => $type,
            'cabinet_id' => $barcode?->cabinet_id ?? $dto->cabinetId,
            'barcodable_id' => $barcode?->barcodable_id ?? $dto->barcodableId,
            'barcodable_type' => $barcode?->barcodable_type ?? $dto->barcodableType,
        ];

        $dto->barcodes['is_generated'] = $barcode?->is_generated ?? false;

        // Если значение баркода не изменилось, возвращаем существующие данные
        // Но только если баркод существует и код не null (избегаем случая null == null при создании)
        if ($barcode !== null && $code !== null && $barcode->value == $code) {
            $resultBarcode['value'] = $code;
            $resultBarcode['is_generated'] = $barcode->is_generated ?? false;
            return $resultBarcode;
        }

        if ($code !== null) {
            switch ($type) {
                case BarcodeEnum::EAN8->value:
                case BarcodeEnum::EAN13->value:
                case BarcodeEnum::GTIN->value:
                case BarcodeEnum::UPC->value:
                    $response = $this->validateGtinOrUpc($code, $type);
                    if (isset($response['errors'])) {
                        $errors["barcodes.value"] = [$response['message']];
                        break;
                    }

                    $existsInDb = $this->repository->getExistsInDb($type, $resultBarcode['cabinet_id'], $code);

                    // Если баркод существует, но это тот же самый баркод, который мы обновляем - это нормально
                    if ($existsInDb && $barcode && $barcode->value !== $code) {
                        $errors["barcodes.value"] = "Штрихкод уже существует";
                        break;
                    }

                    $resultBarcode['value'] = $code;
                    return $resultBarcode;

                case BarcodeEnum::CODE128->value:
                    $this->validateCODE128($code, $type);

                    $existsInDb = $this->repository->getExistsInDb($type, $resultBarcode['cabinet_id'], $code);

                    // Если баркод существует, но это тот же самый баркод, который мы обновляем - это нормально
                    if ($existsInDb && $barcode && $barcode->value !== $code) {
                        $errors["barcodes.value"] = "Штрихкод CODE128 уже существует";
                        break;
                    }

                    $resultBarcode['value'] = $code;
                    return $resultBarcode;
            }

        } else {
            $generatedCodes = [
                BarcodeEnum::EAN8->value => [],
                BarcodeEnum::EAN13->value => [],
                BarcodeEnum::GTIN->value => [],
                BarcodeEnum::UPC->value => [],
            ];

            $defaultStartValues = [
                BarcodeEnum::EAN8->value => 20000004,
                BarcodeEnum::EAN13->value => 2000000000008,
                BarcodeEnum::GTIN->value => 20000004,
                BarcodeEnum::UPC->value => 200000000004,
            ];

            if ($type == BarcodeEnum::CODE128->value) {
                \Log::info('CODE128 cannot be empty, throwing error');
                $errors["barcodes.value"] = "Штрихкод CODE128 не может быть пустым";
                throw new RuntimeException(json_encode($errors, JSON_UNESCAPED_UNICODE));
            }

            \Log::info('Starting barcode generation for type: ' . $type);

            $result = $this->repository->getMaxBarcode($type, $resultBarcode['cabinet_id'], strlen((string)$defaultStartValues[$type]));
            $result = $result ?? $defaultStartValues[$type];

            $generatedCodes[$type][] = $result;
            $maxBarcode = max($generatedCodes[$type]);

            do {
                $newGenerateBarcode = $this->codeGenerateEanOrGtin($maxBarcode, strlen((string)$defaultStartValues[$type]));

                // Проверяем, что метод вернул число, а не массив с ошибкой
                if (is_array($newGenerateBarcode)) {
                    $errors["barcodes.value"] = $newGenerateBarcode['message'] ?? "Ошибка генерации баркода";
                    throw new RuntimeException(json_encode($errors, JSON_UNESCAPED_UNICODE));
                }

                $existsInDb = $this->repository->getExistsInDb($type, $resultBarcode['cabinet_id'], $newGenerateBarcode);

                if ($existsInDb) {
                    $maxBarcode = $newGenerateBarcode;
                }
            } while ($existsInDb);

            $resultBarcode['value'] = $newGenerateBarcode;
            $resultBarcode['is_generated'] = true;

            \Log::info('Generated barcode value: ' . $newGenerateBarcode);
        }

        if ($errors) {
            throw new Exception(json_encode($errors, JSON_UNESCAPED_UNICODE));
        }

        return $resultBarcode;

    }

}
