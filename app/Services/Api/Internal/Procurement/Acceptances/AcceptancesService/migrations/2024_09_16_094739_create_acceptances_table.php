<?php

namespace App\Services\Api\Internal\Procurement\AcceptancesService\migrations;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acceptances', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->softDeletes();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->boolean('is_common')->default(false);

            $table->string('number')->nullable();
            $table->dateTime('date_from');

            $table->foreignUuid('status_id')->nullable()->constrained()->nullOnDelete();
            $table->boolean('held')->default(true); //Проведено

            $table->foreignUuid('legal_entity_id')->constrained(); // юрлицо
            $table->foreignUuid('contractor_id')->constrained(); // конрагент поставщик

            $table->foreignUuid('warehouse_id')->constrained(); // склад

            $table->string('incoming_number')->nullable(); // входящий номер
            $table->date('incoming_date')->nullable();     // входящий дата

            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');
            $table->string('currency_value')->default('1');

            $table->text('comment')->nullable();
            $table->boolean('price_includes_vat')->default(true);
            $table->boolean('has_vat')->default(true); // Есть НДС

            $table->string('overhead_cost')->default(0);    // накладные расходы

            $table->string('total_price')->default(0);      // общая цена
        });

        Schema::create('acceptance_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('acceptance_id')->references('id')->on('acceptances')->cascadeOnDelete();
            $table->foreignUuid('product_id')->references('id')->on('products');
            $table->integer('quantity');                    // количество
            $table->string('price');                    // сумма
            $table->foreignUuid('vat_rate_id')->nullable()->references('id')->on('vat_rates'); // НДС
            $table->string('discount')->default(0);          // скидка
            $table->string('total_price')->default(0);  // общая сумма

            $table->foreignUuid('country_id')->nullable()->references('id')->on('countries'); // Страна
            $table->string('gtd_number')->nullable();       // ГТД номер
        });

        //TODO Связанные документы
        /*Schema::create('vendor_order_documents', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('order_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('document_id')->constrained()->cascadeOnDelete();
        });*/
        //TODO Задачи
        /*Schema::create('vendor_order_tasks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('order_id')->constrained()->cascadeOnDelete();
            $table->text('description')->nullable();
            $table->boolean('is_done')->default(false);
            $table->foreignUuid('employee_id')->constrained();
            $table->date('deadline')->nullable();
            $table->string('type')->nullable();

            $table->foreignUuid('contractor_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('document_id')->nullable()->constrained()->nullOnDelete();
        });

        Schema::create('vendor_order_task_comments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('vendor_order_task_id')->constrained()->cascadeOnDelete();
            $table->text('text');
            $table->foreignUuid('employee_id')->constrained();
        });*/
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acceptance_items');
        Schema::dropIfExists('acceptances');
        Schema::dropIfExists('acceptance_statuses');
    }
};
