<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\VendorOrderItemsRepositoryContract;
use App\Contracts\Repositories\VendorOrdersRepositoryContract;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\Procurement\VendorOrders\Traits\VendorOrderTotalCalculator;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO\VendorOrderItemDTO;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\VendorOrderItemVatCalculatorService;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use http\Exception\InvalidArgumentException;

class VendorOrderItemUpdateHandler
{
    use HasOrderedUuid;
    use PrecisionCalculator;
    use VendorOrderTotalCalculator;

    public function __construct(
        private readonly VendorOrderItemsRepositoryContract $vendorOrderItemsRepository,
        private readonly VendorOrdersRepositoryContract $vendorOrdersRepository,
        private readonly VendorOrderItemVatCalculatorService $vatCalculatorService
    ) {
    }

    /**
     * @throws NotFoundException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof VendorOrderItemDTO) {
            throw new InvalidArgumentException();
        }

        $item = $this->vendorOrderItemsRepository->show($dto->resourceId);
        if (!$item) {
            throw new NotFoundException('Vendor order item not found in handler.');
        }

        // Получаем заказ для проверки настроек НДС
        $order = $this->vendorOrdersRepository->show($item->order_id);
        if (!$order) {
            throw new NotFoundException('Vendor order not found.');
        }

        // Автоматически назначаем ставку НДС "Без НДС" если has_vat = false и ставка не указана
        if (!$order->has_vat && !$dto->vatRateId) {
            $dto->vatRateId = $this->vatCalculatorService->getAutoVatRateId($order->cabinet_id);
        }

        $dto->priceInBase = $this->multiply($dto->priceInCurrency, $dto->currencyRateToBase);
        $dto->amountInBase = $this->multiply($dto->priceInBase, (string)$dto->quantity);

        // Используем новый расчет с НДС
        $dto->totalPrice = $this->vatCalculatorService->calculateItemTotal(
            $item->order_id,
            $dto->priceInCurrency,
            (string)$dto->quantity,
            $dto->discount ?? '0',
            $dto->vatRateId,
            $order->cabinet_id
        );

        $this->vendorOrderItemsRepository->update(
            $dto->resourceId,
            $dto->toUpdateArray(),
        );

        // Пересчитываем общую сумму с НДС
        $this->updateVendorOrderTotal($item->order_id);
    }
}
