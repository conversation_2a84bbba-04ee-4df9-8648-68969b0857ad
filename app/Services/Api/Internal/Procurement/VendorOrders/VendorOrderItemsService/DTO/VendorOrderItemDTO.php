<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Services\Api\Internal\Procurement\VendorOrders\Traits\VendorOrderTotalCalculator;
use App\Traits\HasOrderedUuid;
use App\Traits\SummaryPriceCalculator;

class VendorOrderItemDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;
    use SummaryPriceCalculator;
    use VendorOrderTotalCalculator;

    public string $totalPrice;
    public function __construct(
        public ?string $orderId,
        public ?string $productId,
        public ?int $quantity,
        public ?string $priceInCurrency,
        public ?string $currencyRateToBase,
        public ?string $currencyId,
        public ?string $discount = '0',
        public ?string $vatRateId = null,
        public ?string $resourceId = null,
        public ?string $priceInBase = null,
        public ?string $amountInBase = null,
    ) {
        $this->totalPrice = $this->calculateTotalPrice();
    }

    /**
     * Рассчитывает total_price с учетом настроек НДС в заказе поставщика
     */
    private function calculateTotalPrice(): string
    {
        if (!$this->orderId || !$this->priceInCurrency || !$this->quantity) {
            // Если нет необходимых данных, возвращаем 0
            return '0';
        }

        $vatInfo = $this->getVatInfo($this->orderId, $this->vatRateId);

        return $this->calculateVendorOrderItemTotal(
            $this->priceInCurrency,
            (string)$this->quantity,
            $this->discount ?? '0',
            $vatInfo['vat_rate'],
            $vatInfo['price_includes_vat'],
            $vatInfo['has_vat']
        );
    }
    public static function fromArray(array $data): self
    {
        $dto = new self(
            orderId: $data['order_id'] ?? null,
            productId: $data['product_id'] ?? null,
            quantity: $data['quantity'] ?? null,
            priceInCurrency: $data['price_in_currency'] ?? null,
            currencyRateToBase: $data['currency_rate_to_base'] ?? null,
            currencyId: $data['currency_id'] ?? null,
            discount: $data['discount'] ?? null,
            vatRateId: $data['vat_rate_id'] ?? null,
            resourceId: $data['id'] ?? null,
            priceInBase: $data['price_in_base'] ?? null,
            amountInBase: $data['amount_in_base'] ?? null,
        );

        // Пересчитываем total_price после создания, так как теперь у нас есть все данные
        $dto->totalPrice = $dto->calculateTotalPrice();

        return $dto;
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'order_id' => $this->orderId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vatRateId,
            'currency_id' => $this->currencyId,
            'price_in_currency' => $this->priceInCurrency,
            'currency_rate_to_base' => $this->currencyRateToBase,
            'price_in_base' => $this->priceInBase,
            'amount_in_base' => $this->amountInBase,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'vat_rate_id' => $this->vatRateId,
            'discount' => $this->discount,
            'currency_id' => $this->currencyId,
            'price_in_currency' => $this->priceInCurrency,
            'currency_rate_to_base' => $this->currencyRateToBase,
            'price_in_base' => $this->priceInBase,
            'amount_in_base' => $this->amountInBase,
        ];
    }
}
