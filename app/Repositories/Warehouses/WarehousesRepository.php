<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehousesRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Entities\WarehouseEntity;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\SoftDeletable;
use Carbon\Carbon;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehousesRepository implements WarehousesRepositoryContract
{
    use SoftDeletable;
    private const TABLE = 'warehouses';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
    ) {
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::WAREHOUSES->value, $query::$table);

        // Добавляем связи
        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['show_only']),
            function ($query) use ($filters) {
                if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                    return $query->where('archived_at', '!=', null);
                }

                if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                    return $query->whereNull('archived_at');
                }

                return $query;
            }
        );

        $query->when(
            isset($filters['is_common']['value']),
            fn ($query) => $query->where('is_common', $filters['is_common']['value'])
        );

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('name', 'ilike', '%' . $filters['search']['value'] . '%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        $result = match ($filter) {
            'name' => $query->where('name', 'ilike', '%' . $value . '%'),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            'address' => function ($query) use ($value) {
                $query->whereHas('address', function ($query) use ($value) {
                    $query->whereRaw(
                        "COALESCE(postcode, '') ||
                     ' ' ||
                     COALESCE(city, '') ||
                     ' ' ||
                     COALESCE(region, '')||
                     ' ' ||
                     COALESCE(house, '')||
                     ' ' ||
                     COALESCE(office, '')||
                     ' ' ||
                     COALESCE(other, '')||
                     ' ' ||
                     COALESCE(street, '') ILIKE ?",
                        ['%' . $value . '%']
                    );
                })->with('address');
            },
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        $result = match ($filter) {
            'name' => $query->whereNotNull('name'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            'address' => function ($query) {
                $query->whereHas('address', function ($query) {
                    $query->whereRaw(
                        "postcode IS NOT NULL OR city IS NOT NULL OR region IS NOT NULL OR house IS NOT NULL
                        OR office IS NOT NULL OR other IS NOT NULL OR street IS NOT NULL"
                    );
                })->with('address');
            },
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        $result = match ($filter) {
            'name' => $query->where('name', 'not like', '%' . $value . '%')
                ->orWhere('name', null),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            'address' => function ($query) use ($value) {
                $query->whereHas('address', function ($query) use ($value) {
                    $query->whereRaw(
                        "COALESCE(postcode, '') ||
                     ' ' ||
                     COALESCE(city, '') ||
                     ' ' ||
                     COALESCE(region, '')||
                     ' ' ||
                     COALESCE(house, '')||
                     ' ' ||
                     COALESCE(office, '')||
                     ' ' ||
                     COALESCE(other, '')||
                     ' ' ||
                     COALESCE(street, '') NOT ILIKE ?",
                        ['%' . $value . '%']
                    );
                })->with('address');
            },
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        $result = match ($filter) {
            'name' => $query->whereNull('name'),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            'address' => function ($query) {
                $query->whereHas('address', function ($query) {
                    $query->whereRaw(
                        "postcode IS NULL AND city IS NULL AND region IS NULL AND house IS NULL
                        AND office IS NULL AND other IS NULL AND street IS NULL"
                    );
                })->with('address');
            },
            default => null,
        };

        if ($result instanceof Closure) {
            $result($query);
        }
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->leftJoin('warehouse_addresses as wa', 'warehouses.address_id', '=', 'wa.id')
            ->leftJoin('warehouse_phones as wp', 'warehouses.phone_id', '=', 'wp.id')
            ->leftJoin('warehouse_order_schemes as wos', 'wos.warehouse_id', '=', 'warehouses.id')
            ->leftJoin('warehouse_structures as ws', 'warehouses.id', '=', 'ws.warehouse_id')
            ->leftJoin('warehouse_work_schedules as wws', 'warehouses.work_schedule_id', '=', 'wws.id')
            ->select([
                'warehouses.*',
                DB::raw('
                    COALESCE(
                        CASE
                            WHEN warehouses.phone_id IS NULL THEN \'{}\'
                            ELSE JSONB_BUILD_OBJECT(
                                \'id\', wp.id,
                                \'phone\', wp.phone,
                                \'comment\', wp.comment,
                                \'created_at\', wp.created_at,
                                \'updated_at\', wp.updated_at
                            )
                        END, \'{}\'
                    ) AS phone
                '),
                DB::raw('
                    COALESCE(
                        CASE
                            WHEN warehouses.address_id IS NULL THEN \'{}\'
                            ELSE JSONB_BUILD_OBJECT(
                                \'id\', wa.id,
                                \'country_id\', wa.country_id,
                                \'postcode\', wa.postcode,
                                \'region\', wa.region,
                                \'city\', wa.city,
                                \'street\', wa.street,
                                \'house\', wa.house,
                                \'office\', wa.office,
                                \'other\', wa.other,
                                \'comment\', wa.comment,
                                \'created_at\', wa.created_at,
                                \'updated_at\', wa.updated_at
                            )
                        END, \'{}\'
                    ) AS address
                '),
                DB::raw('
                    COALESCE(
                        JSON_BUILD_OBJECT(
                            \'on_coming_from\', wos.on_coming_from,
                            \'on_shipment_from\', wos.on_shipment_from,
                            \'control_operational_balances\', wos.control_operational_balances
                        ), \'{}\'
                    ) as order_scheme
                '),
                DB::raw('
                    COALESCE(
                        JSON_BUILD_OBJECT(
                            \'use_premises_from\', ws.use_premises_from,
                            \'cells\', ws.cells,
                            \'use_cells_from\', ws.use_cells_from
                        ), \'{}\'
                    ) AS structure
                '),
                DB::raw('coalesce(jsonb_agg(wws) filter (where wws.id is not null), \'[]\') AS work_shedule'),
            ])
            ->where('warehouses.id', $id)
            ->groupBy([
                'warehouses.id',
                'wp.id', 'wa.id',
                'ws.use_premises_from', 'ws.cells', 'ws.use_cells_from',
                'wos.on_coming_from', 'wos.on_shipment_from', 'wos.control_operational_balances'])
            ->first();
    }

    public function findById(string $resourceId): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $resourceId)
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    private function getEntity(): WarehouseEntity
    {
        return new WarehouseEntity();
    }

    public function updateWhereCabinetId(string $cabinetId, array $array): bool
    {
        return DB::table(self::TABLE)
            ->where('cabinet_id', $cabinetId)
            ->update($array) > 0;
    }
}
