<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;
use App\Entities\WarehouseCellGroupEntity;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseCellGroupsRepository implements WarehouseCellGroupsRepositoryContract
{
    private const TABLE = 'warehouse_cell_groups';

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('warehouse_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('address', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('description', 'ILIKE', '%'.$filters['search']['value'].'%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert(
                array_merge($data, ['created_at' => Carbon::now()])
            );
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function getByParentId(string $resourceId): Collection
    {
        return DB::table(self::TABLE)
            ->where('parent_id', $resourceId)
            ->get();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    private function getEntity(): WarehouseCellGroupEntity
    {
        return new WarehouseCellGroupEntity();
    }
}
