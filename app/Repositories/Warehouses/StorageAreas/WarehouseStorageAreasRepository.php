<?php

namespace App\Repositories\Warehouses\StorageAreas;

use App\Contracts\Repositories\WarehouseStorageAreasRepositoryContract;
use App\Entities\WarehouseStorageAreaEntity;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseStorageAreasRepository implements WarehouseStorageAreasRepositoryContract
{
    private const TABLE = 'warehouse_storage_areas';

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('warehouse_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('name', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('description', 'ILIKE', '%'.$filters['search']['value'].'%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert(
                array_merge($data, ['created_at' => Carbon::now()])
            );
    }

    public function show(string $id): ?object
    {
        return DB::table('warehouse_storage_areas as wsa')
            ->join('warehouses as w', 'wsa.warehouse_id', '=', 'w.id')
            ->leftJoin('warehouse_storage_area_cells as wsac', 'wsa.id', '=', 'wsac.storage_area_id')
            ->leftJoin('warehouse_cells as wc', 'wsac.cell_id', '=', 'wc.id')
            ->leftJoin('warehouse_storage_area_products as wsap', 'wsa.id', '=', 'wsap.storage_area_id')
            ->leftJoin('products as p', 'wsap.product_id', '=', 'p.id')
            ->where('wsa.id', $id)
            ->select([
                'wsa.*',
                'w.name as warehouse_name',
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', wc.id,
                                    'address', wc.address
                                )
                            ) filter (where wc.id is not null), '[]'
                        ) AS warehouse_cells
                    "),
                DB::raw("
                        coalesce(
                            jsonb_agg(
                                jsonb_build_object(
                                    'id', p.id,
                                    'title', p.title
                                )
                            ) filter (where p.id is not null), '[]'
                        ) AS warehouse_products
                    "),
            ])
            ->groupBy(['wsa.id', 'w.name'])
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    private function getEntity(): WarehouseStorageAreaEntity
    {
        return new WarehouseStorageAreaEntity();
    }
}
