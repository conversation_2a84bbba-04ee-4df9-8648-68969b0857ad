<?php

namespace App\Repositories\Warehouses\StorageAreas;

use App\Contracts\Repositories\WarehouseStorageAreaCellsRepositoryContract;
use Illuminate\Support\Facades\DB;

class WarehouseStorageAreaCellsRepository implements WarehouseStorageAreaCellsRepositoryContract
{
    public function insert(array $data): void
    {
        DB::table('warehouse_storage_area_cells')->insert(
            $data
        );
    }

    public function update(string $resourceId, array $data): void
    {
        DB::table('warehouse_storage_area_cells')
            ->where('cell_id', $resourceId)
            ->update($data);
    }

    public function upsertWhereCellIdIn(array $cellsId, array $data): void
    {
        //TODO а это точно работало?
        DB::table('warehouse_storage_area_cells')
            ->whereIn('cell_id', $cellsId)
            ->upsert(
                $data,
                ['cell_id'],
                ['storage_area_id']
            );
    }
}
