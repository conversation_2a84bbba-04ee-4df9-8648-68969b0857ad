<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseAddressesRepositoryContract;
use App\Entities\WarehouseAddressEntity;
use App\Traits\HasOrderedUuid;
use App\Traits\HasTimestamps;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class WarehouseAddressesRepository implements WarehouseAddressesRepositoryContract
{
    use HasOrderedUuid;
    use HasTimestamps;

    private const TABLE = 'warehouse_addresses';

    public function findOrCreate(string $cabinetId, array $data): string
    {
        $resource = DB::table(self::TABLE)
            ->where('cabinet_id', $cabinetId)
            ->where('country_id', $data['country_id'])
            ->when(isset($data['postcode']), fn (Builder $query) => $query->where('postcode', $data['postcode']))
            ->when(isset($data['region']), fn (Builder $query) => $query->where('postcode', $data['region']))
            ->when(isset($data['city']), fn (Builder $query) => $query->where('postcode', $data['city']))
            ->when(isset($data['street']), fn (Builder $query) => $query->where('postcode', $data['street']))
            ->when(isset($data['house']), fn (Builder $query) => $query->where('postcode', $data['house']))
            ->when(isset($data['office']), fn (Builder $query) => $query->where('postcode', $data['office']))
            ->when(isset($data['other']), fn (Builder $query) => $query->where('postcode', $data['other']))
            ->when(isset($data['comment']), fn (Builder $query) => $query->where('postcode', $data['comment']))
            ->first('id');

        if ($resource) {
            return $resource->id;
        }

        $id = $this->generateUuid();
        DB::table(self::TABLE)
            ->insert(
                [
                    'id' => $id,
                    'created_at' => Carbon::now(),
                    'cabinet_id' => $cabinetId,
                    'country_id' => $data['country_id'],
                    'postcode' => $data['postcode'] ?? null,
                    'region' => $data['region'] ?? null,
                    'city' => $data['city'] ?? null,
                    'street' => $data['street'] ?? null,
                    'house' => $data['house'] ?? null,
                    'office' => $data['office'] ?? null,
                    'other' => $data['other'] ?? null,
                    'comment' => $data['comment'] ?? null,
                ]
            );
        return $id;
    }

    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);

        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);


        $query->select($baseFields)
            ->where('cabinet_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('comment', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('postcode', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('region', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('city', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('street', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('house', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('office', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('other', 'ILIKE', '%'.$filters['search']['value'].'%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    private function getEntity(): WarehouseAddressEntity
    {
        return new WarehouseAddressEntity();
    }
}
