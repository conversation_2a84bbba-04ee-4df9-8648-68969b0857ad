<?php

namespace App\Repositories\Contractors\Detail;

use App\Contracts\Repositories\ContractorDetailAddressRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ContractorDetailAddressRepository implements ContractorDetailAddressRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'contractor_detail_address';

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return collect();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return 0;
    }

    public function delete(string $id): int
    {
        return 0;
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)->where('id', $id)->first();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['contractor_detail_id'],
                [
                    'postcode',
                    'country',
                    'region',
                    'city',
                    'street',
                    'house',
                    'office',
                    'other',
                    'comment',
                    'updated_at'
                ]
            );
    }
}
