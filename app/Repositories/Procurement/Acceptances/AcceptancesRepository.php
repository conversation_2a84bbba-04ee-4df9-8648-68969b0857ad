<?php

namespace App\Repositories\Procurement\Acceptances;

use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\AcceptanceEntity;
use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AcceptancesRepository implements AcceptanceRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;

    private const TABLE = 'acceptances';

    public function __construct(
        private readonly AuthorizationServiceContract $authService
    ) {
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::ACCEPTANCES->value, $query::getTable());

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        $query->when(
            isset($filters['is_common']['value']),
            fn ($query) => $query->where('is_common', $filters['is_common']['value'])
        );

        $query->when(
            isset($filters['is_held']['value']),
            fn ($query) => $query->where('held', $filters['is_held']['value'])
        );

        $query->when(
            isset($filters['search']['value']),
            function ($query) use ($filters) {
                $query->where('number', 'ilike', '%' . $filters['search']['value'] . '%')
                    ->orWhere('comment', 'ilike', '%' . $filters['search']['value'] . '%');
            }
        );

        $query->when(isset($filters['period']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['period']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('created_at', [$from, $to]);
        });

        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });

        $query->when(isset($filters['incoming_date']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['incoming_date']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['incoming_date']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('incoming_date', [$from, $to]);
        });


        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE . ' as a')
            ->leftJoin('statuses as s', 'a.status_id', '=', 's.id')
            ->join('contractors as c', 'a.contractor_id', '=', 'c.id')
            ->join('legal_entities as le', 'a.legal_entity_id', '=', 'le.id')
            ->join('warehouses as w', 'a.warehouse_id', '=', 'w.id')
            ->leftJoin('acceptance_items as ci', 'a.id', '=', 'ci.acceptance_id')
            ->leftJoin('file_relations as fr', 'a.id', '=', 'fr.related_id')
            ->leftJoin('files as f', 'fr.file_id', '=', 'f.id')
            ->select([
                'a.*',
                DB::raw(" json_build_object(
                        'id', c.id,
                        'title', c.title
                    ) as contractor"),
                DB::raw("CASE
                    WHEN s.id is not null THEN json_build_object(
                        'id', s.id,
                        'name', s.name
                    )
                    ELSE NULL
                END as status"),
                DB::raw("CASE
                    WHEN w.id is not null THEN json_build_object(
                        'id', w.id,
                        'name', w.name
                    )
                    ELSE NULL
                END as warehouse"),
                DB::raw("
                        coalesce(
                            jsonb_agg(f) filter (where f.id is not null), '[]'
                        ) AS files
                    "),
            ])
            ->where('a.id', $id)
            ->groupBy(['a.id', 's.id', 'c.id', 'w.id'])
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge(
                $data,
                ['updated_at' => Carbon::now()]
            ));
    }

    public function getFirstShipmentItemForAllProducts(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE . ' as a')
            ->join('warehouse_items as wi', 'a.id', '=', 'wi.acceptance_id')
            ->join('shipment_warehouse_items as swi', 'wi.id', '=', 'swi.warehouse_item_id')
            ->where('a.id', $resourceId)
            ->where('a.held', true)
            ->where('a.deleted_at', null)
            ->whereIn('swi.shipment_date', function ($query) use ($resourceId) {
                $query->select(DB::raw('MIN(swi2.shipment_date)'))
                    ->from('acceptances as a2')
                    ->join('warehouse_items as wi2', 'a2.id', '=', 'wi2.acceptance_id')
                    ->join('shipment_warehouse_items as swi2', 'wi2.id', '=', 'swi2.warehouse_item_id')
                    ->where('a2.id', $resourceId)
                    ->groupBy('wi2.product_id');
            })
            ->select('swi.shipment_item_id')
            ->orderBy('swi.shipment_date', 'asc')
            ->get();
    }

    public function getFirst(string $resourceId): ?object
    {
        return DB::table('acceptances')
            ->where('id', $resourceId)
            ->first();
    }



    public function getFirstShipmentItemForAllProductsBulk(array $acceptanceIds): Collection
    {
        return DB::table(self::TABLE . ' as a')
            ->join('warehouse_items as wi', 'a.id', '=', 'wi.acceptance_id')
            ->join('shipment_warehouse_items as swi', 'wi.id', '=', 'swi.warehouse_item_id')
            ->whereIn('a.id', $acceptanceIds)
            ->where('a.deleted_at', null)
            ->where('a.held', true)
            ->whereIn('swi.shipment_date', function ($query) use ($acceptanceIds) {
                $query->select(DB::raw('MIN(swi2.shipment_date)'))
                    ->from('acceptances as a2')
                    ->join('warehouse_items as wi2', 'a2.id', '=', 'wi2.acceptance_id')
                    ->join('shipment_warehouse_items as swi2', 'wi2.id', '=', 'swi2.warehouse_item_id')
                    ->whereIn('a2.id', $acceptanceIds)
                    ->groupBy('wi2.product_id');
            })
            ->select(
                'swi.shipment_item_id',
                'wi.product_id',
                'a.cabinet_id'
            )
            ->orderBy('swi.shipment_date', 'asc')
            ->get();
    }

    public function whereIn(string $field, array $resourceIds): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn($field, $resourceIds)
            ->get();
    }

    public function getWhereInIds(array $ids): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->get();
    }

    public function getEntity(): AcceptanceEntity
    {
        return new AcceptanceEntity();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'warehouses' => $query->whereNull('warehouse_id'),
            'incoming_number' => $query->whereNull('incoming_number'),
            'contractor_owners' => $query->whereHas('contractor', function ($query) {
                $query->whereNull('employee_id');
            })
                ->orWhere(fn () => $query->doesntHave('contractor')),
            'legal_entity' => $query->whereNull('legal_entity_id'),
            'employee_owners' => $query->whereNull('employee_id'),
            'contractors' => $query->whereNull('contractor_id'),
            'department_owners' => $query->whereNull('department_id'),
            'contractor_groups' => $query->whereHas('contractor', function ($query) {
                $query->whereHas('group', function ($query) {
                    $query->whereNull('group_id');
                })
                ->orWhere('group_id', null);
            }),
            'statuses' => $query->whereNull('status_id'),
            'products' => $query->whereHas('items', function ($query) {
                $query->whereNull('product_id');
            })
                ->orWhere(fn () => $query->doesntHave('items')),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'warehouses' => $query->whereNotNull('warehouse_id'),
            'incoming_number' => $query->whereNotNull('incoming_number'),
            'contractor_owners' => $query->whereHas('contractor', function ($query) {
                $query->whereNotNull('employee_id');
            }),
            'legal_entity' => $query->whereNotNull('legal_entity_id'),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'contractors' => $query->whereNotNull('contractor_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            'contractor_groups' => $query->whereHas('contractor', function ($query) {
                $query->whereHas('group', function ($query) {
                    $query->whereNotNull('group_id');
                });
            }),
            'statuses' => $query->whereNotNull('status_id'),
            'products' => $query->whereHas('items', function ($query) {
                $query->whereNotNull('product_id');
            }),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'warehouses' => $query->whereNotIn('warehouse_id', $value),
            'incoming_number' => $query->where('incoming_number', 'not like', '%' . $value . '%')
                ->orWhere('incoming_number', null),
            'contractor_owners' => $query->whereHas('contractor', function ($query) use ($value) {
                $query->whereNotIn('employee_id', $value);
            }),
            'legal_entity' => $query->whereNotIn('legal_entity_id', $value),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            'contractors' => $query->whereNotIn('contractor_id', $value),
            'contractor_groups' => $query->whereHas('contractor', function ($query) use ($value) {
                $query->whereHas('group', function ($query) use ($value) {
                    $query->whereNotIn('group_id', $value);
                });
            }),
            'statuses' => $query->where(function ($query) use ($value) {
                $query->whereNotIn('status_id', $value)
                    ->orWhere('status_id', null);
            }),
            'products' => $query->whereHas('items', function ($query) use ($value) {
                $query->whereNotIn('product_id', $value);
            }),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'warehouses' => $query->whereIn('warehouse_id', $value),
            'incoming_number' => $query->where('incoming_number', 'ilike', '%' . $value . '%'),
            'contractor_owners' => $query->whereHas('contractor', function ($query) use ($value) {
                $query->whereIn('employee_id', $value);
            }),
            'legal_entity' => $query->whereIn('legal_entity_id', $value),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            'contractors' => $query->whereIn('contractor_id', $value),
            'contractor_groups' => $query->whereHas('contractor', function ($query) use ($value) {
                $query->whereHas('group', function ($query) use ($value) {
                    $query->whereIn('group_id', $value);
                });
            }),
            'statuses' => $query->whereIn('status_id', $value),
            'products' => $query->whereHas('items', function ($query) use ($value) {
                $query->whereIn('product_id', $value);
            }),
            default => null,
        };
    }
}
