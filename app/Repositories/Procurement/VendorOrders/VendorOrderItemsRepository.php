<?php

namespace App\Repositories\Procurement\VendorOrders;

use App\Contracts\Repositories\VendorOrderItemsRepositoryContract;
use App\Entities\VendorOrderItemEntity;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class VendorOrderItemsRepository implements VendorOrderItemsRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'vendor_order_items';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge($data, ['updated_at' => Carbon::now()])
            );
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();
        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('order_id', $id);

        // Добавляем дополнительные вычисляемые поля через addSelect
        $query->addSelect([
            DB::raw('(
                SELECT COALESCE(SUM(ai.quantity), 0)
                FROM acceptance_items ai
                JOIN acceptances a ON ai.acceptance_id = a.id
                JOIN documents d_acceptance ON a.id = d_acceptance.documentable_id AND d_acceptance.documentable_type = \'acceptances\'
                JOIN documents d_vendor ON d_vendor.documentable_id = vendor_order_items.order_id AND d_vendor.documentable_type = \'vendor_orders\'
                WHERE ai.product_id = vendor_order_items.product_id
                  AND d_acceptance.tree_id = d_vendor.tree_id
                  AND a.held = true
                  AND a.deleted_at IS NULL
            ) as accepted_quantity'),

            DB::raw('(
                SELECT COALESCE(SUM(wi.quantity), 0)
                FROM warehouse_items wi
                JOIN vendor_orders vo_current ON vo_current.id = vendor_order_items.order_id
                WHERE wi.product_id = vendor_order_items.product_id
                  AND wi.warehouse_id = vo_current.warehouse_id
                  AND wi.received_at <= vo_current.date_from
                  AND wi.status != \'out_of_stock\'
            ) as available_quantity'),

            DB::raw('(
                SELECT COALESCE(SUM(wi.quantity) - COALESCE(SUM(swi.quantity), 0), 0)
                FROM warehouse_items wi
                JOIN vendor_orders vo_current ON vo_current.id = vendor_order_items.order_id
                LEFT JOIN shipment_warehouse_items swi ON wi.id = swi.warehouse_item_id
                LEFT JOIN shipment_items si ON swi.shipment_item_id = si.id
                LEFT JOIN shipments s ON si.shipment_id = s.id
                WHERE wi.product_id = vendor_order_items.product_id
                  AND wi.warehouse_id = vo_current.warehouse_id
                  AND wi.received_at <= vo_current.date_from
                  AND wi.status != \'out_of_stock\'
                  AND (s.date_from < vo_current.date_from OR s.date_from IS NULL)
            ) as recidual')
        ]);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->whereHas('product', function ($builder) use ($filters) {
                $builder->where('title', 'ILIKE', '%' . $filters['search']['value'] . '%');
            });
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function getWhereOrderInIds(array $ids): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('order_id', $ids)
            ->get();
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции заказа поставщика
     *
     * @param string $productId ID товара
     * @param string $warehouseId ID склада
     * @param string $dateFrom Дата заказа
     * @param string $cabinetId ID кабинета
     * @return object Объект с рассчитанными показателями
     */
    public function calculateMetricsForNewItem(string $productId, string $warehouseId, string $dateFrom, string $cabinetId): object
    {
        // Принятое количество
        $acceptedResult = DB::selectOne("
            SELECT COALESCE(SUM(ai.quantity), 0) as accepted_quantity
            FROM acceptance_items ai
            JOIN acceptances a ON ai.acceptance_id = a.id
            JOIN documents d_acceptance ON a.id = d_acceptance.documentable_id AND d_acceptance.documentable_type = 'acceptances'
            WHERE ai.product_id = ?
              AND a.warehouse_id = ?
              AND d_acceptance.tree_id IN (
                  SELECT tree_id FROM documents WHERE documentable_type = 'vendor_orders'
              )
              AND a.held = true
              AND a.deleted_at IS NULL
        ", [$productId, $warehouseId]);

        // Доступное количество на складе
        $availableResult = DB::selectOne("
            SELECT COALESCE(SUM(wi.quantity), 0) as available_quantity
            FROM warehouse_items wi
            WHERE wi.product_id = ?
              AND wi.warehouse_id = ?
              AND wi.received_at <= ?
              AND wi.status != 'out_of_stock'
        ", [$productId, $warehouseId, $dateFrom]);

        // Остаток
        $recidualResult = DB::selectOne("
            SELECT COALESCE(SUM(wi.quantity) - COALESCE(SUM(swi.quantity), 0), 0) as recidual
            FROM warehouse_items wi
            LEFT JOIN shipment_warehouse_items swi ON wi.id = swi.warehouse_item_id
            LEFT JOIN shipment_items si ON swi.shipment_item_id = si.id
            LEFT JOIN shipments s ON si.shipment_id = s.id
            WHERE wi.product_id = ?
              AND wi.warehouse_id = ?
              AND wi.received_at <= ?
              AND wi.status != 'out_of_stock'
              AND (s.date_from < ? OR s.date_from IS NULL)
        ", [$productId, $warehouseId, $dateFrom, $dateFrom]);

        return (object) [
            'accepted_quantity' => $acceptedResult->accepted_quantity ?? 0,
            'available_quantity' => $availableResult->available_quantity ?? 0,
            'recidual' => $recidualResult->recidual ?? 0
        ];
    }

    private function getEntity(): VendorOrderItemEntity
    {
        return new VendorOrderItemEntity();
    }
}
