<?php

namespace App\Repositories\References;

use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\BaseEntity;
use App\Entities\CabinetCurrencyEntity;
use App\Entities\EntityBuilder;
use App\Enums\Api\Internal\CurrencyTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CabinetCurrenciesRepository implements CabinetCurrenciesRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;
    use Archivable;

    protected const TABLE = 'cabinet_currencies';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
    ) {
    }
    private function joinGlobalCurrencies(Builder|BaseEntity|EntityBuilder $query): Builder|BaseEntity|EntityBuilder
    {
        $selectFields = [
            self::TABLE . '.*',
            DB::raw('
                CASE
                    WHEN ' . self::TABLE . '.type = ' . CurrencyTypeEnum::AUTO->value . ' THEN global_currencies.num_code
                    ELSE ' . self::TABLE . '.num_code
                END AS num_code
            '),
            DB::raw('
                CASE
                    WHEN ' . self::TABLE . '.type = ' . CurrencyTypeEnum::AUTO->value . ' THEN global_currencies.char_code
                    ELSE ' . self::TABLE . '.char_code
                END AS char_code
            '),
            DB::raw('
                CASE
                    WHEN ' . self::TABLE . '.type = ' . CurrencyTypeEnum::AUTO->value . ' THEN global_currencies.short_name
                    ELSE ' . self::TABLE . '.short_name
                END AS short_name
            '),
            DB::raw('
                CASE
                    WHEN ' . self::TABLE . '.type = ' . CurrencyTypeEnum::AUTO->value . ' THEN global_currencies.name
                    ELSE ' . self::TABLE . '.name
                END AS name
            '),
            DB::raw('
                CASE
                    WHEN ' . self::TABLE . '.type = ' . CurrencyTypeEnum::AUTO->value . ' THEN global_currencies.pluralization
                    ELSE ' . self::TABLE . '.pluralization
                END AS pluralization
            '),
            DB::raw('
                CASE
                    WHEN ' . self::TABLE . '.is_accouting = true THEN \'1\'
                    WHEN ' . self::TABLE . '.type = ' . CurrencyTypeEnum::AUTO->value . ' THEN global_currencies.value
                    ELSE ' . self::TABLE . '.value
                END AS value
            '),
            DB::raw('
                CASE
                    WHEN ' . self::TABLE . '.type = ' . CurrencyTypeEnum::AUTO->value . ' THEN global_currencies.external_id
                    ELSE ' . self::TABLE . '.external_id
                END AS external_id
            '),
        ];

        if ($query instanceof EntityBuilder) {
            // Для EntityBuilder используем его методы
            $query->leftJoin('global_currencies', self::TABLE . '.currency_id', '=', 'global_currencies.id');

            // Получаем текущий SQL запрос
            $currentSql = $query->toSql();

            // Проверяем, есть ли уже SELECT в запросе
            if (!str_contains(strtolower($currentSql), 'select')) {
                $query->addSelect($selectFields);
            }
        } else {
            // Для Builder и BaseEntity используем стандартные методы
            $query->leftJoin('global_currencies', self::TABLE . '.currency_id', '=', 'global_currencies.id')
                ->addSelect($selectFields);
        }

        return $query;
    }

    public function show(string $id): ?object
    {
        $query =  DB::table(self::TABLE)
            ->where(self::TABLE.'.id', $id);
        $query = $this->joinGlobalCurrencies($query);
        return $query->first();
    }
    public function update(string $id, array $data): int
    {
        $data = $this->setUpdatedAt($data);
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table('cabinet_currencies')
            ->insert($data);
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();

        [$baseFields, $relationFields] = $query->parseFields($fields);

        // Добавляем все необходимые поля из global_currencies
        $requiredFields = [
            'num_code', 'char_code', 'short_name', 'name',
            'pluralization', 'value', 'external_id'
        ];

        if (empty($baseFields) || in_array('*', $baseFields)) {
            $baseFields = array_merge($query::getFields(), $requiredFields);
        } else {
            $baseFields = array_unique(array_merge($baseFields, $requiredFields));
        }

        $query->select($baseFields)
            ->where('cabinet_id', $id)
            ->where('deleted_at', null);

        // ВАЖНО: JOIN должен происходить ДО применения фильтров
        $query = $this->joinGlobalCurrencies($query);

        $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::CURRENCIES->value, $query::getTable());

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        foreach ($filters as $filter => $value) {
            if (isset($value['condition'])) {
                match ($value['condition']) {
                    FilterConditionEnum::EMPTY->value => $this->handleEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_EMPTY->value => $this->handleNotEmptyCondition($query, $filter),
                    FilterConditionEnum::NOT_IN->value => $this->handleNotInCondition($query, $filter, $value['value']),
                    default => $this->handleInCondition($query, $filter, $value['value']),
                };
            }
        }

        // Поиск по вычисляемому полю short_name
        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $searchValue = $filters['search']['value'];
            $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.short_name
                    ELSE " . self::TABLE . ".short_name
                END ILIKE ?", ['%' . $searchValue . '%']);
        });

        $query->when(isset($filters['is_common']['value']), fn ($query) => $query->where('is_common', $filters['is_common']['value']));

        // Фильтр по вычисляемому полю short_name
        $query->when(
            isset($filters['short_name']['value']),
            function ($query) use ($filters) {
                $shortNameValue = $filters['short_name']['value'];
                $query->whereRaw("
                    CASE
                        WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.short_name
                        ELSE " . self::TABLE . ".short_name
                    END ILIKE ?", ['%' . $shortNameValue . '%']);
            }
        );

        // Фильтр по вычисляемому полю num_code
        $query->when(
            isset($filters['num_code']['value']),
            function ($query) use ($filters) {
                $numCodeValue = $filters['num_code']['value'];
                $query->whereRaw("
                    CASE
                        WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.num_code
                        ELSE " . self::TABLE . ".num_code
                    END ILIKE ?", ['%' . $numCodeValue . '%']);
            }
        );

        $query->when(
            isset($filters['show_only']['value']),
            function ($query) use ($filters) {
                if ($filters['show_only']['value'] === ShowOnlyEnum::ARCHIVED->value) {
                    return $query->where('archived_at', '!=', null);
                }

                if ($filters['show_only']['value'] === ShowOnlyEnum::COMMON->value) {
                    return $query->whereNull('archived_at');
                }

                return $query;
            }
        );

        $query->when(isset($filters['updated_at']['from']), function ($query) use ($filters) {
            $from = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['from'])?->format('Y-m-d H:i:s');
            $to = Carbon::createFromFormat('d.m.Y H:i', $filters['updated_at']['to'])?->format('Y-m-d H:i:s');

            $query->whereBetween('updated_at', [$from, $to]);
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    private function handleEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.name
                    ELSE " . self::TABLE . ".name
                END IS NULL"),
            'short_name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.short_name
                    ELSE " . self::TABLE . ".short_name
                END IS NULL"),
            'employee_owners' => $query->whereNull('employee_id'),
            'department_owners' => $query->whereNull('department_id'),
            default => null,
        };
    }

    private function handleNotEmptyCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter
    ): void {
        match ($filter) {
            'name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.name
                    ELSE " . self::TABLE . ".name
                END IS NOT NULL"),
            'short_name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.short_name
                    ELSE " . self::TABLE . ".short_name
                END IS NOT NULL"),
            'employee_owners' => $query->whereNotNull('employee_id'),
            'department_owners' => $query->whereNotNull('department_id'),
            default => null,
        };
    }

    private function handleNotInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.name
                    ELSE " . self::TABLE . ".name
                END NOT ILIKE ? OR
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.name
                    ELSE " . self::TABLE . ".name
                END IS NULL", ['%' . $value . '%']),
            'short_name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.short_name
                    ELSE " . self::TABLE . ".short_name
                END NOT ILIKE ? OR
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.short_name
                    ELSE " . self::TABLE . ".short_name
                END IS NULL", ['%' . $value . '%']),
            'employee_owners' => $query->whereNotIn('employee_id', $value),
            'department_owners' => $query->whereNotIn('department_id', $value),
            default => null,
        };
    }

    private function handleInCondition(
        EntityBuilder|BaseEntity|Builder $query,
        string $filter,
        mixed $value
    ): void {
        match ($filter) {
            'name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.name
                    ELSE " . self::TABLE . ".name
                END ILIKE ?", ['%' . $value . '%']),
            'short_name' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.short_name
                    ELSE " . self::TABLE . ".short_name
                END ILIKE ?", ['%' . $value . '%']),
            'num_code' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.num_code
                    ELSE " . self::TABLE . ".num_code
                END ILIKE ?", ['%' . $value . '%']),
            'char_code' => $query->whereRaw("
                CASE
                    WHEN " . self::TABLE . ".type = " . CurrencyTypeEnum::AUTO->value . " THEN global_currencies.char_code
                    ELSE " . self::TABLE . ".char_code
                END ILIKE ?", ['%' . $value . '%']),
            'employee_owners' => $query->whereIn('employee_id', $value),
            'department_owners' => $query->whereIn('department_id', $value),
            default => null,
        };
    }

    public function deleteWhereIn(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    public function deleteAccountingRecord(string $cabinetId): int
    {
        return DB::table(self::TABLE)
            ->where('cabinet_id', $cabinetId)
            ->where('is_accouting', true)
            ->delete();
    }

    private function getEntity(): CabinetCurrencyEntity
    {
        return new CabinetCurrencyEntity();
    }

    public function firstWhere(array $where): ?object
    {
        return DB::table(self::TABLE)
            ->where($where)
            ->first();
    }
}
