<?php

namespace App\Entities;

use BadMethodCallException;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;

abstract class BaseEntity
{
    public static string $table;
    public static array $fields = [];
    protected EntityBuilder $builder;

    public function __construct()
    {
        $this->builder = new EntityBuilder($this);
    }

    public function __call(string $name, array $arguments): EntityBuilder
    {
        if (method_exists($this->builder, $name)) {
            return $this->builder->$name(...$arguments);
        }

        throw new BadMethodCallException("Method {$name} not found in " . static::class);
    }

    public function orWhereNull(string|Closure $field): EntityBuilder
    {
        return $this->builder->orWhereNull($field);
    }
    public function whereIn(string $field, array|Collection $values): EntityBuilder
    {
        return $this->builder->whereIn($field, $values instanceof Collection ? $values->toArray() : $values);

    }
    public function whereNotIn(string $field, array|Collection|Closure $values): EntityBuilder
    {
        return $this->builder->whereNotIn($field, $values instanceof Collection ? $values->toArray() : $values);
    }

    public function hasMany(string $entityClass, string $localKey, string $foreignKey): RelationBuilder
    {
        return new RelationBuilder('hasMany', $entityClass, $localKey, $foreignKey, static::$table);
    }

    public function hasOne(string $entityClass, string $localKey, string $foreignKey): RelationBuilder
    {
        return new RelationBuilder('hasOne', $entityClass, $localKey, $foreignKey, static::$table);
    }

    public function manyToMany(string $entityClass, string $relatedPivotKey, string $foreignPivotKey, string $pivotTable): RelationBuilder
    {
        return new RelationBuilder(
            'manyToMany', // Type of relationship
            $entityClass, // Related entity class
            $relatedPivotKey, // Local key in the pivot table for the current model
            $foreignPivotKey, // Foreign key in the pivot table for the related model
            $pivotTable // Pivot table name
        );
    }

    /**
    * Получить имя первичного ключа.
    *
    * @return string
    */
    public function getKeyName()
    {
        return 'id';
    }

    public static function getTable(): string
    {
        return static::$table;
    }

    public static function getFields(): array
    {
        return static::$fields;
    }

    public static function applyFilters(Builder $query, array $filters): Builder
    {
        foreach ($filters as $field => $value) {
            if (is_array($value)) {
                if (isset($value['from'], $value['to'])) {
                    $query->whereBetween($field, [$value['from'], $value['to']]);
                } elseif (isset($value['in'])) {
                    $query->whereIn($field, $value['in']);
                }
            } else {
                $query->where($field, $value);
            }
        }

        return $query;
    }

    public function select(array $fields): EntityBuilder
    {
        return $this->builder->select($fields);
    }
    public function where(string|Closure $field, mixed $operator = null, mixed $value = null): EntityBuilder
    {
        return $this->builder->where($field, $operator, $value);
    }

    public function whereNull(string|Closure $field): EntityBuilder
    {
        return $this->builder->whereNull($field);
    }

    public function whereNotNull(string|Closure $field): EntityBuilder
    {
        return $this->builder->whereNotNull($field);
    }

    public function toSql(): string
    {
        return $this->builder->toSql();
    }

    public function orWhere(string|Closure $field, mixed $operator = null, mixed $value = null): EntityBuilder
    {
        return $this->builder->orWhere($field, $operator, $value);
    }

    public function when(mixed $value, callable $callback): EntityBuilder
    {
        return $this->builder->when($value, $callback);
    }

    public function filter(array $filters): EntityBuilder
    {
        return $this->builder->filter($filters);
    }

    public function sort(string $field, string $direction = 'asc'): EntityBuilder
    {
        return $this->builder->sort($field, $direction);
    }

    public function paginate(int $perPage, int $page = 1): EntityBuilder
    {
        return $this->builder->paginate($perPage, $page);
    }

    public function with(string $relation, ?callable $callback = null): EntityBuilder
    {
        return $this->builder->with($relation, $callback);
    }

    /**
     * Get all available fields including relations
     */
    public function getAllowedFields(array $visited = []): array
    {
        $fields = static::getFields();
        $allowedFields = [...$fields]; // Копируем базовые поля

        // Проверяем, не посещали ли мы уже этот класс (защита от рекурсии)
        $currentClass = static::class;
        if (in_array($currentClass, $visited)) {
            return $allowedFields;
        }
        $visited[] = $currentClass;

        // Получаем все публичные методы класса
        $methods = get_class_methods($this);

        foreach ($methods as $method) {
            // Пропускаем методы базового класса и другие служебные методы
            if (method_exists(__CLASS__, $method)) {
                continue;
            }

            // Проверяем, является ли метод связью
            if (method_exists($this, $method)) {
                $relation = $this->{$method}();
                if ($relation instanceof RelationBuilder) {
                    // Добавляем имя связи как доступное поле
                    $allowedFields[] = $method;

                    // Получаем класс связанной сущности
                    $entityClass = $relation->getEntityClass();
                    $relationFields = $entityClass::getFields();

                    // Добавляем каждое поле связи отдельно
                    foreach ($relationFields as $field) {
                        $allowedFields[] = "{$method}.{$field}";
                    }

                    // Рекурсивно добавляем поля вложенных связей только если не посещали этот класс
                    if (!in_array($entityClass, $visited)) {
                        $relatedEntity = new $entityClass();
                        $nestedFields = $relatedEntity->getAllowedFields($visited);
                        foreach ($nestedFields as $nestedField) {
                            // Пропускаем базовые поля, которые уже добавлены выше
                            if (!in_array($nestedField, $relationFields)) {
                                $allowedFields[] = "{$method}.{$nestedField}";
                            }
                        }
                    }
                }
            }
        }

        return array_unique($allowedFields);
    }

    public function parseFields(array $fields = ['*']): array
    {
        $baseFields = [];
        $relationFields = [];

        foreach ($fields as $field) {
            if (method_exists($this, $field)) {
                $relationFields[$field] = ['*'];
            } elseif (str_contains($field, '.')) {
                [$relation, $relationField] = explode('.', $field, 2);
                if (!isset($relationFields[$relation])) {
                    $relationFields[$relation] = [];
                }
                $relationFields[$relation][] = $relationField;
            } else {
                $baseFields[] = $field;
            }
        }

        return [$baseFields, $relationFields];
    }

    public function newQuery(): EntityBuilder
    {
        return new EntityBuilder($this);
    }

    public function doesntHave(string $relation, ?callable $callback = null): EntityBuilder
    {
        return $this->builder->doesntHave($relation, $callback);
    }

    public function leftJoin(string $string, string $string1, string $string2, string $string3): EntityBuilder
    {
        return $this->builder->leftJoin($string, $string1, $string2, $string3);
    }
}
