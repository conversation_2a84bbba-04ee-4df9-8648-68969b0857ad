# Путь обработки API запроса в M1 Laravel Backend

## Общая схема обработки запроса

Каждый API запрос в системе проходит следующий путь:

```
HTTP Request → Routes → Middleware → Controller → Policy → Service → Handler → Repository → Database
```

## Детальное описание каждого этапа

### 1. Маршрутизация (Routes)
**Файл:** `routes/api.php`

- Все API маршруты находятся в группе с префиксом `/api/internal`
- Применяются middleware: `auth:sanctum`, `verified`, `AuthPermissionMiddleware`
- Маршруты организованы по доменам (products, contractors, warehouses и т.д.)

```php
Route::group([
    'middleware' => ['auth:sanctum', 'verified'],
    'prefix' => 'internal'
], static function () {
    Route::group([
        'middleware' => [AuthPermissionMiddleware::class],
    ], static function () {
        // API маршруты
    });
});
```

### 2. Middleware (Промежуточное ПО)

#### 2.1 Laravel Sanctum (`auth:sanctum`)
- Проверяет аутентификацию пользователя через токен
- Устанавливает текущего пользователя в `Auth::user()`

#### 2.2 Email Verification (`verified`)
- Проверяет, что email пользователя подтвержден

#### 2.3 AuthPermissionMiddleware
**Файл:** `app/Http/Middleware/AuthPermissionMiddleware.php`

- Инициализирует сервис авторизации
- Загружает права доступа пользователя в кэш
- Подготавливает контекст для проверки прав

```php
public function handle(Request $request, Closure $next): Response
{
    $authorizationService = Container::getInstance()->make(AuthorizationServiceContract::class);
    $authorizationService->init();
    return $next($request);
}
```

### 3. Контроллер (Controller)
**Пример:** `app/Http/Controllers/Api/Internal/Goods/Products/ProductController.php`

#### Структура контроллера:
- Наследуется от базового `Controller`
- Использует трейты `ApiPolicy` и `ApiResponse`
- Инжектирует сервис и policy через конструктор
- Каждый метод обернут в `executeAction()` для обработки исключений

```php
public function index(ProductIndexRequest $request): JsonResponse
{
    return $this->executeAction(function () use ($request) {
        $data = $request->toDTO();
        $products = $this->productsServiceContract->index($data);
        return $this->successResponse($products);
    });
}
```

#### Основные методы контроллера:
- `index()` - получение списка ресурсов
- `store()` - создание нового ресурса
- `show()` - получение конкретного ресурса
- `update()` - обновление ресурса
- `destroy()` - удаление ресурса

### 4. Валидация запроса (Form Request)
**Пример:** `ProductStoreRequest`, `ProductUpdateRequest`

- Валидирует входящие данные
- Преобразует данные в DTO через метод `toDTO()`
- Содержит правила валидации и сообщения об ошибках

### 5. Проверка прав доступа (Policy)
**Трейт:** `app/Traits/ApiPolicy.php`

Выполняет проверку прав через policy:
- `authorizeView()` - право на просмотр
- `authorizeCreate()` - право на создание
- `authorizeUpdate()` - право на обновление
- `authorizeDelete()` - право на удаление

```php
protected function authorizeCreate(Request $request, DtoContract $dto): void
{
    $this->getPolicy()->create($request->user(), $dto);
}
```

### 6. Сервис (Service)
**Пример:** `app/Services/Api/Internal/Goods/Products/ProductsService/ProductsService.php`

#### Роль сервиса:
- Координирует бизнес-логику
- Делегирует операции специализированным хендлерам
- Реализует контракт сервиса

```php
public function create(HasInsertArrayDtoContract $dto): string
{
    return $this->createHandler->run($dto);
}
```

### 7. Хендлер (Handler)
**Пример:** `app/Services/Api/Internal/Goods/Products/ProductsService/Handlers/ProductCreateHandler.php`

#### Функции хендлера:
- Содержит конкретную бизнес-логику операции
- Работает с несколькими репозиториями
- Управляет транзакциями
- Генерирует UUID для новых записей

```php
public function run(HasInsertArrayDtoContract $dto): string
{
    // Бизнес-логика создания продукта
    // Работа с несколькими репозиториями
    // Возврат ID созданного ресурса
}
```

### 8. Репозиторий (Repository)
**Пример:** `app/Repositories/Goods/Products/ProductsRepository.php`

#### Ответственность репозитория:
- Абстракция работы с базой данных
- Применение фильтров авторизации через `AuthorizationService`
- Построение сложных запросов с джойнами и условиями
- Работа с Entity Builder для типизированных запросов

```php
public function get(string $id, array $filters = []): Collection
{
    $query = $this->getEntity();
    $query = $this->authService->queryFilter($id, $query, PermissionNameEnum::GOODS_AND_SERVICES->value);
    // Применение фильтров и возврат данных
}
```

### 9. Авторизация на уровне данных
**Сервис:** `app/Services/Api/Internal/AuthorizationService.php`

#### Функции авторизации:
- Загрузка прав пользователя из кэша
- Проверка принадлежности к кабинету
- Фильтрация запросов по правам доступа
- Проверка прав на конкретные операции

### 10. Обработка ответа
**Трейт:** `app/Traits/ApiResponse.php`

#### Типы ответов:
- `successResponse()` - успешный ответ с данными
- `createdResponse()` - ответ при создании (201)
- `noContentResponse()` - ответ без содержимого (204)
- `errorResponse()` - ответ с ошибкой

#### Обработка исключений:
- `AccessDeniedException` → 403 Forbidden
- `NotFoundException` → 404 Not Found
- `QueryException` → специальная обработка ошибок БД
- Общие исключения → 500 Internal Server Error

## Ключевые принципы архитектуры

### 1. Разделение ответственности
- **Controller** - только HTTP логика
- **Service** - координация бизнес-процессов
- **Handler** - конкретная бизнес-логика
- **Repository** - работа с данными

### 2. Безопасность
- Многоуровневая авторизация (middleware + policy + repository)
- Фильтрация данных по правам доступа
- Проверка принадлежности к кабинету

### 3. Типизация
- DTO для передачи данных между слоями
- Контракты для всех сервисов и репозиториев
- Entity Builder для типизированных запросов

### 4. Обработка ошибок
- Централизованная обработка через `executeAction()`
- Специализированные исключения
- Понятные сообщения об ошибках на русском языке

## Пример полного цикла запроса

```
POST /api/internal/products
↓
1. Sanctum проверяет токен
2. AuthPermissionMiddleware загружает права
3. ProductController::store()
4. ProductStoreRequest валидирует данные
5. Policy проверяет право на создание
6. ProductsService::create()
7. ProductCreateHandler::run()
8. ProductsRepository::create()
9. Запись в БД
10. Возврат ID созданного продукта
```

Этот подход обеспечивает безопасность, масштабируемость и поддерживаемость кода.