<?php

use App\Modules\Marketplaces\Services\Ozon\Jobs\CheckCarriageDocumentsJob;
use App\Modules\Marketplaces\Services\Ozon\Jobs\CheckCarriageWaybillJob;
use App\Modules\Marketplaces\Services\Ozon\Jobs\SyncCarriageDetailsJob;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Schedule::job(new CheckCarriageDocumentsJob())
    ->everyFiveMinutes()
    ->name('check-ozon-carriage-documents')
    ->withoutOverlapping();

Schedule::job(new CheckCarriageWaybillJob())
    ->everyFiveMinutes()
    ->name('check-ozon-carriage-waybill')
    ->withoutOverlapping();

Schedule::job(new SyncCarriageDetailsJob())
    ->everyFiveMinutes()
    ->name('check-ozon-carriage-details')
    ->withoutOverlapping();

Schedule::command('app:update-currencies')->hourly();
Schedule::command('app:delete-old-suggestions')->daily();

Schedule::command('app:delete-old-cabinet-invites')->hourly();
Schedule::command('app:delete-expires-verification-codes')->hourly();
Schedule::command('app:delete-old-currencies-history')->daily();
