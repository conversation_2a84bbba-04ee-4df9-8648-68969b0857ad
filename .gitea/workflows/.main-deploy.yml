# name: Main deploy
# run-name: ${{ gitea.actor }} is testing feature
# on:
#   push:
#     branches:
#       - main

# jobs:
#   deploy:
#     runs-on: ubuntu-latest
#     # needs: testing
#     steps:
#       - run: echo "Некуда деплоить 🤖"
#       # - name: executing remote ssh commands using password
#       #   uses: appleboy/ssh-action@v1.0.3
#       #   with:
#       #     host: ${{ secrets.DEV_HOST }}
#       #     username: ${{ secrets.DEV_USER }}
#       #     password: ${{ secrets.DEV_PASSWORD }}
#       #   script: |
#       #     cd ~/m1/src
#       #     git pull origin dev
#       #     docker exec m1-php-1 composer install
#       #     docker exec m1-php-1 php artisan cache:clear
