# M1 Laravel Backend

## Установка

- Установить **Docker Compose** по [официальной документации](https://docs.docker.com/compose/install/)
- Клонировать репозиторий в вашу папку с проектами
    ```bash
    <NAME_EMAIL>:M1/Backend.git
    ```
- Перейти в новую созданную папку
- Скопировать .env.example с переименованием
    ```bash
    cp ./.env.example ./.env
    ```
- Запустить Docker Compose
    ```bash
    docker compose up --build
    ```
- Похвалить себя и скушать печенье

## Стандарты
Стандарты проекта можно прочитать [здесь](https://app.weeek.net/ws/569485/document/19). Вероятно, они будут дополняться.

## <span style="color:#f43f5e">Важно</span>

Данный файл дополнится тем, как подключать Frontend локально