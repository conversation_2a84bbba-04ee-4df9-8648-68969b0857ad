<?php

namespace Tests\Unit\Console\Commands;

use Tests\TestCase;
use Mockery;
use App\Actions\Currencies\FillCurenciesAction;
use App\Console\Commands\FillCurencies;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class FillCurenciesTest extends TestCase
{
    use DatabaseTransactions;

    private FillCurenciesAction $action;
    private FillCurencies $command;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock for FillCurenciesAction
        $this->action = Mockery::mock(FillCurenciesAction::class);

        // Create command instance with mocked action
        $this->command = new FillCurencies($this->action);

        // Register command in the application
        $this->app->instance(FillCurencies::class, $this->command);
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        // Clean up Mockery
        Mockery::close();
    }

    public function test_it_should_fill_currencies_successfully(): void
    {
        // Arrange
        $this->action
            ->shouldReceive('handle')
            ->once()
            ->andReturn(true);

        // Act & Assert
        $this->artisan('app:get-currencies')
            ->assertSuccessful()
            ->expectsOutput('Заполняем таблицу "global_currencies" используя API курсов валют ЦБ РФ...')
            ->expectsOutput('Заполнение таблицы "global_currencies" завершено.');
    }

    public function test_it_should_handle_action_exceptions_gracefully(): void
    {
        // Arrange
        $errorMessage = 'Ошибка при получении данных из API ЦБ РФ';

        $this->action
            ->shouldReceive('handle')
            ->once()
            ->andThrow(new \Exception($errorMessage));

        // Act & Assert
        $this->artisan('app:get-currencies')
            ->assertFailed()
            ->expectsOutput('Заполняем таблицу "global_currencies" используя API курсов валют ЦБ РФ...')
            ->expectsOutput('Ошибка при заполнении валют: ' . $errorMessage);
    }
}
