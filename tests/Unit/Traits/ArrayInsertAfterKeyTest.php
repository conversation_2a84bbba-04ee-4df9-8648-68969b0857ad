<?php

namespace Tests\Unit\Traits;

use Tests\TestCase;
use App\Traits\ArrayInsertAfterKey;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TestClass
{
    use ArrayInsertAfterKey;
}

class ArrayInsertAfterKeyTest extends TestCase
{
    use RefreshDatabase;

    private TestClass $testClass;

    protected function setUp(): void
    {
        parent::setUp();
        $this->testClass = new TestClass();
    }

    public function test_it_inserts_array_after_existing_key(): void
    {
        // Arrange
        $array = ['a' => 1, 'b' => 2, 'c' => 3];
        $key = 'b';
        $insertArray = ['x' => 10, 'y' => 20];

        // Act
        $this->testClass->array_insert_after_key($array, $key, $insertArray);

        // Assert
        $this->assertEquals([
            'a' => 1,
            'b' => 2,
            'x' => 10,
            'y' => 20,
            'c' => 3
        ], $array);
    }

    public function test_it_inserts_array_at_the_end_when_key_not_found(): void
    {
        // Arrange
        $array = ['a' => 1, 'b' => 2, 'c' => 3];
        $key = 'd';
        $insertArray = ['x' => 10, 'y' => 20];

        // Act
        $this->testClass->array_insert_after_key($array, $key, $insertArray);

        // Assert
        $this->assertEquals([
            'a' => 1,
            'b' => 2,
            'c' => 3,
            'x' => 10,
            'y' => 20
        ], $array);
    }

    public function test_it_inserts_array_after_numeric_key(): void
    {
        // Arrange
        $array = ['1' => 'a', '2' => 'b', '3' => 'c'];
        $key = '2';
        $insertArray = ['4' => 'x', '5' => 'y'];

        // Act
        $this->testClass->array_insert_after_key($array, $key, $insertArray);

        // Assert
        $this->assertEquals([
            '1' => 'a',
            '2' => 'b',
            '4' => 'x',
            '5' => 'y',
            '3' => 'c'
        ], $array);
    }

    public function test_it_inserts_empty_array(): void
    {
        // Arrange
        $array = ['a' => 1, 'b' => 2, 'c' => 3];
        $key = 'b';
        $insertArray = [];

        // Act
        $this->testClass->array_insert_after_key($array, $key, $insertArray);

        // Assert
        $this->assertEquals([
            'a' => 1,
            'b' => 2,
            'c' => 3
        ], $array);
    }

    public function test_it_inserts_array_after_first_key(): void
    {
        // Arrange
        $array = ['a' => 1, 'b' => 2, 'c' => 3];
        $key = 'a';
        $insertArray = ['x' => 10, 'y' => 20];

        // Act
        $this->testClass->array_insert_after_key($array, $key, $insertArray);

        // Assert
        $this->assertEquals([
            'a' => 1,
            'x' => 10,
            'y' => 20,
            'b' => 2,
            'c' => 3
        ], $array);
    }

    public function test_it_inserts_array_after_last_key(): void
    {
        // Arrange
        $array = ['a' => 1, 'b' => 2, 'c' => 3];
        $key = 'c';
        $insertArray = ['x' => 10, 'y' => 20];

        // Act
        $this->testClass->array_insert_after_key($array, $key, $insertArray);

        // Assert
        $this->assertEquals([
            'a' => 1,
            'b' => 2,
            'c' => 3,
            'x' => 10,
            'y' => 20
        ], $array);
    }
}
