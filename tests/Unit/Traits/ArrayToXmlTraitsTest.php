<?php

namespace Tests\Unit\Traits;

use Tests\TestCase;
use App\Traits\ArrayToXmlTraits;
use SimpleXMLElement;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TestArrayToXmlClass
{
    use ArrayToXmlTraits;
}

class ArrayToXmlTraitsTest extends TestCase
{
    use RefreshDatabase;
    private TestArrayToXmlClass $testClass;


    protected function setUp(): void
    {
        parent::setUp();
        $this->testClass = new TestArrayToXmlClass();
    }

    public function it_converts_simple_array_to_xml(): void
    {
        // Arrange
        $data = [
            'name' => 'John',
            'age' => 30,
            'city' => 'New York'
        ];
        $xml = new SimpleXMLElement('<?xml version="1.0"?><root></root>');

        // Act
        $this->testClass->arrayToXml($data, $xml);

        // Assert
        $this->assertEquals('John', (string)$xml->name);
        $this->assertEquals('30', (string)$xml->age);
        $this->assertEquals('New York', (string)$xml->city);
    }

    public function test_it_converts_nested_array_to_xml(): void
    {
        // Arrange
        $data = [
            'person' => [
                'name' => 'John',
                'address' => [
                    'street' => '123 Main St',
                    'city' => 'New York'
                ]
            ]
        ];
        $xml = new SimpleXMLElement('<?xml version="1.0"?><root></root>');

        // Act
        $this->testClass->arrayToXml($data, $xml);

        // Assert
        $this->assertEquals('John', (string)$xml->person->name);
        $this->assertEquals('123 Main St', (string)$xml->person->address->street);
        $this->assertEquals('New York', (string)$xml->person->address->city);
    }

    public function test_it_converts_indexed_array_to_xml(): void
    {
        // Arrange
        $data = [
            'items' => [
                ['name' => 'Item 1', 'price' => 10],
                ['name' => 'Item 2', 'price' => 20]
            ]
        ];
        $xml = new SimpleXMLElement('<?xml version="1.0"?><root></root>');

        // Act
        $this->testClass->arrayToXml($data, $xml);

        // Assert
        $this->assertEquals('Item 1', (string)$xml->items[0]->name);
        $this->assertEquals('10', (string)$xml->items[0]->price);
        $this->assertEquals('Item 2', (string)$xml->items[1]->name);
        $this->assertEquals('20', (string)$xml->items[1]->price);
    }

    public function test_it_converts_array_with_attributes_to_xml(): void
    {
        // Arrange
        $data = [
            'person' => [
                '@attributes' => [
                    'id' => '123',
                    'type' => 'customer'
                ],
                'name' => 'John',
                'age' => 30
            ]
        ];
        $xml = new SimpleXMLElement('<?xml version="1.0"?><root></root>');

        // Act
        $this->testClass->arrayToXml($data, $xml);

        // Assert
        $this->assertEquals('123', (string)$xml->person['id']);
        $this->assertEquals('customer', (string)$xml->person['type']);
        $this->assertEquals('John', (string)$xml->person->name);
        $this->assertEquals('30', (string)$xml->person->age);
    }

    public function test_it_escapes_special_characters(): void
    {
        // Arrange
        $data = [
            'text' => '<script>alert("xss")</script> & "quotes"'
        ];
        $xml = new SimpleXMLElement('<?xml version="1.0"?><root></root>');

        // Act
        $this->testClass->arrayToXml($data, $xml);

        // Assert
        $this->assertEquals('&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt; &amp; &quot;quotes&quot;', (string)$xml->text);
    }

    public function test_it_converts_empty_array_to_xml(): void
    {
        // Arrange
        $data = [];
        $xml = new SimpleXMLElement('<?xml version="1.0"?><root></root>');

        // Act
        $this->testClass->arrayToXml($data, $xml);

        // Assert
        $this->assertEquals('', (string)$xml);
    }

    public function test_it_converts_array_with_mixed_types(): void
    {
        // Arrange
        $data = [
            'string' => 'text',
            'number' => 42,
            'boolean' => true,
            'null' => null,
            'array' => ['item1', 'item2'],
            'object' => (object)['key' => 'value']
        ];
        $xml = new SimpleXMLElement('<?xml version="1.0"?><root></root>');

        // Act
        $this->testClass->arrayToXml($data, $xml);

        // Assert
        $this->assertEquals('text', (string)$xml->string);
        $this->assertEquals('42', (string)$xml->number);
        $this->assertEquals('1', (string)$xml->boolean);
        $this->assertEquals('', (string)$xml->null);
        $this->assertEquals('item1', (string)$xml->array[0]);
        $this->assertEquals('item2', (string)$xml->array[1]);
        $this->assertEquals('value', (string)$xml->object->key);
    }
}
