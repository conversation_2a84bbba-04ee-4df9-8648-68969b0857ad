<?php

namespace Tests\Unit\Listeners;

use Tests\TestCase;
use Mockery;
use App\Events\BarcodeDeletedEvent;
use App\Listeners\DeleteBarcodeListener;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DeleteBarcodeListenerTest extends TestCase
{
    use RefreshDatabase;

    private DeleteBarcodeListener $listener;

    protected function setUp(): void
    {
        parent::setUp();
        $this->listener = new DeleteBarcodeListener();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_it_deletes_barcodes_successfully(): void
    {
        // Подготавливаем тестовые данные
        $barcodableId = 'test-barcodable-id';
        $event = new BarcodeDeletedEvent($barcodableId);

        // Мокаем фасад DB
        DB::shouldReceive('table')
            ->with('barcodes')
            ->once()
            ->andReturn(Mockery::mock([
                'where' => Mockery::mock([
                    'delete' => true
                ])
            ]));

        // Выполняем слушатель
        $this->listener->handle($event);

        // Проверяем, что метод delete был вызван
        $this->assertTrue(true);
    }

    public function test_it_handles_database_exceptions_gracefully(): void
    {
        // Подготавливаем тестовые данные
        $barcodableId = 'test-barcodable-id';
        $event = new BarcodeDeletedEvent($barcodableId);

        // Мокаем фасад DB с исключением
        DB::shouldReceive('table')
            ->with('barcodes')
            ->once()
            ->andThrow(new \Exception('Database error'));

        // Проверяем, что исключение пробрасывается выше
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        // Выполняем слушатель
        $this->listener->handle($event);
    }
}
