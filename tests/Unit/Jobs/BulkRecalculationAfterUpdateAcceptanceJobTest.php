<?php

namespace Tests\Unit\Jobs;

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Jobs\BulkRecalculationAfterUpdateAcceptanceJob;
use App\Jobs\BulkHandleFifoJob;
use App\Jobs\BulkRecalculateAcceptanceItemsRecidualJob;
use App\Models\Acceptance;
use App\Models\AcceptanceItem;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\WarehouseItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Queue;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Contracts\Container\BindingResolutionException;

class BulkRecalculationAfterUpdateAcceptanceJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    private Collection $oldResources;
    private Collection $newResources;
    private Warehouse $warehouse;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->warehouse = Warehouse::factory()->create();
        $this->product = Product::factory()->create();
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_updates_warehouse_items_when_dates_change(): void
    {
        // Arrange
        $oldAcceptance = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'held' => true
        ]);

        AcceptanceItem::factory()->create([
            'acceptance_id' => $oldAcceptance->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'price' => 100
        ]);

        WarehouseItem::factory()->create([
            'acceptance_id' => $oldAcceptance->id,
            'product_id' => $this->product->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => 10,
            'unit_price' => 100,
            'total_price' => 1000,
            'received_at' => '2024-01-01'
        ]);

        // Сохраняем старую версию
        $this->oldResources = collect([$oldAcceptance]);

        // Создаем новую версию с обновленной датой
        $newAcceptance = $oldAcceptance->replicate();
        $newAcceptance->id = $oldAcceptance->id;
        $newAcceptance->date_from = '2024-01-02';
        $newAcceptance->held = true;

        $this->newResources = collect([$newAcceptance]);

        Queue::fake();

        // Act
        $job = new BulkRecalculationAfterUpdateAcceptanceJob($this->oldResources, $this->newResources);
        $job->handle();

        // Assert
        $this->assertDatabaseHas('warehouse_items', [
            'acceptance_id' => $oldAcceptance->id,
            'received_at' => '2024-01-02 00:00:00'
        ]);

        // Не должен запускать FIFO пересчет, так как нет отгрузок
        Queue::assertNotPushed(BulkHandleFifoJob::class);
        Queue::assertPushed(BulkRecalculateAcceptanceItemsRecidualJob::class);
    }

    public function test_it_skips_update_when_acceptance_not_held(): void
    {
        // Arrange
        $oldAcceptance = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'held' => false
        ]);

        AcceptanceItem::factory()->create([
            'acceptance_id' => $oldAcceptance->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'price' => 100
        ]);

        WarehouseItem::factory()->create([
            'acceptance_id' => $oldAcceptance->id,
            'product_id' => $this->product->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => 10,
            'unit_price' => 100,
            'total_price' => 1000,
            'received_at' => '2024-01-01'
        ]);

        // Сохраняем старую версию
        $this->oldResources = collect([$oldAcceptance]);

        // Создаем новую версию с обновленной датой, но held = false
        $newAcceptance = $oldAcceptance->replicate();
        $newAcceptance->id = $oldAcceptance->id;
        $newAcceptance->date_from = '2024-01-02';
        $newAcceptance->held = false;

        $this->newResources = collect([$newAcceptance]);

        Queue::fake();

        // Act
        $job = new BulkRecalculationAfterUpdateAcceptanceJob($this->oldResources, $this->newResources);
        $job->handle();

        // Assert
        $this->assertDatabaseHas('warehouse_items', [
            'acceptance_id' => $oldAcceptance->id,
            'received_at' => '2024-01-01 00:00:00'
        ]);

        // Не должен запускать FIFO пересчет, так как нет отгрузок
        Queue::assertNotPushed(BulkHandleFifoJob::class);
        Queue::assertPushed(BulkRecalculateAcceptanceItemsRecidualJob::class);
    }

    public function test_it_updates_multiple_warehouse_items(): void
    {
        // Arrange
        $oldAcceptance1 = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'held' => true
        ]);

        $oldAcceptance2 = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-02',
            'held' => true
        ]);

        // Create items for both acceptances
        foreach ([$oldAcceptance1, $oldAcceptance2] as $acceptance) {
            AcceptanceItem::factory()->create([
                'acceptance_id' => $acceptance->id,
                'product_id' => $this->product->id,
                'quantity' => 10,
                'price' => 100
            ]);

            WarehouseItem::factory()->create([
                'acceptance_id' => $acceptance->id,
                'product_id' => $this->product->id,
                'warehouse_id' => $this->warehouse->id,
                'quantity' => 10,
                'unit_price' => 100,
                'total_price' => 1000,
                'received_at' => $acceptance->date_from
            ]);
        }

        // Сохраняем старые версии
        $this->oldResources = collect([$oldAcceptance1, $oldAcceptance2]);

        // Создаем новые версии с обновленными датами
        $newAcceptance1 = $oldAcceptance1->replicate();
        $newAcceptance1->id = $oldAcceptance1->id;
        $newAcceptance1->date_from = '2024-01-03';
        $newAcceptance1->held = true;

        $newAcceptance2 = $oldAcceptance2->replicate();
        $newAcceptance2->id = $oldAcceptance2->id;
        $newAcceptance2->date_from = '2024-01-04';
        $newAcceptance2->held = true;

        $this->newResources = collect([$newAcceptance1, $newAcceptance2]);

        Queue::fake();

        // Act
        $job = new BulkRecalculationAfterUpdateAcceptanceJob($this->oldResources, $this->newResources);
        $job->handle();

        // Assert
        $this->assertDatabaseHas('warehouse_items', [
            'acceptance_id' => $oldAcceptance1->id,
            'received_at' => '2024-01-03 00:00:00'
        ]);

        $this->assertDatabaseHas('warehouse_items', [
            'acceptance_id' => $oldAcceptance2->id,
            'received_at' => '2024-01-04 00:00:00'
        ]);

        // Не должен запускать FIFO пересчет, так как нет отгрузок
        Queue::assertNotPushed(BulkHandleFifoJob::class);
        Queue::assertPushed(BulkRecalculateAcceptanceItemsRecidualJob::class);
    }

    public function test_it_handles_multiple_warehouses(): void
    {
        // Arrange
        $warehouse2 = Warehouse::factory()->create();

        $oldAcceptance1 = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'held' => true
        ]);

        $oldAcceptance2 = Acceptance::factory()->create([
            'warehouse_id' => $warehouse2->id,
            'date_from' => '2024-01-01',
            'held' => true
        ]);

        // Create items for both acceptances
        foreach ([$oldAcceptance1, $oldAcceptance2] as $acceptance) {
            AcceptanceItem::factory()->create([
                'acceptance_id' => $acceptance->id,
                'product_id' => $this->product->id,
                'quantity' => 10,
                'price' => 100
            ]);

            WarehouseItem::factory()->create([
                'acceptance_id' => $acceptance->id,
                'product_id' => $this->product->id,
                'warehouse_id' => $acceptance->warehouse_id,
                'quantity' => 10,
                'unit_price' => 100,
                'total_price' => 1000,
                'received_at' => $acceptance->date_from
            ]);
        }

        // Сохраняем старые версии
        $this->oldResources = collect([$oldAcceptance1, $oldAcceptance2]);

        // Создаем новые версии с обновленными датами
        $newAcceptance1 = $oldAcceptance1->replicate();
        $newAcceptance1->id = $oldAcceptance1->id;
        $newAcceptance1->date_from = '2024-01-02';
        $newAcceptance1->held = true;

        $newAcceptance2 = $oldAcceptance2->replicate();
        $newAcceptance2->id = $oldAcceptance2->id;
        $newAcceptance2->date_from = '2024-01-02';
        $newAcceptance2->held = true;

        $this->newResources = collect([$newAcceptance1, $newAcceptance2]);

        Queue::fake();

        // Act
        $job = new BulkRecalculationAfterUpdateAcceptanceJob($this->oldResources, $this->newResources);
        $job->handle();

        // Assert
        foreach ([$oldAcceptance1, $oldAcceptance2] as $acceptance) {
            $this->assertDatabaseHas('warehouse_items', [
                'acceptance_id' => $acceptance->id,
                'warehouse_id' => $acceptance->warehouse_id,
                'received_at' => '2024-01-02 00:00:00'
            ]);
        }

        // Не должен запускать FIFO пересчет, так как нет отгрузок
        Queue::assertNotPushed(BulkHandleFifoJob::class);
        Queue::assertPushed(BulkRecalculateAcceptanceItemsRecidualJob::class, 2);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_skips_recalculation_when_no_changes(): void
    {
        // Arrange
        $oldAcceptance = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01 00:00:00',
            'held' => true
        ]);

        AcceptanceItem::factory()->create([
            'acceptance_id' => $oldAcceptance->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'price' => 100
        ]);

        WarehouseItem::factory()->create([
            'acceptance_id' => $oldAcceptance->id,
            'product_id' => $this->product->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => 10,
            'unit_price' => 100,
            'total_price' => 1000,
            'received_at' => '2024-01-01 00:00:00'
        ]);

        // Сохраняем старую версию
        $this->oldResources = collect([$oldAcceptance]);

        // Создаем новую версию с теми же данными
        $newAcceptance = $oldAcceptance->replicate();
        $newAcceptance->id = $oldAcceptance->id;
        $newAcceptance->date_from = $oldAcceptance->date_from;
        $newAcceptance->held = true;

        $this->newResources = collect([$newAcceptance]);

        Queue::fake();

        // Act
        $job = new BulkRecalculationAfterUpdateAcceptanceJob($this->oldResources, $this->newResources);
        $job->handle();

        // Assert
        Queue::assertNotPushed(BulkHandleFifoJob::class);
        Queue::assertNotPushed(BulkRecalculateAcceptanceItemsRecidualJob::class);

        // Проверяем что warehouse items не изменились
        $warehouseItem = WarehouseItem::where('acceptance_id', $oldAcceptance->id)->first();
        $this->assertEquals($oldAcceptance->date_from, $warehouseItem->received_at);
    }

    public function test_it_maintains_fifo_order_when_changing_dates(): void
    {
        // Arrange
        $oldAcceptance1 = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-01',
            'held' => true
        ]);

        $oldAcceptance2 = Acceptance::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'date_from' => '2024-01-02',
            'held' => true
        ]);

        // Create items for both acceptances
        foreach ([$oldAcceptance1, $oldAcceptance2] as $acceptance) {
            AcceptanceItem::factory()->create([
                'acceptance_id' => $acceptance->id,
                'product_id' => $this->product->id,
                'quantity' => 10,
                'price' => 100
            ]);

            WarehouseItem::factory()->create([
                'acceptance_id' => $acceptance->id,
                'product_id' => $this->product->id,
                'warehouse_id' => $this->warehouse->id,
                'quantity' => 10,
                'unit_price' => 100,
                'total_price' => 1000,
                'received_at' => $acceptance->date_from
            ]);
        }

        // Сохраняем старые версии
        $this->oldResources = collect([$oldAcceptance1, $oldAcceptance2]);

        // Создаем новые версии с обновленными датами
        $newAcceptance1 = $oldAcceptance1->replicate();
        $newAcceptance1->id = $oldAcceptance1->id;
        $newAcceptance1->date_from = '2024-01-03';
        $newAcceptance1->held = true;

        $newAcceptance2 = $oldAcceptance2->replicate();
        $newAcceptance2->id = $oldAcceptance2->id;
        $newAcceptance2->date_from = '2024-01-01';
        $newAcceptance2->held = true;

        $this->newResources = collect([$newAcceptance1, $newAcceptance2]);

        Queue::fake();

        // Act
        $job = new BulkRecalculationAfterUpdateAcceptanceJob($this->oldResources, $this->newResources);
        $job->handle();

        // Assert
        $this->assertDatabaseHas('warehouse_items', [
            'acceptance_id' => $oldAcceptance1->id,
            'received_at' => '2024-01-03 00:00:00'
        ]);

        $this->assertDatabaseHas('warehouse_items', [
            'acceptance_id' => $oldAcceptance2->id,
            'received_at' => '2024-01-01 00:00:00'
        ]);

        // Не должен запускать FIFO пересчет, так как нет отгрузок
        Queue::assertNotPushed(BulkHandleFifoJob::class);
        Queue::assertPushed(BulkRecalculateAcceptanceItemsRecidualJob::class);
    }
}
