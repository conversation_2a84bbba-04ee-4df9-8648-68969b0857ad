<?php

namespace Tests\Unit\Jobs\FIFOJobs;

use Tests\TestCase;
use Mockery;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Jobs\FIFOJobs\RecalculateAcceptanceItemsRecidualJob;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Contracts\Container\BindingResolutionException;

class RecalculateAcceptanceItemsRecidualJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;


    private AcceptanceRepositoryContract $acceptanceRepository;
    private object $acceptance;
    private string $productId;

    protected function setUp(): void
    {
        parent::setUp();

        $this->acceptance = (object)[
            'id' => $this->faker()->uuid,
            'date' => now()
        ];
        $this->productId = $this->faker()->uuid;

        $this->acceptanceRepository = Mockery::mock(AcceptanceRepositoryContract::class);
        $this->app->instance(AcceptanceRepositoryContract::class, $this->acceptanceRepository);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_recalculates_acceptance_items_recidual(): void
    {
        // Arrange
        $items = collect([
            (object)[
                'id' => '1',
                'recidual' => 10,
                'updated_at' => now(),
                'acceptance_id' => $this->acceptance->id,
                'product_id' => $this->productId,
                'quantity' => 100,
                'price' => 50
            ],
            (object)[
                'id' => '2',
                'recidual' => 20,
                'updated_at' => now(),
                'acceptance_id' => $this->acceptance->id,
                'product_id' => $this->productId,
                'quantity' => 200,
                'price' => 60
            ]
        ]);

        $this->acceptanceRepository
            ->shouldReceive('getNewestAcceptancesForReciduals')
            ->once()
            ->with($this->acceptance, $this->productId)
            ->andReturn($items);

        // Мокаем DB::table для проверки upsert
        DB::shouldReceive('table')
            ->once()
            ->with('acceptance_items')
            ->andReturnSelf();

        DB::shouldReceive('upsert')
            ->once()
            ->withArgs(function ($records, $uniqueBy, $update) {
                // Проверяем что данные правильно преобразованы
                return count($records) === 2
                    && $records[0]['id'] === '1'
                    && $records[0]['recidual'] === 10
                    && $records[1]['id'] === '2'
                    && $records[1]['recidual'] === 20
                    && $uniqueBy === ['id']
                    && $update === ['recidual', 'updated_at'];
            });

        // Act
        $job = new RecalculateAcceptanceItemsRecidualJob($this->acceptance, $this->productId);
        $job->handle();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
