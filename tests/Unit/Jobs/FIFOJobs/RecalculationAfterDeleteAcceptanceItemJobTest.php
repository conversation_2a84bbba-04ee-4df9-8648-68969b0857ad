<?php

namespace Tests\Unit\Jobs\FIFOJobs;

use Tests\TestCase;
use Mockery;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Jobs\FIFOJobs\RecalculationAfterDeleteAcceptanceItemJob;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use Illuminate\Foundation\Testing\WithFaker;

class RecalculationAfterDeleteAcceptanceItemJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;


    private FifoServiceContract $fifoService;
    private WarehouseItemsRepositoryContract $warehouseItemsRepository;
    private AcceptanceItemsRepositoryContract $acceptanceItemsRepository;
    private string $resourceId;
    private object $resource;

    protected function setUp(): void
    {
        parent::setUp();

        $this->resourceId = $this->faker()->uuid;
        $this->resource = (object)[
            'id' => $this->resourceId,
            'acceptance_id' => $this->faker()->uuid,
            'product_id' => $this->faker()->uuid
        ];

        $this->fifoService = Mockery::mock(FifoServiceContract::class);
        $this->warehouseItemsRepository = Mockery::mock(WarehouseItemsRepositoryContract::class);
        $this->acceptanceItemsRepository = Mockery::mock(AcceptanceItemsRepositoryContract::class);

        $this->app->instance(FifoServiceContract::class, $this->fifoService);
        $this->app->instance(WarehouseItemsRepositoryContract::class, $this->warehouseItemsRepository);
        $this->app->instance(AcceptanceItemsRepositoryContract::class, $this->acceptanceItemsRepository);
    }

    public function test_deletes_item_and_recalculates_fifo(): void
    {
        // Arrange
        $shipmentId = $this->faker()->uuid;

        $this->acceptanceItemsRepository
            ->shouldReceive('show')
            ->once()
            ->with($this->resourceId)
            ->andReturn($this->resource);

        $this->warehouseItemsRepository
            ->shouldReceive('getShipmentItemIdByAcceptanceAndProductIds')
            ->once()
            ->with($this->resource->acceptance_id, [$this->resource->product_id])
            ->andReturn($shipmentId);

        $this->acceptanceItemsRepository
            ->shouldReceive('delete')
            ->once()
            ->with($this->resourceId);

        $this->warehouseItemsRepository
            ->shouldReceive('deleteWhereAcceptanceAndProductIds')
            ->once()
            ->with($this->resource->acceptance_id, [$this->resource->product_id]);

        $this->fifoService
            ->shouldReceive('handle')
            ->once()
            ->with($shipmentId);

        // Act
        $job = new RecalculationAfterDeleteAcceptanceItemJob($this->resourceId);
        $job->handle();
    }

    public function test_handles_no_shipment_found(): void
    {
        // Arrange
        $this->acceptanceItemsRepository
            ->shouldReceive('show')
            ->once()
            ->with($this->resourceId)
            ->andReturn($this->resource);

        $this->warehouseItemsRepository
            ->shouldReceive('getShipmentItemIdByAcceptanceAndProductIds')
            ->once()
            ->with($this->resource->acceptance_id, [$this->resource->product_id])
            ->andReturn(null);

        $this->acceptanceItemsRepository
            ->shouldReceive('delete')
            ->once()
            ->with($this->resourceId);

        $this->warehouseItemsRepository
            ->shouldReceive('deleteWhereAcceptanceAndProductIds')
            ->once()
            ->with($this->resource->acceptance_id, [$this->resource->product_id]);

        $this->fifoService
            ->shouldNotReceive('handle');

        // Act
        $job = new RecalculationAfterDeleteAcceptanceItemJob($this->resourceId);
        $job->handle();
    }

    public function test_handles_resource_not_found(): void
    {
        // Arrange
        $this->acceptanceItemsRepository
            ->shouldReceive('show')
            ->once()
            ->with($this->resourceId)
            ->andReturn(null);

        $this->warehouseItemsRepository
            ->shouldNotReceive('getShipmentItemIdByAcceptanceAndProductIds');

        $this->acceptanceItemsRepository
            ->shouldNotReceive('delete');

        $this->warehouseItemsRepository
            ->shouldNotReceive('deleteWhereAcceptanceAndProductIds');

        $this->fifoService
            ->shouldNotReceive('handle');

        // Act
        $job = new RecalculationAfterDeleteAcceptanceItemJob($this->resourceId);
        $job->handle();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
