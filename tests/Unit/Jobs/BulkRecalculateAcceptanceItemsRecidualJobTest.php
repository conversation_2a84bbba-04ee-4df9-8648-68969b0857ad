<?php

namespace Tests\Unit\Jobs;

use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Jobs\BulkRecalculateAcceptanceItemsRecidualJob;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Models\Acceptance;
use App\Models\AcceptanceItem;
use Illuminate\Support\Collection;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\TestCase;
use Illuminate\Contracts\Container\BindingResolutionException;

class BulkRecalculateAcceptanceItemsRecidualJobTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private Collection $acceptances;
    private array $productIds;
    private AcceptanceRepositoryContract $acceptanceRepository;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем тестовые данные
        $this->acceptances = collect([
            Acceptance::factory()->create(),
            Acceptance::factory()->create()
        ]);

        $this->productIds = [
            Product::factory()->create()->id,
            Product::factory()->create()->id,
            Product::factory()->create()->id
        ];

        // Мокаем репозиторий
        $this->acceptanceRepository = Mockery::mock(AcceptanceRepositoryContract::class);
        app()->instance(AcceptanceRepositoryContract::class, $this->acceptanceRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_updates_acceptance_items_reciduals(): void
    {
        // Arrange
        $item1Id = $this->faker->uuid;
        $item2Id = $this->faker->uuid;

        $repositoryResponse = collect([
            (object)[
                'id' => $item1Id,
                'recidual' => 10,
                'updated_at' => now(),
                'acceptance_id' => $this->acceptances[0]->id,
                'product_id' => $this->productIds[0],
                'quantity' => 100,
                'price' => 10
            ],
            (object)[
                'id' => $item2Id,
                'recidual' => 20,
                'updated_at' => now(),
                'acceptance_id' => $this->acceptances[1]->id,
                'product_id' => $this->productIds[1],
                'quantity' => 200,
                'price' => 20
            ]
        ]);

        $this->acceptanceRepository->shouldReceive('getBulkNewestAcceptancesForReciduals')
            ->with($this->acceptances, $this->productIds)
            ->once()
            ->andReturn($repositoryResponse);

        // Act
        $job = new BulkRecalculateAcceptanceItemsRecidualJob($this->acceptances, $this->productIds);
        $job->handle();

        // Assert
        $this->assertDatabaseHas('acceptance_items', [
            'id' => $item1Id,
            'recidual' => 10
        ]);

        $this->assertDatabaseHas('acceptance_items', [
            'id' => $item2Id,
            'recidual' => 20
        ]);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_handles_empty_repository_response(): void
    {
        // Arrange
        $this->acceptanceRepository->shouldReceive('getBulkNewestAcceptancesForReciduals')
            ->with($this->acceptances, $this->productIds)
            ->once()
            ->andReturn(collect([]));

        // Act
        $job = new BulkRecalculateAcceptanceItemsRecidualJob($this->acceptances, $this->productIds);
        $job->handle();

        // Assert
        $this->assertDatabaseMissing('acceptance_items', [
            'acceptance_id' => $this->acceptances[0]->id
        ]);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_updates_existing_records(): void
    {
        // Arrange
        $existingItem = AcceptanceItem::factory()->create([
            'recidual' => 5
        ]);

        $repositoryResponse = collect([
            (object)[
                'id' => $existingItem->id,
                'recidual' => 15,
                'updated_at' => now(),
                'acceptance_id' => $existingItem->acceptance_id,
                'product_id' => $existingItem->product_id,
                'quantity' => $existingItem->quantity,
                'price' => $existingItem->price
            ]
        ]);

        $this->acceptanceRepository->shouldReceive('getBulkNewestAcceptancesForReciduals')
            ->with($this->acceptances, $this->productIds)
            ->once()
            ->andReturn($repositoryResponse);

        // Act
        $job = new BulkRecalculateAcceptanceItemsRecidualJob($this->acceptances, $this->productIds);
        $job->handle();

        // Assert
        $this->assertDatabaseHas('acceptance_items', [
            'id' => $existingItem->id,
            'recidual' => 15
        ]);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_uses_upsert_for_bulk_update(): void
    {
        // Arrange
        $itemId = $this->faker->uuid;
        $repositoryResponse = collect([
            (object)[
                'id' => $itemId,
                'recidual' => 10,
                'updated_at' => now(),
                'acceptance_id' => $this->acceptances[0]->id,
                'product_id' => $this->productIds[0],
                'quantity' => 100,
                'price' => 10
            ]
        ]);

        $this->acceptanceRepository->shouldReceive('getBulkNewestAcceptancesForReciduals')
            ->with($this->acceptances, $this->productIds)
            ->once()
            ->andReturn($repositoryResponse);

        // Act
        $job = new BulkRecalculateAcceptanceItemsRecidualJob($this->acceptances, $this->productIds);
        $job->handle();

        // Assert
        $this->assertDatabaseHas('acceptance_items', [
            'id' => $itemId,
            'recidual' => 10,
            'acceptance_id' => $this->acceptances[0]->id,
            'product_id' => $this->productIds[0],
            'quantity' => 100,
            'price' => 10
        ]);
    }
}
