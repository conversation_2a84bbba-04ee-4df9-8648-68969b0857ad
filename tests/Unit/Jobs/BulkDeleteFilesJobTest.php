<?php

namespace Tests\Unit\Jobs;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\FileRelation;
use App\Jobs\BulkDeleteFilesJob;
use App\Models\File;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BulkDeleteFilesJobTest extends TestCase
{
    use RefreshDatabase;

    use WithFaker;
    private array $relatedIds;
    private File $file1;
    private File $file2;

    protected function setUp(): void
    {
        parent::setUp();

        $this->relatedIds = [
            $this->faker->uuid(),
            $this->faker->uuid()
        ];

        // Используем реальный Storage для тестирования
        Storage::fake('s3-docs');

        // Создаем кабинет и сотрудника
        $cabinet = Cabinet::factory()->create();
        $employee = Employee::factory()->create();

        // Создаем тестовые файлы
        $this->file1 = File::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $employee->id,
            'path' => 'docs/test1.pdf',
            'name' => 'test1.pdf',
            'mime_type' => 'application/pdf'
        ]);

        $this->file2 = File::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $employee->id,
            'path' => 'docs/test2.pdf',
            'name' => 'test2.pdf',
            'mime_type' => 'application/pdf'
        ]);

        // Создаем связи с related_id
        FileRelation::factory()->create([
            'file_id' => $this->file1->id,
            'related_id' => $this->relatedIds[0]
        ]);

        FileRelation::factory()->create([
            'file_id' => $this->file2->id,
            'related_id' => $this->relatedIds[0]
        ]);

        // Создаем файлы в хранилище
        Storage::disk('s3-docs')->put($this->file1->path, 'test content 1');
        Storage::disk('s3-docs')->put($this->file2->path, 'test content 2');
    }

    protected function tearDown(): void
    {
        Storage::disk('s3-docs')->delete([$this->file1->path, $this->file2->path]);
        FileRelation::whereIn('related_id', $this->relatedIds)->delete();
        $this->file1->delete();
        $this->file2->delete();
        Mockery::close();
        parent::tearDown();
    }

    public function test_it_deletes_files_and_records_successfully(): void
    {
        // Act
        BulkDeleteFilesJob::dispatch($this->relatedIds);
        $this->artisan('queue:work --once');

        // Assert
        $this->assertFalse(Storage::disk('s3-docs')->exists($this->file1->path));
        $this->assertFalse(Storage::disk('s3-docs')->exists($this->file2->path));
        $this->assertDatabaseMissing('file_relations', ['related_id' => $this->relatedIds[0]]);
        $this->assertDatabaseMissing('files', ['id' => $this->file1->id]);
        $this->assertDatabaseMissing('files', ['id' => $this->file2->id]);
    }

    public function test_it_continues_processing_when_some_files_cannot_be_deleted(): void
    {
        // Arrange
        Storage::disk('s3-docs')->delete($this->file1->path);
        Log::shouldReceive('error')
            ->with('Failed to delete file from storage', [
                'file_path' => $this->file1->path,
                'related_id' => $this->relatedIds[0],
                'error' => 'File does not exist.'
            ]);

        // Act
        BulkDeleteFilesJob::dispatch($this->relatedIds);
        $this->artisan('queue:work --once');

        // Assert
        $this->assertFalse(Storage::disk('s3-docs')->exists($this->file2->path));
        $this->assertDatabaseMissing('file_relations', ['related_id' => $this->relatedIds[0]]);
        $this->assertDatabaseMissing('files', ['id' => $this->file1->id]);
        $this->assertDatabaseMissing('files', ['id' => $this->file2->id]);
    }

    public function test_it_logs_job_failure_with_context(): void
    {
        // Arrange
        $exception = new \Exception('Job failed');
        Log::shouldReceive('error')
            ->with('BulkDeleteFilesJob failed', [
                'related_ids' => $this->relatedIds,
                'error' => 'Job failed',
                'trace' => $exception->getTraceAsString()
            ]);

        // Act
        $job = new BulkDeleteFilesJob($this->relatedIds);
        $job->failed($exception);

        $this->assertTrue(true);
    }

    public function test_it_has_appropriate_retry_settings(): void
    {
        // Arrange
        $job = new BulkDeleteFilesJob($this->relatedIds);

        // Assert
        $this->assertEquals(3, $job->tries);
        $this->assertEquals(3, $job->maxExceptions);
        $this->assertEquals(60, $job->backoff);
    }
}
