<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use RuntimeException;
use App\Jobs\GenerateCalendarJob;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class GenerateCalendarJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    public function test_if_schedule_not_found_throws_exception(): void
    {
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Schedule not found');

        $job = new GenerateCalendarJob($this->faker()->uuid);
        $job->handle();
    }
}
