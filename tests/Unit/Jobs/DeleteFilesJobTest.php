<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use App\Jobs\DeleteFilesJob;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Contracts\Repositories\FilesRepositoryContract;
use Mockery;

class DeleteFilesJobTest extends TestCase
{
    use RefreshDatabase;

    private FilesRepositoryContract $filesRepository;
    private string $relatedId;
    private array $files;

    protected function setUp(): void
    {
        parent::setUp();

        $this->filesRepository = Mockery::mock(FilesRepositoryContract::class);
        app()->instance(FilesRepositoryContract::class, $this->filesRepository);

        $this->relatedId = 'test-related-id';
        $this->files = [
            (object)[
                'id' => 'file-1',
                'path' => 'test/path/file1.pdf'
            ],
            (object)[
                'id' => 'file-2',
                'path' => 'test/path/file2.pdf'
            ]
        ];

        // Подготавливаем фейковое хранилище
        Storage::fake('s3-docs');
    }

    public function test_it_deletes_multiple_files_from_storage_and_database(): void
    {
        // Arrange
        foreach ($this->files as $file) {
            Storage::disk('s3-docs')->put($file->path, 'test content');
        }

        $this->filesRepository
            ->shouldReceive('getByRelatedId')
            ->once()
            ->with($this->relatedId)
            ->andReturn(collect($this->files));

        $this->filesRepository
            ->shouldReceive('deleteByRelatedId')
            ->once()
            ->with($this->relatedId)
            ->andReturn(true);

        // Act
        $job = new DeleteFilesJob($this->relatedId);
        $job->handle();

        // Assert
        foreach ($this->files as $file) {
            Storage::disk('s3-docs')->assertMissing($file->path);
        }
    }

    public function test_it_handles_no_files_found(): void
    {
        // Arrange
        $this->filesRepository
            ->shouldReceive('getByRelatedId')
            ->once()
            ->with($this->relatedId)
            ->andReturn(collect([]));

        $this->filesRepository
            ->shouldReceive('deleteByRelatedId')
            ->once()
            ->with($this->relatedId)
            ->andReturn(true);

        // Act
        $job = new DeleteFilesJob($this->relatedId);
        $job->handle();

        // Assert
        // Проверка вызовов методов уже выполнена через mockery
    }

    public function test_it_deletes_from_database_even_if_files_missing_in_storage(): void
    {
        // Arrange
        $this->filesRepository
            ->shouldReceive('getByRelatedId')
            ->once()
            ->with($this->relatedId)
            ->andReturn(collect($this->files));

        $this->filesRepository
            ->shouldReceive('deleteByRelatedId')
            ->once()
            ->with($this->relatedId)
            ->andReturn(true);

        // Act
        $job = new DeleteFilesJob($this->relatedId);
        $job->handle();

        // Assert
        // Проверяем что записи удалены из БД даже если файлов не было в хранилище
        // (это уже проверено в mockery)
    }
}
