<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use App\Jobs\DeleteFileJob;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Contracts\Repositories\FilesRepositoryContract;
use Mockery;
use Illuminate\Contracts\Container\BindingResolutionException;

class DeleteFileJobTest extends TestCase
{
    use RefreshDatabase;

    private FilesRepositoryContract $filesRepository;
    private string $fileId;
    private object $file;

    protected function setUp(): void
    {
        parent::setUp();

        $this->filesRepository = Mockery::mock(FilesRepositoryContract::class);
        app()->instance(FilesRepositoryContract::class, $this->filesRepository);

        $this->fileId = 'test-file-id';
        $this->file = (object)[
            'id' => $this->fileId,
            'path' => 'test/path/file.pdf'
        ];

        // Подготавливаем фейковое хранилище
        Storage::fake('s3-docs');
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_deletes_file_from_storage_and_database(): void
    {
        // Arrange
        Storage::disk('s3-docs')->put($this->file->path, 'test content');

        $this->filesRepository
            ->shouldReceive('getById')
            ->once()
            ->with($this->fileId)
            ->andReturn($this->file);

        $this->filesRepository
            ->shouldReceive('delete')
            ->once()
            ->with($this->fileId)
            ->andReturn(true);

        // Act
        $job = new DeleteFileJob($this->fileId);
        $job->handle();

        // Assert
        Storage::disk('s3-docs')->assertMissing($this->file->path);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_handles_missing_file(): void
    {
        // Arrange
        $this->filesRepository
            ->shouldReceive('getById')
            ->once()
            ->with($this->fileId)
            ->andReturn(null);

        $this->filesRepository
            ->shouldNotReceive('delete');

        // Act
        $job = new DeleteFileJob($this->fileId);
        $job->handle();

        // Assert
        // Проверяем что метод delete не вызывался (это уже проверено в mockery)
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_it_deletes_from_database_even_if_file_missing_in_storage(): void
    {
        // Arrange
        $this->filesRepository
            ->shouldReceive('getById')
            ->once()
            ->with($this->fileId)
            ->andReturn($this->file);

        $this->filesRepository
            ->shouldReceive('delete')
            ->once()
            ->with($this->fileId)
            ->andReturn(true);

        // Act
        $job = new DeleteFileJob($this->fileId);
        $job->handle();

        // Assert
        // Проверяем что запись удалена из БД даже если файла не было в хранилище
        // (это уже проверено в mockery)
    }
}
