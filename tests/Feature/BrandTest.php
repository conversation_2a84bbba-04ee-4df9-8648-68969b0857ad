<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BrandTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_cannot_get_brands_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/brands');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_brands_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => 'not-a-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_brands_from_other_cabinet(): void
    {
        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_get_brands_list(): void
    {
        // Arrange
        // Создаем бренды для нашего кабинета
        Brand::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем бренд для другого кабинета
        Brand::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'cabinet_id',
                        'name',
                        'created_at',
                        'updated_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только бренды нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_can_get_brands_with_pagination(): void
    {
        // Arrange
        Brand::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(15, $response->json('meta.total'));
        $this->assertCount(5, $response->json('data'));
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        // Arrange
        Brand::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'name']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        // Act
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_can_get_brands_with_sorting(): void
    {
        // Arrange
        $brands = [
            'A brand' => Brand::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'A brand'
            ]),
            'B brand' => Brand::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'B brand'
            ]),
            'C brand' => Brand::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'C brand'
            ])
        ];

        // Act - получаем отсортированный по имени список
        $response = $this->getJson('/api/internal/brands?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200);

        $names = collect($response->json('data'))->pluck('name')->values();
        $expectedNames = collect(['C brand', 'B brand', 'A brand']);

        $this->assertEquals($expectedNames, $names);
    }

    public function test_cannot_create_brand_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/brands', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name'
            ]);
    }

    public function test_cannot_create_brand_with_invalid_data(): void
    {
        // Act
        $response = $this->postJson('/api/internal/brands', [
            'cabinet_id' => 'not-a-uuid',
            'name' => str_repeat('a', 256), // Превышает максимальную длину в 255 символов
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name'
            ]);
    }

    public function test_cannot_create_brand_for_other_cabinet(): void
    {
        // Act
        $response = $this->postJson('/api/internal/brands', [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $this->faker->company()
        ]);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_create_brand(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->company()
        ];

        // Act
        $response = $this->postJson('/api/internal/brands', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('brands', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name']
        ]);
    }

    public function test_can_update_brand(): void
    {
        // Arrange
        $brand = Brand::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Old Name'
        ]);

        $data = [
            'name' => 'New Brand Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/brands/{$brand->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('brands', [
            'id' => $brand->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => $data['name']
        ]);
    }

    public function test_cannot_update_brand_from_other_cabinet(): void
    {
        // Arrange
        $otherBrand = Brand::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => 'New Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/brands/{$otherBrand->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('brands', [
            'id' => $otherBrand->id,
            'name' => $otherBrand->name // Проверяем что имя не изменилось
        ]);
    }

    public function test_cannot_update_brand_with_invalid_data(): void
    {
        // Arrange
        $brand = Brand::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => str_repeat('a', 256) // Превышает максимальную длину в 255 символов
        ];

        // Act
        $response = $this->putJson("/api/internal/brands/{$brand->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);

        $this->assertDatabaseHas('brands', [
            'id' => $brand->id,
            'name' => $brand->name // Проверяем что имя не изменилось
        ]);
    }

    public function test_cannot_update_non_existent_brand(): void
    {
        // Act
        $response = $this->putJson("/api/internal/brands/" . $this->faker->uuid(), [
            'name' => 'New Name'
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_brand(): void
    {
        // Arrange
        $brand = Brand::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/brands/{$brand->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'cabinet_id',
                'name',
                'created_at',
                'updated_at'
            ]);

        $this->assertEquals($brand->id, $response->json('id'));
        $this->assertEquals($brand->name, $response->json('name'));
        $this->assertEquals($this->cabinet->id, $response->json('cabinet_id'));
    }

    public function test_cannot_show_brand_from_other_cabinet(): void
    {
        // Arrange
        $otherBrand = Brand::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/brands/{$otherBrand->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_brand(): void
    {
        // Act
        $response = $this->getJson("/api/internal/brands/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_brand(): void
    {
        // Arrange
        $brand = Brand::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/brands/{$brand->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('brands', [
            'id' => $brand->id
        ]);
    }

    public function test_cannot_delete_brand_from_other_cabinet(): void
    {
        // Arrange
        $otherBrand = Brand::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/brands/{$otherBrand->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('brands', [
            'id' => $otherBrand->id
        ]);
    }

    public function test_cannot_delete_non_existent_brand(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/brands/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }
}
