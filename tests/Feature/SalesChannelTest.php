<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\SalesChannel;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\SalesChannelType;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SalesChannelTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Employee $employee;
    private Department $department;
    private Cabinet $otherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->otherCabinet = Cabinet::factory()->create();

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    /**
     * Тест получения списка каналов продаж
     */
    public function test_can_get_sales_channels_list(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $salesChannels = SalesChannel::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Выполнение запроса
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Проверка результатов
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'name',
                        'description',
                        'cabinet_id',
                        'sales_channel_type_id',
                        'employee_id',
                        'department_id',
                        'is_common',
                        'archived_at'
                    ],
                ],
                'meta'
            ]);

        $data = $response->json('data');
        $this->assertCount(3, $data);
        $this->assertTrue(collect($data)->contains('id', $salesChannels[0]->id));
        $this->assertTrue(collect($data)->contains('id', $salesChannels[1]->id));
        $this->assertTrue(collect($data)->contains('id', $salesChannels[2]->id));
    }

    /**
     * Тест валидации - отсутствие cabinet_id
     */
    public function test_cannot_get_sales_channels_without_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/sales-channels');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    /**
     * Тест валидации - некорректный формат cabinet_id
     */
    public function test_cannot_get_sales_channels_with_invalid_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => 'invalid-uuid'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    /**
     * Тест на мультитенантность - запрет доступа к данным другого кабинета
     */
    public function test_cannot_get_sales_channels_from_other_cabinet(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        // Создаем каналы продаж в нашем кабинете
        SalesChannel::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Создаем каналы продаж в другом кабинете
        SalesChannel::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'sales_channel_type_id' => $type->id
        ]);

        // Выполнение запроса к чужому кабинету
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Проверка запрета доступа
        $response->assertStatus(403);
    }

    /**
     * Тест фильтрации по имени
     */
    public function test_index_filter_by_name(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'name' => 'Test Channel'
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'name' => 'Other Channel'
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'name' => [
                    'value' => 'Test'
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием IN для поля description
     */
    public function test_index_filter_description_in(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => 'Test Description'
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => 'Other Description'
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'value' => 'Test',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием NOT_IN для поля description
     */
    public function test_index_filter_description_not_in(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => 'Test Description'
        ]);

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => 'Other Description'
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'value' => 'Test',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием EMPTY для поля description
     */
    public function test_index_filter_description_empty(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => null
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => 'Test Description'
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием NOT_EMPTY для поля description
     */
    public function test_index_filter_description_not_empty(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => 'Test Description'
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'description' => null
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием IN для поля employee_owners
     */
    public function test_index_filter_employee_owners_in(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $otherEmployee = Employee::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $otherEmployee->id
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием NOT_IN для поля employee_owners
     */
    public function test_index_filter_employee_owners_not_in(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $otherEmployee = Employee::factory()->create();

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id
        ]);

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $otherEmployee->id
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$this->employee->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием EMPTY для поля employee_owners
     */
    public function test_index_filter_employee_owners_empty(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(0, $data);
    }

    /**
     * Тест фильтрации с условием NOT_EMPTY для поля employee_owners
     */
    public function test_index_filter_employee_owners_not_empty(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(2, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест пагинации
     */
    public function test_index_pagination(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        SalesChannel::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id
        ]);

        // Выполнение запроса с параметрами пагинации
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 10
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(10, $data);
        $this->assertEquals(15, $response->json('meta.total'));
        $this->assertEquals(2, $response->json('meta.last_page'));
    }

    /**
     * Тест сортировки
     */
    public function test_index_sorting(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $channel1 = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'name' => 'A Channel'
        ]);

        $channel2 = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'name' => 'B Channel'
        ]);

        $channel3 = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'name' => 'C Channel'
        ]);

        // Сортировка по имени в обратном порядке
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals($channel3->id, $data[0]['id']);
        $this->assertEquals($channel2->id, $data[1]['id']);
        $this->assertEquals($channel1->id, $data[2]['id']);
    }

    /**
     * Тест фильтрации с выбранными полями
     */
    public function test_index_selected_fields(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Выполнение запроса только с выбранными полями
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'name', 'description']
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data.0');

        // Проверяем наличие запрошенных полей
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('name', $data);
        $this->assertArrayHasKey('description', $data);

        // Проверяем отсутствие других полей
        $this->assertArrayNotHasKey('employee_id', $data);
        $this->assertArrayNotHasKey('department_id', $data);
        $this->assertArrayNotHasKey('sales_channel_type_id', $data);
    }

    /**
     * Тест фильтрации с условием EMPTY для поля department_owners
     */
    public function test_index_filter_department_owners_empty(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'department_id' => $this->department->id
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(0, $data);
    }

    /**
     * Тест фильтрации с условием NOT_EMPTY для поля department_owners
     */
    public function test_index_filter_department_owners_not_empty(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'department_id' => $this->department->id
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(2, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием IN для поля department_owners
     */
    public function test_index_filter_department_owners_in(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $otherDepartment = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'department_id' => $this->department->id
        ]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'department_id' => $otherDepartment->id
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест фильтрации с условием NOT_IN для поля department_owners
     */
    public function test_index_filter_department_owners_not_in(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $otherDepartment = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $nonMatchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'department_id' => $this->department->id
        ]);

        $matchingChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'department_id' => $otherDepartment->id
        ]);

        // Выполнение запроса с фильтром
        $response = $this->getJson('/api/internal/sales-channels?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$this->department->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Проверка результатов
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($matchingChannel->id, $data[0]['id']);
    }

    /**
     * Тест создания канала продаж в чужом кабинете (проверка мультитенантности)
     */
    public function test_cannot_create_sales_channel_in_other_cabinet(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $data = [
            'cabinet_id' => $this->otherCabinet->id,  // Чужой кабинет
            'name' => 'Test Sales Channel',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Выполнение запроса
        $response = $this->postJson('/api/internal/sales-channels', $data);

        // Проверка результатов - доступ запрещен
        $response->assertStatus(403);
    }

    /**
     * Комплексный тест валидации при создании канала продаж
     */
    public function test_sales_channel_validation_rules(): void
    {
        $type = SalesChannelType::factory()->create();

        // Проверка пустого запроса
        $response = $this->postJson('/api/internal/sales-channels', []);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'name', 'sales_channel_type_id', 'employee_id', 'department_id']);

        // Проверка некорректных UUID
        $response = $this->postJson('/api/internal/sales-channels', [
            'cabinet_id' => 'invalid-uuid',
            'name' => 'Test Sales Channel',
            'sales_channel_type_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'sales_channel_type_id', 'employee_id', 'department_id']);

        // Проверка пустого имени
        $response = $this->postJson('/api/internal/sales-channels', [
            'cabinet_id' => $this->cabinet->id,
            'name' => '',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);

        // Проверка несуществующего типа
        $response = $this->postJson('/api/internal/sales-channels', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Sales Channel',
            'sales_channel_type_id' => '00000000-0000-0000-0000-000000000000',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sales_channel_type_id']);

        // Проверка некорректного значения sort
        $response = $this->postJson('/api/internal/sales-channels', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Sales Channel',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sort' => 'not-a-number',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sort']);
    }

    /**
     * Тест создания канала продаж с минимальным набором полей
     */
    public function test_can_create_sales_channel_with_minimum_fields(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $minimalData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Sales Channel',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Выполнение запроса
        $response = $this->postJson('/api/internal/sales-channels', $minimalData);

        // Проверка результатов
        $response->assertCreated()
            ->assertJsonStructure(['id']);

        // Проверка, что запись создана в БД
        $this->assertDatabaseHas('sales_channels', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Sales Channel',
            'sales_channel_type_id' => $type->id,
        ]);
    }

    /**
     * Тест создания канала продаж со всеми полями
     */
    public function test_can_create_sales_channel_with_all_fields(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();

        $fullData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Sales Channel',
            'description' => 'Detailed description of the sales channel',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sort' => 100,
            'is_common' => true
        ];

        // Выполнение запроса
        $response = $this->postJson('/api/internal/sales-channels', $fullData);

        // Проверка результатов
        $response->assertCreated();

        // Проверка, что данные сохранены в БД
        $this->assertDatabaseHas('sales_channels', [
            'id' => $response->json('id'),
            'name' => 'Test Sales Channel',
            'description' => 'Detailed description of the sales channel',
            'sort' => 100,
            'is_common' => true
        ]);
    }

    /**
     * Тест обновления канала продаж с минимальным набором полей
     */
    public function test_can_update_sales_channel_with_minimum_fields(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'name' => 'Initial Channel Name',
        ]);

        $minimalUpdateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Channel Name',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Выполнение запроса
        $response = $this->putJson("/api/internal/sales-channels/{$salesChannel->id}", $minimalUpdateData);

        // Проверка результатов
        $response->assertNoContent();

        // Проверка данных в БД
        $this->assertDatabaseHas('sales_channels', [
            'id' => $salesChannel->id,
            'name' => 'Updated Channel Name',
            'sales_channel_type_id' => $type->id
        ]);

        // Проверка, что остальные поля не изменились
        $updatedChannel = SalesChannel::find($salesChannel->id);
        $this->assertEquals($type->id, $updatedChannel->sales_channel_type_id);
        $this->assertEquals($this->employee->id, $updatedChannel->employee_id);
    }

    /**
     * Тест успешного обновления канала продаж со всеми полями
     */
    public function test_can_update_sales_channel_with_all_fields(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $newType = SalesChannelType::factory()->create();

        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'name' => 'Initial Name',
            'description' => 'Initial Description',
            'sort' => 10,
            'is_common' => false
        ]);

        $fullUpdateData = [
            'name' => 'Completely Updated Name',
            'description' => 'Completely Updated Description',
            'sales_channel_type_id' => $newType->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sort' => 50,
            'is_common' => true
        ];

        // Выполнение запроса
        $response = $this->putJson("/api/internal/sales-channels/{$salesChannel->id}", $fullUpdateData);

        // Проверка результатов
        $response->assertNoContent();

        // Проверка данных в БД
        $this->assertDatabaseHas('sales_channels', [
            'id' => $salesChannel->id,
            'name' => 'Completely Updated Name',
            'description' => 'Completely Updated Description',
            'sales_channel_type_id' => $newType->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sort' => 50,
            'is_common' => true
        ]);
    }

    /**
     * Тест получения канала продаж по ID (show)
     */
    public function test_can_show_sales_channel(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Выполнение запроса
        $response = $this->getJson("/api/internal/sales-channels/{$salesChannel->id}?" . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Проверка результатов
        $response->assertStatus(200)
            ->assertJsonStructure([
                    'id',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'name',
                    'description',
                    'cabinet_id',
                    'sales_channel_type_id',
                    'employee_id',
                    'department_id',
                    'is_common',
                    'archived_at'
            ]);

        $data = $response->json();
        $this->assertEquals($salesChannel->id, $data['id']);
        $this->assertEquals($salesChannel->name, $data['name']);
        $this->assertEquals($this->cabinet->id, $data['cabinet_id']);
    }

    /**
     * Тест получения несуществующего канала продаж
     */
    public function test_cannot_show_nonexistent_sales_channel(): void
    {
        // Выполнение запроса с несуществующим ID
        $nonExistentId = '00000000-0000-0000-0000-000000000000';
        $response = $this->getJson("/api/internal/sales-channels/{$nonExistentId}?" . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Проверка результатов (ожидаем 404 Not Found)
        $response->assertStatus(404);
    }

    /**
     * Тест запрета доступа к чужому каналу продаж
     */
    public function test_cannot_show_sales_channel_from_other_cabinet(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $otherCabinetChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'sales_channel_type_id' => $type->id
        ]);

        // Выполнение запроса
        $response = $this->getJson("/api/internal/sales-channels/{$otherCabinetChannel->id}?" . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Проверка результатов (ожидаем 403 Forbidden)
        $response->assertStatus(404);
    }

    /**
     * Комплексный тест валидации при обновлении канала продаж
     */
    public function test_update_sales_channel_validation_rules(): void
    {
        $type = SalesChannelType::factory()->create();
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        // Проверка пустого запроса
        $response = $this->putJson("/api/internal/sales-channels/{$salesChannel->id}", []);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'sales_channel_type_id', 'employee_id', 'department_id']);

        // Проверка некорректных UUID
        $response = $this->putJson("/api/internal/sales-channels/{$salesChannel->id}", [
            'cabinet_id' => 'invalid-uuid',
            'name' => 'Test Name',
            'sales_channel_type_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sales_channel_type_id', 'employee_id', 'department_id']);

        // Проверка пустого имени
        $response = $this->putJson("/api/internal/sales-channels/{$salesChannel->id}", [
            'cabinet_id' => $this->cabinet->id,
            'name' => '',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);

        // Проверка несуществующего типа
        $response = $this->putJson("/api/internal/sales-channels/{$salesChannel->id}", [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Name',
            'sales_channel_type_id' => '00000000-0000-0000-0000-000000000000',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sales_channel_type_id']);

        // Проверка некорректного значения sort
        $response = $this->putJson("/api/internal/sales-channels/{$salesChannel->id}", [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Name',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sort' => 'not-a-number',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sort']);
    }

    /**
     * Тест запрета обновления канала продаж из другого кабинета
     */
    public function test_cannot_update_sales_channel_from_other_cabinet(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $otherCabinetChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'sales_channel_type_id' => $type->id
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Name',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Выполнение запроса
        $response = $this->putJson("/api/internal/sales-channels/{$otherCabinetChannel->id}", $updateData);

        // Проверка результатов (ожидаем 403 Forbidden)
        $response->assertStatus(404);
    }

    /**
     * Тест обновления несуществующего канала продаж
     */
    public function test_cannot_update_nonexistent_sales_channel(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $nonExistentId = '00000000-0000-0000-0000-000000000000';

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Name',
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Выполнение запроса
        $response = $this->putJson("/api/internal/sales-channels/{$nonExistentId}", $updateData);

        // Проверка результатов (ожидаем 404 Not Found)
        $response->assertStatus(404);
    }

    /**
     * Тест успешного удаления канала продаж
     */
    public function test_can_delete_sales_channel(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        // Выполнение запроса
        $response = $this->deleteJson("/api/internal/sales-channels/{$salesChannel->id}?" . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Проверка результатов
        $response->assertStatus(204);

        // Проверка, что запись удалена (soft delete)
        $this->assertSoftDeleted('sales_channels', [
            'id' => $salesChannel->id
        ]);
    }

    /**
     * Тест запрета удаления канала продаж из другого кабинета
     */
    public function test_cannot_delete_sales_channel_from_other_cabinet(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $otherCabinetChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'sales_channel_type_id' => $type->id
        ]);

        // Выполнение запроса
        $response = $this->deleteJson("/api/internal/sales-channels/{$otherCabinetChannel->id}?" . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Проверка результатов (ожидаем 403 Forbidden)
        $response->assertStatus(404);

        // Проверка, что запись не удалена
        $this->assertDatabaseHas('sales_channels', [
            'id' => $otherCabinetChannel->id,
            'deleted_at' => null
        ]);
    }

    /**
     * Тест удаления несуществующего канала продаж
     */
    public function test_cannot_delete_nonexistent_sales_channel(): void
    {
        $nonExistentId = '00000000-0000-0000-0000-000000000000';

        // Выполнение запроса
        $response = $this->deleteJson("/api/internal/sales-channels/{$nonExistentId}?" . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Проверка результатов (ожидаем 404 Not Found)
        $response->assertStatus(404);
    }

    /**
     * Тест массового удаления каналов продаж
     */
    public function test_can_bulk_delete_sales_channels(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $salesChannels = SalesChannel::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        $ids = $salesChannels->pluck('id')->toArray();

        // Выполнение запроса
        $response = $this->deleteJson("/api/internal/sales-channels/bulk-delete", [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $ids
        ]);

        // Проверка результатов
        $response->assertStatus(204);

        // Проверка, что записи удалены (soft delete)
        foreach ($ids as $id) {
            $this->assertDatabaseMissing('sales_channels', [
                'id' => $id
            ]);
        }
    }

    /**
     * Тест архивации канала продаж
     */
    public function test_can_archive_sales_channel(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'archived_at' => null
        ]);

        // Выполнение запроса
        $response = $this->postJson("/api/internal/sales-channels/archive", [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$salesChannel->id]
        ]);

        // Проверка результатов
        $response->assertNoContent();

        // Проверка, что запись архивирована
        $this->assertDatabaseHas('sales_channels', [
            'id' => $salesChannel->id,
        ]);

        // Проверяем, что поле archived_at не null
        $updatedChannel = SalesChannel::find($salesChannel->id);
        $this->assertNotNull($updatedChannel->archived_at);
    }

    /**
     * Тест разархивации канала продаж
     */
    public function test_can_unarchive_sales_channel(): void
    {
        // Подготовка данных
        $type = SalesChannelType::factory()->create();
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_type_id' => $type->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'archived_at' => now() // архивируем при создании
        ]);

        // Выполнение запроса
        $response = $this->postJson("/api/internal/sales-channels/unarchive", [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$salesChannel->id]
        ]);

        // Проверка результатов
        $response->assertNoContent();

        // Проверка, что запись разархивирована
        $this->assertDatabaseHas('sales_channels', [
            'id' => $salesChannel->id,
        ]);

        // Проверяем, что поле archived_at is null
        $updatedChannel = SalesChannel::find($salesChannel->id);
        $this->assertNull($updatedChannel->archived_at);
    }
}
