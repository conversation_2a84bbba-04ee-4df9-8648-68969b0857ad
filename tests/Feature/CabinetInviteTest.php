<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\CabinetInviteStatusEnum;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetInvite;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Role;
use App\Models\User;
use App\Services\Api\Internal\Workspace\CabinetInviteService\Jobs\SendCabinetInviteJob;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class CabinetInviteTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        Queue::fake();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'use_bin' => false
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_cabinet_invites(): void
    {
        // Arrange
        $invites = CabinetInvite::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CabinetInvite::factory()->create();

        // Act
        $response = $this->getJson('/api/internal/invites?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'email',
                        'status',
                        'token',
                        'role_id'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertCount(3, $response->json('data'));
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/invites?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
            ]);
    }

    public function test_can_filter_cabinet_invites_by_fields(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>',
        ]);

        // Act
        $response = $this->getJson('/api/internal/invites?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['email'],
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonFragment([
                'email' => '<EMAIL>',
            ]);
    }

    public function test_cannot_access_invites_from_other_cabinet(): void
    {
        // Arrange
        $invites = CabinetInvite::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/invites?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_sort_cabinet_invites(): void
    {
        // Arrange
        $invite1 = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>',
        ]);
        $invite2 = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>',
        ]);

        // Act
        $response = $this->getJson('/api/internal/invites?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'email',
            'sortDirection' => 'asc',
        ]));

        // Assert
        $response->assertStatus(200);

        $emails = collect($response->json('data'))->pluck('email')->values();
        $this->assertEquals(collect(['<EMAIL>', '<EMAIL>']), $emails);
    }

    public function test_can_paginate_cabinet_invites(): void
    {
        // Arrange
        CabinetInvite::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/invites?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 10,
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'email',
                        'status',
                        'token',
                        'role_id'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertCount(10, $response->json('data'));
        $this->assertEquals(1, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
    }

    public function test_can_store_cabinet_invite(): void
    {
        // Arrange
        $defaultDepartment = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $role = Role::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>',
            'role_id' => $role->id,
            'department_id' => $defaultDepartment->id
        ];

        // Act
        $response = $this->postJson('/api/internal/invites', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('cabinet_invites', [
            'cabinet_id' => $data['cabinet_id'],
            'email' => $data['email'],
            'role_id' => $data['role_id'],
        ]);

        Queue::assertPushed(SendCabinetInviteJob::class);
    }

    public function test_can_store_cabinet_invite_with_minimal_data(): void
    {
        // Arrange
        $defaultDepartment = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_default' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>',
        ];

        // Act
        $response = $this->postJson('/api/internal/invites', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('cabinet_invites', $data);

        Queue::assertPushed(SendCabinetInviteJob::class);
    }

    public function test_store_cabinet_invite_validation_errors(): void
    {
        // Act
        $response = $this->postJson('/api/internal/invites', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'email',
            ]);
    }

    public function test_cannot_store_cabinet_invite_for_other_cabinet(): void
    {
        // Arrange
        $role = Role::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'email' => '<EMAIL>',
            'role_id' => $role->id,
        ];

        // Act
        $response = $this->postJson('/api/internal/invites', $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseMissing('cabinet_invites', [
            'cabinet_id' => $data['cabinet_id'],
            'email' => $data['email'],
        ]);

        Queue::assertNotPushed(SendCabinetInviteJob::class);
    }

    public function test_can_accept_cabinet_invite(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => $this->faker->email
        ]);

        $user = User::factory()->create(['email' => $invite->email]);

        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->postJson('/api/internal/invites/'. $invite->id, [
            'token' => 'validtoken123456',
        ]);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
            'status' => 'accepted',
        ]);

        $this->assertDatabaseHas('employees', [
            'user_id' => $user->id,
            'email' => $invite->email
        ]);
        $employee = Employee::query()
            ->where('user_id', $user->id)
            ->first();

        $this->assertDatabaseHas('cabinet_employee', [
            'cabinet_id' => $invite->cabinet_id,
            'employee_id' => $employee->id,
        ]);

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
            'status' => CabinetInviteStatusEnum::ACCEPTED->value
        ]);
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'current_cabinet_id' => $invite->cabinet_id
        ]);
    }

    public function test_accept_cabinet_invite_validation_errors(): void
    {
        // Act
        $response = $this->postJson('/api/internal/invites/'. $this->faker->uuid, [
            'token' => 'short',
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['token']);
    }

    public function test_cannot_accept_non_existent_cabinet_invite(): void
    {
        // Act
        $response = $this->postJson('/api/internal/invites/'. $this->faker->uuid, [
            'token' => 'nonexistenttoken',
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_accept_invite_with_foreign_token(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => '<EMAIL>'
        ]);

        $otherUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->actingAs($otherUser, 'sanctum');

        // Act
        $response = $this->postJson('/api/internal/invites/' . $invite->id, [
            'token' => 'validtoken123456',
        ]);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
            'status' => CabinetInviteStatusEnum::WAITING->value,
        ]);

        $this->assertDatabaseMissing('employees', [
            'user_id' => $otherUser->id
        ]);
    }

    public function test_cannot_accept_invite_not_in_waiting_status(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::ACCEPTED->value,
            'email' => $this->faker->email
        ]);

        $user = User::factory()->create(['email' => $invite->email]);
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->postJson('/api/internal/invites/' . $invite->id, [
            'token' => 'validtoken123456',
        ]);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
            'status' => CabinetInviteStatusEnum::ACCEPTED->value,
        ]);
    }

    public function test_can_decline_cabinet_invite(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => $this->faker->email
        ]);

        $user = User::factory()->create(['email' => $invite->email]);
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->postJson('/api/internal/invites/' . $invite->id . '/decline', [
            'token' => 'validtoken123456',
        ]);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
            'status' => CabinetInviteStatusEnum::DECLINED->value,
        ]);
    }

    public function test_cannot_decline_non_existent_cabinet_invite(): void
    {
        // Act
        $response = $this->postJson('/api/internal/invites/' . $this->faker->uuid . '/decline', [
            'token' => 'nonexistenttoken',
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_decline_invite_not_in_waiting_status(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::ACCEPTED->value,
            'email' => $this->faker->email
        ]);

        $user = User::factory()->create(['email' => $invite->email]);
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->postJson('/api/internal/invites/' . $invite->id . '/decline', [
            'token' => 'validtoken123456',
        ]);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
            'status' => CabinetInviteStatusEnum::ACCEPTED->value,
        ]);
    }

    public function test_cannot_decline_invite_for_other_user(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => '<EMAIL>'
        ]);

        $otherUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->actingAs($otherUser, 'sanctum');

        // Act
        $response = $this->postJson('/api/internal/invites/' . $invite->id . '/decline', [
            'token' => 'validtoken123456',
        ]);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
            'status' => CabinetInviteStatusEnum::WAITING->value,
        ]);
    }

    public function test_can_get_received_invites(): void
    {
        // Arrange
        $user = User::factory()->create(['email' => '<EMAIL>']);
        $this->actingAs($user, 'sanctum');

        $invite1 = CabinetInvite::factory()->create([
            'email' => '<EMAIL>',
            'status' => CabinetInviteStatusEnum::WAITING->value,
        ]);

        $invite2 = CabinetInvite::factory()->create([
            'email' => '<EMAIL>',
            'status' => CabinetInviteStatusEnum::WAITING->value,
        ]);

        // Act
        $response = $this->getJson('/api/internal/invites/received');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(2)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'email',
                    'status',
                    'token',
                    'created_at',
                    'updated_at',
                    'cabinet_name',
                    'employee_name',
                ]
            ])
            ->assertJson([['email' => '<EMAIL>']]);
    }

    public function test_cannot_get_received_invites_for_other_user(): void
    {
        // Arrange
        $user = User::factory()->create(['email' => '<EMAIL>']);
        $this->actingAs($user, 'sanctum');

        $invite = CabinetInvite::factory()->create([
            'email' => '<EMAIL>',
            'status' => CabinetInviteStatusEnum::WAITING->value,
        ]);

        // Act
        $response = $this->getJson('/api/internal/invites/received');

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(0);
    }

    public function test_can_show_cabinet_invite(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => $this->faker->email
        ]);

        $user = User::factory()->create(['email' => $invite->email]);
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->getJson('/api/internal/invites/' . $invite->token);

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'employee_id',
                'department_id',
                'email',
                'status',
                'token',
                'role_id',
                'cabinet_name'
            ]);
    }

    public function test_cannot_show_invite_for_other_user(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => '<EMAIL>'
        ]);

        $otherUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->actingAs($otherUser, 'sanctum');

        // Act
        $response = $this->getJson('/api/internal/invites/' . $invite->token);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_invite(): void
    {
        // Act
        $response = $this->getJson('/api/internal/invites/nonexistenttoken');

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_destroy_cabinet_invite(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => $this->faker->email
        ]);

        // Act
        $response = $this->deleteJson('/api/internal/invites/' . $invite->id);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('cabinet_invites', [
            'id' => $invite->id,
        ]);
    }

    public function test_cannot_destroy_invite_for_other_user(): void
    {
        // Arrange
        $invite = CabinetInvite::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'token' => 'validtoken123456',
            'status' => CabinetInviteStatusEnum::WAITING->value,
            'email' => '<EMAIL>'
        ]);
        // Act
        $response = $this->deleteJson('/api/internal/invites/' . $invite->id);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('cabinet_invites', [
            'id' => $invite->id,
        ]);
    }

    public function test_cannot_destroy_non_existent_invite(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/invites/' . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }
}
