<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\CabinetEmployee;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserCabinetTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $anotherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        // Создаем кабинет, принадлежащий пользователю
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);

        // Создаем другой кабинет для тестов
        $this->anotherCabinet = Cabinet::factory()->create();
    }

    public function test_can_update_current_cabinet_as_owner(): void
    {
        // Act
        $response = $this->putJson("/api/internal/users/cabinet/{$this->cabinet->id}");

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('users', [
            'id' => $this->user->id,
            'current_cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_update_current_cabinet_as_employee(): void
    {
        // Arrange
        $employee = Employee::factory()->create([
            'user_id' => $this->user->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/users/cabinet/{$this->anotherCabinet->id}");

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('users', [
            'id' => $this->user->id,
            'current_cabinet_id' => $this->anotherCabinet->id
        ]);
    }

    public function test_cannot_update_current_cabinet_without_access(): void
    {
        // Arrange
        $cabinetWithoutAccess = Cabinet::factory()->create();

        // Act
        $response = $this->putJson("/api/internal/users/cabinet/{$cabinetWithoutAccess->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('users', [
            'id' => $this->user->id,
            'current_cabinet_id' => $cabinetWithoutAccess->id
        ]);
    }

    public function test_cannot_update_current_cabinet_for_non_existent_cabinet(): void
    {
        // Act
        $response = $this->putJson("/api/internal/users/cabinet/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_update_current_cabinet_multiple_times(): void
    {
        // Arrange
        $employee = Employee::factory()->create([
            'user_id' => $this->user->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act - сначала меняем на один кабинет
        $this->putJson("/api/internal/users/cabinet/{$this->cabinet->id}");

        // Затем меняем на другой
        $response = $this->putJson("/api/internal/users/cabinet/{$this->anotherCabinet->id}");

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('users', [
            'id' => $this->user->id,
            'current_cabinet_id' => $this->anotherCabinet->id
        ]);
    }
}
