<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use Illuminate\Support\Str;
use App\Models\UserSettings;
use App\Models\CabinetInvite;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_successful_registration(): void
    {
        $data = [
            'lastname' => 'John',
            'firstname' => 'John',
            'patronymic' => 'John',
            'tel' => '88005553535',
            'email' => '<EMAIL>',
            'password' => 'securePass123',
        ];

        $response = $this->postJson('/api/register', $data);

        $response->assertCreated()
            ->assertJsonStructure(['token']);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
        $this->assertDatabaseHas('user_settings', [
            'user_id' => User::where('email', '<EMAIL>')->first()->id,
        ]);
    }

    public function test_validation_fails_if_required_fields_missing(): void
    {
        $response = $this->postJson('/api/register', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['firstname', 'tel', 'email', 'password']);
    }

    public function test_validation_fails_if_email_invalid(): void
    {
        $data = [
            'lastname' => 'Doe',
            'firstname' => 'John',
            'tel' => '1234567890',
            'email' => 'invalid-email',
            'password' => 'securePass123',
        ];

        $response = $this->postJson('/api/register', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_succeful_register_with_invite_token(): void
    {
        $registerEmail = fake()->email;
        $user = User::factory()->create();
        $token = Str::random(16);
        $cabinet = Cabinet::factory()->create(['user_id' => $user->id]);
        $employee = Employee::factory()->create(
            ['user_id' => $user->id]
        );

        CabinetInvite::factory()
           ->create([
               'cabinet_id' => $cabinet->id,
               'employee_id' => $employee->id,
               'email' => $registerEmail,
               'token' => $token
           ]);

        $data = [
            'lastname' => 'Doe',
            'firstname' => 'John',
            'tel' => '1234567890',
            'email' => $registerEmail,
            'password' => 'securePass123',
            'invite_token' => $token
        ];

        $this->postJson('/api/register', $data)
            ->assertCreated();

        $this->assertDatabaseHas('users', [
            'email' => $registerEmail,
        ]);

        $this->assertDatabaseHas('employees', [
            'email' => $registerEmail,
        ]);
    }

    public function test_fail_register_with_stealed_invite_token(): void
    {
        $registerEmail = fake()->email;
        $user = User::factory()->create();
        $token = Str::random(16);
        $cabinet = Cabinet::factory()->create(['user_id' => $user->id]);
        $employee = Employee::factory()->create(
            ['user_id' => $user->id]
        );

        CabinetInvite::factory()
            ->create([
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employee->id,
                'email' => $registerEmail,
                'token' => $token
            ]);

        $data = [
            'lastname' => 'Doe',
            'firstname' => 'John',
            'tel' => '1234567890',
            'email' => fake()->email,
            'password' => 'securePass123',
            'invite_token' => $token
        ];

        $this->postJson('/api/register', $data)
            ->assertStatus(422)
            ->assertJsonValidationErrors(['invite_token']);
    }

    public function test_fail_register_with_incorect_invite_token(): void
    {
        $registerEmail = fake()->email;
        $user = User::factory()->create();
        $tokens = [Str::random(13), Str::random(16)];
        $cabinet = Cabinet::factory()->create(['user_id' => $user->id]);
        $employee = Employee::factory()->create(
            ['user_id' => $user->id]
        );

        CabinetInvite::factory()
            ->create([
                'cabinet_id' => $cabinet->id,
                'employee_id' => $employee->id,
                'email' => $registerEmail,
                'token' => Str::random(16)
            ]);

        foreach ($tokens as $token) {
            $data = [
                'lastname' => 'Doe',
                'firstname' => 'John',
                'tel' => '1234567890',
                'email' => fake()->email,
                'password' => 'securePass123',
                'invite_token' => $token
            ];

            $this->postJson('/api/register', $data)
                ->assertStatus(422)
                ->assertJsonValidationErrors(['invite_token']);
        }
    }

    public function test_successful_login(): void
    {
        $password = 'securePass123';
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => $password
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => $password
        ]);

        $response->assertOk()
            ->assertJsonStructure(['token']);
    }

    public function test_fail_login_with_incorrect_password(): void
    {
        $password = 'securePass123';
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => $password
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'incorrectPassword'
        ]);

        $response->assertStatus(401);
    }

    public function test_fail_login_with_incorrect_email(): void
    {
        $password = 'securePass123';
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => $password
        ]);

        $response = $this->postJson('/api/login', [
            'email' => 'incorrectEmail',
            'password' => $password
        ]);

        $response->assertStatus(401);
    }

    public function test_successful_logout(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('Api-token')->plainTextToken;

        $response = $this->withHeaders(
            [
                'Authorization' => 'Bearer '.$token,
                'Accept' => 'application/json'
            ]
        )->get('/api/internal/logout');

        $response->assertNoContent();

        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $user->id
        ]);
    }

    public function test_destroy_user(): void
    {
        $user = User::factory()->create();
        UserSettings::factory()->create(['user_id' => $user->id]);
        $token = $user->createToken('Api-token')->plainTextToken;

        $response = $this->withHeaders(
            [
                'Authorization' => 'Bearer '.$token,
                'Accept' => 'application/json'
            ]
        )->delete('/api/internal/destroy');

        $response->assertNoContent();

        $this->assertDatabaseMissing('users', [
                'id' => $user->id
            ]);
        $this->assertDatabaseMissing('user_settings', [
                'user_id' => $user->id
                ]);
    }

}
