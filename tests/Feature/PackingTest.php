<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Cabinet;
use App\Models\Packing;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\MeasurementUnit;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PackingTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Department $department;
    protected Employee $employee;
    protected MeasurementUnit $measurementUnitSize;
    protected MeasurementUnit $measurementUnitWeight;
    protected MeasurementUnit $measurementUnitVolume;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->measurementUnitSize = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->measurementUnitWeight = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->measurementUnitVolume = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_packings_list(): void
    {
        // Arrange
        Packing::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings?{$query}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                '*' => [
                    'id',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'cabinet_id',
                    'name',
                    'description',
                    'length',
                    'width',
                    'height',
                    'measurement_unit_size_id',
                    'weight',
                    'measurement_unit_weight_id',
                    'volume',
                    'measurement_unit_volume_id',
                    'employee_id',
                    'department_id',
                ]
                    ],
                'meta'
            ])
            ->assertJsonCount(3, 'data');
    }

    public function test_cannot_get_packings_without_cabinet_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/packings');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_packings_with_invalid_cabinet_id(): void
    {
        // Act
        $query = http_build_query([
            'cabinet_id' => 'invalid-uuid'
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings?{$query}");

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_packings_from_other_cabinet(): void
    {
        // Arrange
        Packing::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings?{$query}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_get_packings_with_sorting(): void
    {
        // Arrange
        Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'name' => 'AAA'
        ]);

        Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'name' => 'BBB'
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings?{$query}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        $data = $response->json('data');
        $this->assertEquals('BBB', $data[0]['name']);
        $this->assertEquals('AAA', $data[1]['name']);
    }

    public function test_can_get_packings_with_pagination(): void
    {
        // Arrange
        Packing::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings?{$query}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(5, 'data');
    }

    public function test_cannot_get_packings_with_invalid_pagination(): void
    {
        // Act
        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 0,
            'per_page' => 0
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings?{$query}");

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['page', 'per_page']);
    }

    public function test_cannot_get_packings_with_invalid_sort_direction(): void
    {
        // Act
        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortDirection' => 'invalid'
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings?{$query}");

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortDirection']);
    }

    public function test_can_create_packing_with_minimal_required_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/packings', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('packings', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
    }

    public function test_can_create_packing_with_all_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing Full',
            'description' => 'Test Description',
            'length' => 10.5,
            'width' => 20.5,
            'height' => 30.5,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'weight' => 40.5,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'volume' => 50.5,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/packings', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('packings', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing Full',
            'description' => 'Test Description',
            'length' => 10.5,
            'width' => 20.5,
            'height' => 30.5,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'weight' => 40.5,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'volume' => 50.5,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
    }

    public function test_cannot_create_packing_without_required_fields(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/packings', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'measurement_unit_size_id',
                'measurement_unit_weight_id',
                'measurement_unit_volume_id',
                'employee_id',
                'department_id',
            ]);
    }

    public function test_cannot_create_packing_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/packings', $data);

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseMissing('packings', [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Packing'
        ]);
    }

    public function test_cannot_create_packing_with_measurement_units_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetMeasurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'measurement_unit_size_id' => $otherCabinetMeasurementUnit->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/packings', $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseMissing('packings', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'measurement_unit_size_id' => $otherCabinetMeasurementUnit->id
        ]);
    }

    public function test_cannot_create_packing_with_employee_from_other_cabinet(): void
    {
        // Arrange
        $otherEmployee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $otherEmployee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $otherEmployee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/packings', $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseMissing('packings', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'employee_id' => $otherEmployee->id
        ]);
    }

    public function test_cannot_create_packing_with_department_from_other_cabinet(): void
    {
        // Arrange
        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $otherDepartment->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/packings', $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseMissing('packings', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'department_id' => $otherDepartment->id
        ]);
    }

    public function test_can_update_packing_with_minimal_fields(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $data = [
            'name' => 'Updated Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$packing->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('packings', [
            'id' => $packing->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
    }

    public function test_can_update_packing_with_all_fields(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $data = [
            'name' => 'Updated Packing Full',
            'description' => 'Updated Description',
            'length' => 15.5,
            'width' => 25.5,
            'height' => 35.5,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'weight' => 45.5,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'volume' => 55.5,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$packing->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('packings', [
            'id' => $packing->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Packing Full',
            'description' => 'Updated Description',
            'length' => 15.5,
            'width' => 25.5,
            'height' => 35.5,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'weight' => 45.5,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'volume' => 55.5,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);
    }

    public function test_cannot_update_packing_without_required_fields(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$packing->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'measurement_unit_size_id',
                'measurement_unit_weight_id',
                'measurement_unit_volume_id',
                'employee_id',
                'department_id',
            ]);
    }

    public function test_cannot_update_packing_from_other_cabinet(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
        ]);

        $data = [
            'name' => 'Updated Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$packing->id}", $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseMissing('packings', [
            'id' => $packing->id,
            'name' => 'Updated Packing'
        ]);
    }

    public function test_cannot_update_packing_with_measurement_units_from_other_cabinet(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $otherCabinetMeasurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => 'Updated Packing',
            'measurement_unit_size_id' => $otherCabinetMeasurementUnit->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$packing->id}", $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseMissing('packings', [
            'id' => $packing->id,
            'measurement_unit_size_id' => $otherCabinetMeasurementUnit->id
        ]);
    }

    public function test_cannot_update_packing_with_employee_from_other_cabinet(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $otherEmployee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $otherEmployee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => 'Updated Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $otherEmployee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$packing->id}", $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseMissing('packings', [
            'id' => $packing->id,
            'employee_id' => $otherEmployee->id
        ]);
    }

    public function test_cannot_update_packing_with_department_from_other_cabinet(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => 'Updated Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $otherDepartment->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$packing->id}", $data);

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseMissing('packings', [
            'id' => $packing->id,
            'department_id' => $otherDepartment->id
        ]);
    }

    public function test_cannot_update_non_existent_packing(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid;
        $data = [
            'name' => 'Updated Packing',
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/packings/{$nonExistentId}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_packing(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Packing',
            'description' => 'Test Description',
            'length' => 10.5,
            'width' => 20.5,
            'height' => 30.5,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'weight' => 40.5,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'volume' => 50.5,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings/{$packing->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'name',
                'description',
                'length',
                'width',
                'height',
                'measurement_unit_size',
                'weight',
                'measurement_unit_weight',
                'volume',
                'measurement_unit_volume',
                'employee_id',
                'department_id',
            ])
            ->assertJson([
                'id' => $packing->id,
                'cabinet_id' => $this->cabinet->id,
                'name' => 'Test Packing',
                'description' => 'Test Description',
                'length' => 10.5,
                'width' => 20.5,
                'height' => 30.5,
                'weight' => 40.5,
                'volume' => 50.5,
                'employee_id' => $this->employee->id,
                'department_id' => $this->department->id,
            ]);
    }

    public function test_cannot_show_packing_from_other_cabinet(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings/{$packing->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_packing(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid;

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/packings/{$nonExistentId}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_packing(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/packings/{$packing->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('packings', [
            'id' => $packing->id
        ]);
    }

    public function test_cannot_delete_packing_from_other_cabinet(): void
    {
        // Arrange
        $packing = Packing::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'measurement_unit_size_id' => $this->measurementUnitSize->id,
            'measurement_unit_weight_id' => $this->measurementUnitWeight->id,
            'measurement_unit_volume_id' => $this->measurementUnitVolume->id,
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/packings/{$packing->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('packings', [
            'id' => $packing->id
        ]);
    }

    public function test_cannot_delete_non_existent_packing(): void
    {
        // Arrange
        $nonExistentId = $this->faker->uuid;

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/packings/{$nonExistentId}");

        // Assert
        $response->assertStatus(404);
    }
}
