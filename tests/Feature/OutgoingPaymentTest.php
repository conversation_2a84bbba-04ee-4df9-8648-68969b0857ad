<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\File;
use App\Models\LegalEntity;
use App\Models\OutgoingPayment;
use App\Models\SalesChannel;
use App\Models\Status;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class OutgoingPaymentTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Department $department;
    protected Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_outgoing_payments_list(): void
    {
        // Arrange
        $payments = OutgoingPayment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments?cabinet_id={$this->cabinet->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'created_at',
                            'updated_at'
                        ]
                    ],
            ])
            ->assertJsonCount(3, 'data');
    }

    public function test_cannot_get_outgoing_payments_without_cabinet_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/outgoing-payments');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_outgoing_payments_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/outgoing-payments?cabinet_id=invalid-uuid');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_access_other_cabinet_outgoing_payments(): void
    {
        // Arrange
        OutgoingPayment::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments?cabinet_id={$this->otherCabinet->id}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_cannot_get_outgoing_payments_without_permission(): void
    {
        // Arrange
        Artisan::call('app:fill-permissions');

        $newUser = User::factory()->create();

        $employeeWithoutPermission = Employee::factory()->create([
            'user_id' => $newUser->id,
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employeeWithoutPermission->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        OutgoingPayment::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->actingAs($newUser)
            ->getJson("/api/internal/outgoing-payments?cabinet_id={$this->cabinet->id}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_create_outgoing_payment_with_minimal_required_fields(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure([
                    'id'
            ]);

        $this->assertDatabaseHas('outgoing_payments', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
        ]);
    }

    public function test_can_create_outgoing_payment_with_all_fields(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $legal = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'number' => (string)$this->faker->unique()->numberBetween(1000, 9999),
            'date_from' => now()->format('Y-m-d'),
            'status_id' => $status->id,
            'held' => true,
            'without_closing_documents' => true,
            'legal_entity_id' => $legal->id,
            'contractor_id' => $contractor->id,
            'sales_channel_id' => $salesChannel->id,
            'sum' => $this->faker->randomFloat(2, 100, 1000),
            'included_vat' => $this->faker->randomFloat(2, 10, 100),
            'comment' => $this->faker->sentence,
            'currency_id' => $currency->id,
            'currency_value' => $this->faker->randomFloat(2, 1, 100),
            'is_imported' => true,
            'is_common' => true,
            'files' => [$file->id]
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure([
                    'id'
            ]);

        $this->assertDatabaseHas('outgoing_payments', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'held' => true,
            'without_closing_documents' => true,
            'sum' => $data['sum'],
            'included_vat' => $data['included_vat'],
            'comment' => $data['comment'],
            'is_imported' => true,
            'is_common' => true
        ]);
    }

    public function test_cannot_create_outgoing_payment_without_required_fields(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'department_id',
                'employee_id',
                'legal_entity_id',
                'contractor_id',
                'currency_id'
            ]);
    }

    public function test_cannot_create_outgoing_payment_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'legal_entity_id' => $this->faker->uuid,
            'contractor_id' => $this->faker->uuid,
            'currency_id' => $this->faker->uuid,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/outgoing-payments', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_update_outgoing_payment_with_minimal_fields(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/{$payment->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('outgoing_payments', [
            'id' => $payment->id,
            'legal_entity_id' => $data['legal_entity_id'],
            'contractor_id' => $data['contractor_id'],
            'currency_id' => $data['currency_id'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id'],
        ]);
    }

    public function test_can_update_outgoing_payment_with_all_fields(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $legal = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $salesChannel = SalesChannel::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $file = File::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'number' => (string)$this->faker->unique()->numberBetween(1000, 9999),
            'date_from' => now()->format('Y-m-d'),
            'status_id' => $status->id,
            'held' => true,
            'legal_entity_id' => $legal->id,
            'contractor_id' => $contractor->id,
            'sales_channel_id' => $salesChannel->id,
            'sum' => $this->faker->randomFloat(2, 100, 1000),
            'included_vat' => $this->faker->randomFloat(2, 10, 100),
            'comment' => $this->faker->sentence,
            'currency_id' => $currency->id,
            'currency_value' => $this->faker->randomFloat(2, 1, 100),
            'without_closing_documents' => true,
            'files' => [$file->id],
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/{$payment->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('outgoing_payments', [
            'id' => $payment->id,
            'held' => true,
            'sum' => $data['sum'],
            'included_vat' => $data['included_vat'],
            'comment' => $data['comment'],
            'without_closing_documents' => true
        ]);
    }

    public function test_cannot_update_outgoing_payment_without_required_fields(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/{$payment->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'legal_entity_id',
                'contractor_id',
                'currency_id',
                'employee_id',
                'department_id'
            ]);
    }

    public function test_cannot_update_outgoing_payment_in_other_cabinet(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/{$payment->id}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_non_existent_outgoing_payment(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'currency_id' => $currency->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/outgoing-payments/" . $this->faker->uuid, $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_outgoing_payment(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/{$payment->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'cabinet_id',
                'department_id',
                'employee_id',
                'number',
                'date_from',
                'status_id',
                'held',
                'legal_entity_id',
                'contractor_id',
                'sales_channel_id',
                'sum',
                'included_vat',
                'comment',
                'currency_id',
                'currency_value',
                'without_closing_documents',
                'is_imported',
                'is_common',
                'files',
                'created_at',
                'updated_at'
            ]);
    }

    public function test_cannot_show_outgoing_payment_from_other_cabinet(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/{$payment->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_outgoing_payment(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/outgoing-payments/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_outgoing_payment(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/outgoing-payments/{$payment->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('outgoing_payments', [
            'id' => $payment->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_outgoing_payment_from_other_cabinet(): void
    {
        // Arrange
        $payment = OutgoingPayment::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/outgoing-payments/{$payment->id}");

        // Assert
        $response->assertStatus(404);

        $this->assertDatabaseHas('outgoing_payments', [
            'id' => $payment->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_non_existent_outgoing_payment(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/outgoing-payments/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }
}
