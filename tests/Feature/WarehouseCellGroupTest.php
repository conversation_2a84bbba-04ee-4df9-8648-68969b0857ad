<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseCellGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class WarehouseCellGroupTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Warehouse $warehouse;
    private Warehouse $otherWarehouse;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department->id,
        ]);

        $this->otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
    }

    public function test_can_get_warehouse_groups_list(): void
    {
        // Arrange
        // Создаем 3 группы для нашего склада
        WarehouseCellGroup::factory()->count(3)->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Создаем группу для другого склада
        WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/groups?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'address',
                        'description',
                        'warehouse_id',
                        'parent_id',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего склада
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->warehouse->id, $item['warehouse_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_warehouse_groups(): void
    {
        // Arrange
        // Создаем группы для склада другого кабинета
        WarehouseCellGroup::factory()->count(2)->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/groups?' . http_build_query([
            'warehouse_id' => $this->otherWarehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/groups?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'warehouse_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'warehouse_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_without_required_warehouse_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/groups');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['warehouse_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/groups?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/groups?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_returns_empty_collection_when_no_groups(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells/groups?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_can_create_warehouse_group(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Test Address',
            'description' => 'Test Description'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_cell_groups', [
            'id' => $response->json('id'),
            'warehouse_id' => $data['warehouse_id'],
            'address' => $data['address'],
            'description' => $data['description']
        ]);
    }

    public function test_can_create_warehouse_group_with_parent(): void
    {
        // Arrange
        $parentGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
        ]);

        $data = [
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Child Group Address',
            'parent_id' => $parentGroup->id,
            'description' => 'Child Group Description'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_cell_groups', [
            'id' => $response->json('id'),
            'warehouse_id' => $data['warehouse_id'],
            'address' => $data['address'],
            'parent_id' => $parentGroup->id,
            'description' => $data['description']
        ]);
    }

    public function test_cannot_create_warehouse_group_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'warehouse_id' => $this->otherWarehouse->id,
            'address' => 'Test Address',
            'description' => 'Test Description'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('warehouse_cell_groups', [
            'warehouse_id' => $data['warehouse_id'],
            'address' => $data['address']
        ]);
    }

    public function test_cannot_create_warehouse_group_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'warehouse_id' => 'not-a-uuid',
            'address' => '',
            'parent_id' => 'not-a-uuid',
            'description' => []  // неверный тип данных
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'warehouse_id',
                'address',
                'parent_id',
                'description'
            ]);
    }

    public function test_cannot_create_warehouse_group_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'warehouse_id',
                'address'
            ]);
    }

    public function test_cannot_create_warehouse_group_with_non_existent_parent(): void
    {
        // Arrange
        $data = [
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Test Address',
            'parent_id' => $this->faker->uuid,
            'description' => 'Test Description'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_warehouse_group_with_parent_from_other_warehouse(): void
    {
        // Arrange
        $otherWarehouseGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id,
        ]);

        $data = [
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Test Address',
            'parent_id' => $otherWarehouseGroup->id,
            'description' => 'Test Description'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_create_warehouse_group_with_minimal_fields(): void
    {
        // Arrange
        $data = [
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Test Address'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells/groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_cell_groups', [
            'id' => $response->json('id'),
            'warehouse_id' => $data['warehouse_id'],
            'address' => $data['address'],
            'parent_id' => null,
            'description' => null
        ]);
    }

    public function test_can_update_warehouse_group(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Old Address',
            'description' => 'Old Description'
        ]);

        $data = [
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Updated Address',
            'description' => 'Updated Description'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cell_groups', [
            'id' => $group->id,
            'warehouse_id' => $data['warehouse_id'],
            'address' => $data['address'],
            'description' => $data['description']
        ]);
    }

    public function test_can_update_warehouse_group_with_parent(): void
    {
        // Arrange
        $parentGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'parent_id' => null
        ]);

        $data = [
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Updated Address',
            'parent_id' => $parentGroup->id,
            'description' => 'Updated Description'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cell_groups', [
            'id' => $group->id,
            'warehouse_id' => $data['warehouse_id'],
            'address' => $data['address'],
            'parent_id' => $parentGroup->id,
            'description' => $data['description']
        ]);
    }

    public function test_cannot_update_warehouse_group_from_other_cabinet(): void
    {
        // Arrange
        $otherGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        $data = [
            'warehouse_id' => $this->otherWarehouse->id,
            'address' => 'Updated Address',
            'description' => 'Updated Description'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$otherGroup->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('warehouse_cell_groups', [
            'id' => $otherGroup->id,
            'address' => $data['address']
        ]);
    }

    public function test_cannot_update_warehouse_group_with_invalid_data(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'address' => '',
            'parent_id' => 'not-a-uuid',
            'description' => []  // неверный тип данных
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'address',
                'parent_id',
                'description'
            ]);
    }

    public function test_cannot_update_warehouse_group_without_required_fields(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$group->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'address'
            ]);
    }

    public function test_cannot_update_non_existent_warehouse_group(): void
    {
        // Arrange
        $data = [
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Updated Address'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/" . $this->faker->uuid, $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_warehouse_group_with_non_existent_parent(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'address' => 'Updated Address',
            'parent_id' => $this->faker->uuid
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$group->id}", $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_warehouse_group_with_parent_from_other_warehouse(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $otherWarehouseGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        $data = [
            'address' => 'Updated Address',
            'parent_id' => $otherWarehouseGroup->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$group->id}", $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_update_warehouse_group_with_minimal_fields(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Old Address',
            'description' => 'Old Description',
            'parent_id' => WarehouseCellGroup::factory()->create([
                'warehouse_id' => $this->warehouse->id
            ])->id
        ]);

        $data = [
            'address' => 'Updated Address'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cell_groups', [
            'id' => $group->id,
            'warehouse_id' => $group->warehouse_id,
            'address' => $data['address'],
        ]);
    }

    public function test_can_show_warehouse_group(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'address' => 'Test Address',
            'description' => 'Test Description'
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/groups/{$group->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'warehouse_id',
                'address',
                'description',
                'parent_id'
            ])
            ->assertJson([
                'id' => $group->id,
                'warehouse_id' => $this->warehouse->id,
                'address' => 'Test Address',
                'description' => 'Test Description'
            ]);
    }

    public function test_can_show_warehouse_group_with_parent(): void
    {
        // Arrange
        $parentGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'parent_id' => $parentGroup->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/groups/{$group->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'id' => $group->id,
                'parent_id' => $parentGroup->id
            ]);
    }

    public function test_cannot_show_warehouse_group_from_other_cabinet(): void
    {
        // Arrange
        $otherGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/groups/{$otherGroup->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_warehouse_group(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/groups/" . $this->faker->uuid);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_warehouse_group(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/groups/{$group->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_cell_groups', [
            'id' => $group->id
        ]);
    }

    public function test_can_delete_warehouse_group_with_children(): void
    {
        // Arrange
        $parentGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $childGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'parent_id' => $parentGroup->id
        ]);

        $grandChildGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'parent_id' => $childGroup->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/groups/{$parentGroup->id}");

        // Assert
        $response->assertStatus(204);

        // Проверяем что удалились все группы в иерархии
        $this->assertDatabaseMissing('warehouse_cell_groups', [
            'id' => $parentGroup->id
        ]);
        $this->assertDatabaseMissing('warehouse_cell_groups', [
            'id' => $childGroup->id
        ]);
        $this->assertDatabaseMissing('warehouse_cell_groups', [
            'id' => $grandChildGroup->id
        ]);
    }

    public function test_cannot_delete_warehouse_group_from_other_cabinet(): void
    {
        // Arrange
        $otherGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->otherWarehouse->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/groups/{$otherGroup->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('warehouse_cell_groups', [
            'id' => $otherGroup->id
        ]);
    }

    public function test_cannot_delete_non_existent_warehouse_group(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/groups/" . $this->faker->uuid);

        // Assert
        $response->assertNotFound();
    }
}
