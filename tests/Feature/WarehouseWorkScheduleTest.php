<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Jobs\GenerateCalendarJob;
use App\Models\WarehouseWorkSchedule;
use Illuminate\Support\Facades\Queue;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WarehouseWorkScheduleTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_work_schedules_list(): void
    {
        // Arrange
        // Создаем 3 графика работы для нашего кабинета
        WarehouseWorkSchedule::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем график работы для другого кабинета
        WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/work-schedules?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'name',
                        'description',
                        'cabinet_id',
                        'date_from',
                        'date_to',
                        'filling_type',
                        'cycle_day_lenght',
                        'cycle_day_from',
                        'keep_holidays',
                        'holiday_schedule',
                        'filling_template',
                        'planning_horizon',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_work_schedules(): void
    {
        // Arrange
        // Создаем графики работы для другого кабинета
        WarehouseWorkSchedule::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/work-schedules?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertForbidden();
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/work-schedules?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_without_required_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/work-schedules');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/work-schedules?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        // Act
        $response = $this->getJson('/api/internal/work-schedules?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_returns_empty_collection_when_no_work_schedules(): void
    {
        // Act
        $response = $this->getJson('/api/internal/work-schedules?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_index_with_selected_fields(): void
    {
        // Arrange
        WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/work-schedules?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => [
                'id', 'name'
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                    ]
                ]
            ]);

        // Проверяем что в ответе только запрошенные поля
        $responseData = $response->json('data.0');
        $this->assertCount(2, array_keys($responseData));
    }

    public function test_can_create_work_schedule(): void
    {
        Queue::fake();
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Schedule',
            'description' => 'Test Description',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31',
            'filling_type' => 1,
            'keep_holidays' => true,
            'holiday_schedule' => json_encode([
                [
                    'start' => '08:00',
                    'end' => '12:00'
                ]
            ]),
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ],
                'Tuesday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->postJson('/api/internal/work-schedules', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_work_schedules', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Schedule',
            'description' => 'Test Description',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31',
            'filling_type' => 1,
            'keep_holidays' => true
        ]);

        Queue::assertPushed(GenerateCalendarJob::class);

    }

    public function test_can_create_work_schedule_with_cycle(): void
    {
        // Arrange
        Queue::fake();
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Cycle Schedule',
            'date_from' => '2024-01-01',
            'filling_type' => 2,
            'cycle_day_lenght' => 7,
            'cycle_day_from' => '2024-01-01',
            'keep_holidays' => false,
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->postJson('/api/internal/work-schedules', $data);

        // Assert
        $response->assertStatus(201);

        $this->assertDatabaseHas('warehouse_work_schedules', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Cycle Schedule',
            'filling_type' => 2,
            'cycle_day_lenght' => 7,
            'cycle_day_from' => '2024-01-01',
            'keep_holidays' => false
        ]);

        Queue::assertPushed(GenerateCalendarJob::class);
    }

    public function test_cannot_create_work_schedule_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Schedule',
            'date_from' => '2024-01-01',
            'filling_type' => 1,
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->postJson('/api/internal/work-schedules', $data);

        // Assert
        $response->assertForbidden();

        $this->assertDatabaseMissing('warehouse_work_schedules', [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Schedule'
        ]);
    }

    public function test_cannot_create_work_schedule_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'name' => '',
            'date_from' => 'not-a-date',
            'filling_type' => 999,
            'cycle_day_lenght' => 'not-a-number',
            'cycle_day_from' => 'not-a-date',
            'keep_holidays' => 'not-a-boolean',
            'holiday_schedule' => 'not-a-json',
            'filling_template' => 'not-a-json'
        ];

        // Act
        $response = $this->postJson('/api/internal/work-schedules', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'date_from',
                'filling_type',
                'filling_template'
            ]);
    }

    public function test_cannot_create_work_schedule_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/work-schedules', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'date_from',
                'filling_type',
                'filling_template'
            ]);
    }

    public function test_cycle_fields_required_for_cycle_schedule(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Cycle Schedule',
            'date_from' => '2024-01-01',
            'filling_type' => 2, // Тип с циклом
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->postJson('/api/internal/work-schedules', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cycle_day_lenght',
                'cycle_day_from'
            ]);
    }

    public function test_holiday_schedule_required_when_holidays_enabled(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Schedule',
            'date_from' => '2024-01-01',
            'filling_type' => 1,
            'keep_holidays' => true, // Праздники включены
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->postJson('/api/internal/work-schedules', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['holiday_schedule']);
    }

    public function test_can_update_work_schedule(): void
    {
        Queue::fake();
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Schedule',
            'description' => 'Updated Description',
            'date_from' => '2024-02-01',
            'date_to' => '2024-12-31',
            'filling_type' => 1,
            'keep_holidays' => true,
            'holiday_schedule' => json_encode([
                [
                    'start' => '09:00',
                    'end' => '13:00'
                ]
            ]),
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '10:00',
                        'end' => '19:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->putJson("/api/internal/work-schedules/{$schedule->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_work_schedules', [
            'id' => $schedule->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Schedule',
            'description' => 'Updated Description',
            'date_from' => '2024-02-01',
            'date_to' => '2024-12-31',
            'filling_type' => 1,
            'keep_holidays' => true
        ]);

        Queue::assertPushed(GenerateCalendarJob::class);
    }

    public function test_can_update_work_schedule_to_cycle(): void
    {
        Queue::fake();
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'filling_type' => 1
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated to Cycle',
            'date_from' => '2024-02-01',
            'filling_type' => 2,
            'cycle_day_lenght' => 5,
            'cycle_day_from' => '2024-02-01',
            'keep_holidays' => false,
            'filling_template' => json_encode([
                'Day1' => [
                    [
                        'start' => '08:00',
                        'end' => '20:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->putJson("/api/internal/work-schedules/{$schedule->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_work_schedules', [
            'id' => $schedule->id,
            'filling_type' => 2,
            'cycle_day_lenght' => 5,
            'cycle_day_from' => '2024-02-01'
        ]);

        Queue::assertPushed(GenerateCalendarJob::class);
    }

    public function test_cannot_update_work_schedule_in_other_cabinet(): void
    {
        // Arrange
        $otherSchedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Try Update Other Cabinet',
            'date_from' => '2024-02-01',
            'filling_type' => 1,
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->putJson("/api/internal/work-schedules/{$otherSchedule->id}", $updateData);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('warehouse_work_schedules', [
            'id' => $otherSchedule->id,
            'name' => 'Try Update Other Cabinet'
        ]);
    }

    public function test_cannot_update_work_schedule_with_invalid_data(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'cabinet_id' => 'not-a-uuid',
            'name' => '',
            'date_from' => 'not-a-date',
            'filling_type' => 999,
            'cycle_day_lenght' => 'not-a-number',
            'cycle_day_from' => 'not-a-date',
            'keep_holidays' => 'not-a-boolean',
            'holiday_schedule' => 'not-a-json',
            'filling_template' => 'not-a-json'
        ];

        // Act
        $response = $this->putJson("/api/internal/work-schedules/{$schedule->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'date_from',
                'filling_template'
            ]);
    }

    public function test_cannot_update_non_existent_work_schedule(): void
    {
        // Arrange
        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Schedule',
            'date_from' => '2024-02-01',
            'filling_type' => 1,
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->putJson("/api/internal/work-schedules/" . $this->faker->uuid(), $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cycle_fields_required_when_updating_to_cycle_schedule(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'filling_type' => 1
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated to Cycle',
            'date_from' => '2024-02-01',
            'filling_type' => 2, // Меняем на циклический тип
            'filling_template' => json_encode([
                'Day1' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->putJson("/api/internal/work-schedules/{$schedule->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cycle_day_lenght',
                'cycle_day_from'
            ]);
    }

    public function test_holiday_schedule_required_when_updating_with_holidays_enabled(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'keep_holidays' => false
        ]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Updated Schedule',
            'date_from' => '2024-02-01',
            'filling_type' => 1,
            'keep_holidays' => true, // Включаем праздники
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '09:00',
                        'end' => '18:00'
                    ]
                ]
            ])
        ];

        // Act
        $response = $this->putJson("/api/internal/work-schedules/{$schedule->id}", $updateData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['holiday_schedule']);
    }

    public function test_can_show_work_schedule(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Schedule',
            'description' => 'Test Description',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31',
            'filling_type' => 1,
            'keep_holidays' => true,
            'holiday_schedule' => json_encode([
                [
                    'start' => '09:00',
                    'end' => '13:00'
                ]
            ]),
            'filling_template' => json_encode([
                'Monday' => [
                    [
                        'start' => '10:00',
                        'end' => '19:00'
                    ]
                ]
            ])
        ]);

        // Act
        $response = $this->getJson("/api/internal/work-schedules/{$schedule->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'name',
                'description',
                'cabinet_id',
                'date_from',
                'date_to',
                'filling_type',
                'cycle_day_lenght',
                'cycle_day_from',
                'keep_holidays',
                'holiday_schedule',
                'filling_template',
                'planning_horizon'
            ])
            ->assertJson([
                'id' => $schedule->id,
                'name' => 'Test Schedule',
                'description' => 'Test Description',
                'cabinet_id' => $this->cabinet->id,
                'date_from' => '2024-01-01',
                'date_to' => '2024-12-31',
                'filling_type' => 1,
                'keep_holidays' => true
            ]);
    }

    public function test_can_show_work_schedule_with_cycle(): void
    {
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'filling_type' => 2,
            'cycle_day_lenght' => 7,
            'cycle_day_from' => '2024-01-01'
        ]);

        // Act
        $response = $this->getJson("/api/internal/work-schedules/{$schedule->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'id' => $schedule->id,
                'filling_type' => 2,
                'cycle_day_lenght' => 7,
                'cycle_day_from' => '2024-01-01'
            ]);
    }

    public function test_cannot_show_work_schedule_from_other_cabinet(): void
    {
        // Arrange
        $otherSchedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/work-schedules/{$otherSchedule->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_work_schedule(): void
    {
        // Act
        $response = $this->getJson("/api/internal/work-schedules/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_work_schedule(): void
    {
        Queue::fake();
        // Arrange
        $schedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/work-schedules/{$schedule->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_work_schedules', [
            'id' => $schedule->id
        ]);
    }

    public function test_cannot_delete_work_schedule_from_other_cabinet(): void
    {
        // Arrange
        $otherSchedule = WarehouseWorkSchedule::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/work-schedules/{$otherSchedule->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('warehouse_work_schedules', [
            'id' => $otherSchedule->id
        ]);
    }

    public function test_cannot_delete_non_existent_work_schedule(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/work-schedules/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }
}
