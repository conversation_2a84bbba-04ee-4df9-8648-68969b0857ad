<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\MeasurementUnit;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\MeasurementUnitGroup;
use App\Enums\Api\Internal\EntitiesTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MeasurementUnitTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Department $department;
    protected Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_measurement_units_list(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $units = MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?cabinet_id={$this->cabinet->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'cabinet_id',
                        'group_id',
                        'name',
                        'short_name',
                        'code',
                        'conversion_factor',
                        'created_at',
                        'updated_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
    }

    public function test_cannot_get_measurement_units_without_cabinet_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/measurement-units');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_measurement_units_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/measurement-units?cabinet_id=invalid-uuid');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_access_other_cabinet_measurement_units(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?cabinet_id={$this->otherCabinet->id}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        // Arrange
        $invalidData = [
            'filters' => [
                'type' => [
                    'value' => 'invalid_type'
                ],
                'name' => [
                    'condition' => 'invalid_condition'
                ],
                'code' => [
                    'condition' => 'invalid_condition'
                ],
                'updated_at' => [
                    'from' => 'invalid_date',
                    'to' => 'invalid_date'
                ]
            ]
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?cabinet_id={$this->cabinet->id}&" . http_build_query($invalidData));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'filters.type.value',
                'filters.name.condition',
                'filters.code.condition',
                'filters.updated_at.from',
                'filters.updated_at.to'
            ]);
    }

    public function test_index_filter_type_system(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        MeasurementUnit::factory()->count(2)->create([
            'cabinet_id' => null,
            'group_id' => $group->id
        ]);

        MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'type' => [
                        'value' => EntitiesTypeEnum::SYSTEM->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);

        foreach ($response->json('data') as $item) {
            $this->assertEquals(null, $item['cabinet_id']);
        }
    }

    public function test_index_filter_type_custom(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        MeasurementUnit::factory()->count(2)->create([
            'cabinet_id' => null,
            'group_id' => $group->id
        ]);

        MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'type' => [
                        'value' => EntitiesTypeEnum::CUSTOM->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_index_filter_name_in(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $name = 'TEST-MEASUREMENT-NAME-IN';
        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $name
        ]);

        MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'name' => [
                        'value' => $name,
                        'condition' => FilterConditionEnum::IN->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('meta.total'));
        $this->assertEquals($unit->id, $response->json('data.0.id'));
    }

    public function test_index_filter_name_not_in(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $name = 'TEST-MEASUREMENT-NAME-NOT-IN';
        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $name
        ]);

        $units = MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'name' => [
                        'value' => $name,
                        'condition' => FilterConditionEnum::NOT_IN->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertNotEquals($name, collect($response->json('data'))->pluck('name'));
    }

    public function test_index_filter_name_empty(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word()
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => null
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'name' => [
                        'condition' => FilterConditionEnum::EMPTY->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('meta.total'));
        $this->assertEquals($unit->id, $response->json('data.0.id'));
    }

    public function test_index_filter_name_not_empty(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $units = MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word()
        ]);

        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => null
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'name' => [
                        'condition' => FilterConditionEnum::NOT_EMPTY->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertNotEquals(null, collect($response->json('data'))->pluck('name'));
    }

    public function test_index_filter_code_in(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $code = $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}');
        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'code' => $code
        ]);

        MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'code' => [
                        'value' => $code,
                        'condition' => FilterConditionEnum::IN->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('meta.total'));
        $this->assertEquals($unit->id, $response->json('data.0.id'));
    }

    public function test_index_filter_code_not_in(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $code = $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}');
        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'code' => $code
        ]);

        $units = MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'code' => [
                        'value' => $code,
                        'condition' => FilterConditionEnum::NOT_IN->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertNotEquals($code, collect($response->json('data'))->pluck('code'));
    }

    public function test_index_filter_code_empty(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}')
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'code' => null
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'code' => [
                        'condition' => FilterConditionEnum::EMPTY->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);

        foreach (collect($response->json('data'))->unique('code')->toArray() as $code) {
            $this->assertNull($code['code']);
        }
    }

    public function test_index_filter_code_not_empty(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $units = MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}')
        ]);

        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'code' => null
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'code' => [
                        'condition' => FilterConditionEnum::NOT_EMPTY->value
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(3, $response->json('meta.total'));
        $this->assertEquals($units->pluck('id'), collect($response->json('data'))->pluck('id'));
    }

    public function test_index_filter_short_name(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $shortName = $this->faker->unique()->word();
        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'short_name' => $shortName
        ]);

        MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'short_name' => [
                        'value' => $shortName
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('meta.total'));
        $this->assertEquals($unit->id, $response->json('data.0.id'));
    }

    public function test_index_filter_search(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => 'UNIQUE-NAME'
        ]);

        MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'search' => [
                        'value' => 'UNIQUE-NAME'
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('meta.total'));
        $this->assertEquals($unit->id, $response->json('data.0.id'));
    }

    public function test_index_filter_updated_at(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $now = now();
        $unit1 = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'updated_at' => $now
        ]);

        $unit2 = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'updated_at' => $now->subDays(2)
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units?" . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'filters' => [
                    'updated_at' => [
                        'from' => $now->format('d.m.Y H:i'),
                        'to' => $now->addDay()->format('d.m.Y H:i')
                    ]
                ]
            ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');
        $this->assertEquals(1, $response->json('meta.total'));
    }

    public function test_can_create_base_measurement_unit(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 1
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('measurement_units', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $data['name'],
            'short_name' => $data['short_name'],
            'code' => $data['code'],
            'conversion_factor' => $data['conversion_factor']
        ]);
    }

    public function test_can_create_non_base_measurement_unit(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 2.5
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('measurement_units', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $data['name'],
            'short_name' => $data['short_name'],
            'code' => $data['code'],
            'conversion_factor' => $data['conversion_factor']
        ]);
    }

    public function test_cannot_create_base_unit_without_conversion_factor_to_old_base(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем базовую единицу
        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 1
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 1
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(500)
            ->assertJson([
                'error' => 'An error occurred while processing your request. Нужно указать фактор конверции к текущей базовой единице'
            ]);
    }

    public function test_cannot_create_base_unit_with_equal_conversion_factor(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем базовую единицу
        MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 1
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 1,
            'conversion_factor_to_old_base' => 1
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(500)
            ->assertJson([
                'error' => 'An error occurred while processing your request. Конвертация в старую базу не может быть 1:1'
            ]);
    }

    public function test_can_create_base_unit_with_conversion_factor_to_old_base(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем базовую единицу
        $oldBaseUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 1
        ]);

        // Создаем небазовую единицу
        $nonBaseUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 2
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 1,
            'conversion_factor_to_old_base' => 2
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(201);

        // Проверяем, что старая базовая единица стала небазовой
        $this->assertEquals(0.5, $oldBaseUnit->fresh()->conversion_factor);

        // Проверяем, что небазовая единица обновила свой коэффициент
        $this->assertEquals(1, $nonBaseUnit->fresh()->conversion_factor);
    }

    public function test_cannot_create_measurement_unit_in_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'group_id' => $group->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 1
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_cannot_create_measurement_unit_without_required_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'short_name',
                'conversion_factor',
                'group_id'
            ]);
    }

    public function test_cannot_create_measurement_unit_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'invalid-uuid',
            'group_id' => 'invalid-uuid',
            'short_name' => '',
            'conversion_factor' => 'not-numeric'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'group_id',
                'short_name',
                'conversion_factor'
            ]);
    }

    public function test_can_create_measurement_unit_in_system_group(): void
    {
        // Arrange
        $systemGroup = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => null,
            'is_system' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $systemGroup->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 1
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $newGroup = MeasurementUnitGroup::where('cabinet_id', $this->cabinet->id)
            ->where('is_system', false)
            ->first();

        $this->assertNotNull($newGroup);

        // Проверяем, что единица измерения создалась в новой группе
        $this->assertDatabaseHas('measurement_units', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $newGroup->id,
            'name' => $data['name'],
            'short_name' => $data['short_name'],
            'code' => $data['code'],
            'conversion_factor' => $data['conversion_factor']
        ]);

        // Проверяем, что системная группа осталась нетронутой
        $this->assertDatabaseHas('measurement_unit_groups', [
            'id' => $systemGroup->id,
            'cabinet_id' => null,
            'is_system' => true
        ]);
    }

    public function test_cannot_create_measurement_unit_in_other_cabinet_group(): void
    {
        // Arrange
        $otherGroup = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $otherGroup->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 1
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->postJson('/api/internal/measurement-units', $data);

        // Assert
        $response->assertStatus(404)
            ->assertJson([
                'error' => 'measurement_unit_groups not found.'
            ]);
    }

    public function test_can_update_measurement_unit(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 2
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => 10
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/{$unit->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('measurement_units', [
            'id' => $unit->id,
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => $data['name'],
            'short_name' => $data['short_name'],
            'code' => $data['code'],
            'conversion_factor' => 10
        ]);
    }

    public function test_can_update_non_base_unit_to_base(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем базовую единицу
        $baseUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 1
        ]);

        // Создаем единицу с фактором 2 (которую будем обновлять до базовой)
        $unitToUpdate = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 2
        ]);

        // Создаем еще одну единицу с фактором 4
        $otherUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 4
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'conversion_factor' => 1
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/{$unitToUpdate->id}", $data);

        // Assert
        $response->assertStatus(204);

        // Проверяем, что старая базовая единица обновила свой коэффициент
        $this->assertDatabaseHas('measurement_units', [
            'id' => $baseUnit->id,
            'conversion_factor' => 0.5 // 1 / 2 (старый фактор обновляемой единицы)
        ]);

        // Проверяем, что другая единица обновила свой коэффициент
        $this->assertDatabaseHas('measurement_units', [
            'id' => $otherUnit->id,
            'conversion_factor' => 2 // 4 / 2 (старый фактор обновляемой единицы)
        ]);

        // Проверяем обновленную единицу
        $this->assertDatabaseHas('measurement_units', [
            'id' => $unitToUpdate->id,
            'name' => $data['name'],
            'short_name' => $data['short_name'],
            'conversion_factor' => 1
        ]);
    }

    public function test_cannot_update_measurement_unit_in_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'group_id' => $group->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'conversion_factor' => random_int(0, 100000)
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/{$unit->id}", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_measurement_unit_in_system_group(): void
    {
        // Arrange
        $systemGroup = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => null,
            'is_system' => true
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $systemGroup->id,
            'conversion_factor' => 2
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'code' => $this->faker->unique()->regexify('[A-Z]{5}[0-9]{3}'),
            'conversion_factor' => random_int(0, 100000)
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/{$unit->id}", $data);

        // Assert
        $response->assertStatus(204);

        // Проверяем, что создалась новая группа
        $newGroup = MeasurementUnitGroup::where('cabinet_id', $this->cabinet->id)
            ->where('is_system', false)
            ->first();

        $this->assertNotNull($newGroup);

        // Проверяем, что единица измерения обновилась и переместилась в новую группу
        $this->assertDatabaseHas('measurement_units', [
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $newGroup->id,
            'name' => $data['name'],
            'short_name' => $data['short_name'],
            'code' => $data['code']
        ]);

        // Проверяем, что системная группа осталась нетронутой
        $this->assertDatabaseHas('measurement_unit_groups', [
            'id' => $systemGroup->id,
            'cabinet_id' => null,
            'is_system' => true
        ]);
    }

    public function test_cannot_update_measurement_unit_with_invalid_data(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        $data = [
            'cabinet_id' => 'invalid-uuid',
            'short_name' => '',
            'conversion_factor' => 'not-numeric'
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/{$unit->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'short_name',
                'conversion_factor'
            ]);
    }

    public function test_cannot_update_non_existent_measurement_unit(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'short_name' => $this->faker->word(),
            'conversion_factor' => 404
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->putJson("/api/internal/measurement-units/" . $this->faker->uuid, $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_measurement_unit(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'name' => 'Test Unit',
            'short_name' => 'TU',
            'code' => 'TEST123',
            'conversion_factor' => 2.5
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/{$unit->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'id' => $unit->id,
                'cabinet_id' => $this->cabinet->id,
                'group_id' => $group->id,
                'name' => 'Test Unit',
                'short_name' => 'TU',
                'code' => 'TEST123',
                'conversion_factor' => 2.5
            ]);
    }

    public function test_cannot_show_measurement_unit_from_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/{$unit->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_measurement_unit(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson("/api/internal/measurement-units/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_measurement_unit(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/{$unit->id}");

        // Assert
        $response->assertStatus(204);
        $this->assertDatabaseMissing('measurement_units', [
            'id' => $unit->id
        ]);
    }

    public function test_can_delete_base_measurement_unit_and_update_others(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем базовую единицу
        $baseUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 1
        ]);

        // Создаем небазовые единицы
        $unit1 = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 2
        ]);

        $unit2 = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 4
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/{$baseUnit->id}");

        // Assert
        $response->assertStatus(204);

        // Проверяем, что базовая единица удалена
        $this->assertDatabaseMissing('measurement_units', [
            'id' => $baseUnit->id
        ]);

        // Проверяем, что коэффициенты других единиц обновились
        $this->assertDatabaseHas('measurement_units', [
            'id' => $unit1->id,
            'conversion_factor' => 1 // 2 / 2 (старый фактор unit1)
        ]);

        $this->assertDatabaseHas('measurement_units', [
            'id' => $unit2->id,
            'conversion_factor' => 2 // 4 / 2 (старый фактор unit1)
        ]);
    }

    public function test_cannot_delete_measurement_unit_from_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $unit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/{$unit->id}");

        // Assert
        $response->assertStatus(404);
        $this->assertDatabaseHas('measurement_units', [
            'id' => $unit->id
        ]);
    }

    public function test_cannot_delete_non_existent_measurement_unit(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/internal/measurement-units/" . $this->faker->uuid);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_bulk_delete_measurement_units(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $units = MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $units->pluck('id')->toArray()
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson('/api/internal/measurement-units/bulk-delete', $data);

        // Assert
        $response->assertStatus(204);
        $this->assertEquals(0, MeasurementUnit::whereIn('id', $units->pluck('id'))->count());
    }

    public function test_cannot_bulk_delete_measurement_units_without_cabinet_id(): void
    {
        // Arrange
        $data = [
            'ids' => [$this->faker->uuid()]
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson('/api/internal/measurement-units/bulk-delete', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_bulk_delete_measurement_units_with_invalid_ids(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => ['invalid-uuid']
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson('/api/internal/measurement-units/bulk-delete', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids.0']);
    }

    public function test_cannot_bulk_delete_measurement_units_from_other_cabinet(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $units = MeasurementUnit::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'group_id' => $group->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $units->pluck('id')->toArray()
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson('/api/internal/measurement-units/bulk-delete', $data);

        // Assert
        $response->assertStatus(403);
        $this->assertEquals(3, MeasurementUnit::whereIn('id', $units->pluck('id'))->count());
    }

    public function test_can_bulk_delete_base_measurement_unit_and_update_others(): void
    {
        // Arrange
        $group = MeasurementUnitGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем базовую единицу
        $baseUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 1
        ]);

        // Создаем небазовые единицы
        $unit1 = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 2
        ]);

        $unit2 = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'group_id' => $group->id,
            'conversion_factor' => 4
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => [$baseUnit->id]
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->deleteJson('/api/internal/measurement-units/bulk-delete', $data);

        // Assert
        $response->assertStatus(204);

        // Проверяем, что базовая единица удалена
        $this->assertDatabaseMissing('measurement_units', [
            'id' => $baseUnit->id
        ]);

        // Проверяем, что коэффициенты других единиц обновились
        $this->assertDatabaseHas('measurement_units', [
            'id' => $unit1->id,
            'conversion_factor' => 1 // 2 / 2 (старый фактор unit1)
        ]);

        $this->assertDatabaseHas('measurement_units', [
            'id' => $unit2->id,
            'conversion_factor' => 2 // 4 / 2 (старый фактор unit1)
        ]);
    }
}
