<?php

namespace Tests\Feature;

use App\Models\User;
use App\Enums\Api\Internal\BranchTypeEnum;
use App\Enums\Api\Internal\LegalEntityType;
use Tests\TestCase;
use App\Enums\Api\Internal\OrganizationStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use App\Contracts\Services\Internal\DadataClientInterface;
use Mockery;

class SuggestionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем мок DadataClient
        $dadataMock = Mockery::mock(DadataClientInterface::class);

        // Настраиваем ожидаемое поведение для party
        $dadataMock->shouldReceive('suggest');

        // Регистрируем мок в сервис-контейнере
        $this->app->instance(DadataClientInterface::class, $dadataMock);
    }

    /**
     * A basic feature test example.
     */
    public function test_find_party_suggestion(): void
    {
        $user = User::factory()->create();

        $data = [
            'query' => 'Москва',
            'kpp' => 5,
            'branch_type' => BranchTypeEnum::BRANCH->value,
            'type' => LegalEntityType::LEGAL->value,
            'status' => [
                OrganizationStatusEnum::ACTIVE->value
            ]
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/suggest/find-party?' . http_build_query($data));

        $response->assertStatus(200)
            ->assertJsonStructure([
                    '*' => [
                        'value',
                        'data' => [
                        ]
                    ]
            ]);
    }

    public function test_find_party_suggestion_without_required_params(): void
    {
        $user = User::factory()->create();

        $data = [
            'kpp' => 5,
            'branch_type' => BranchTypeEnum::BRANCH->value,
            'type' => LegalEntityType::LEGAL->value,
            'status' => [
                OrganizationStatusEnum::ACTIVE->value
            ]
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/suggest/find-party?' . http_build_query($data));

        $response->assertJsonValidationErrors('query');
    }


    public function test_bank_suggestion(): void
    {
        $user = User::factory()->create();

        $data = [
            'query' => 'Сбербанк',
            'status' => [
                OrganizationStatusEnum::ACTIVE->value
            ],
            'type' => LegalEntityType::LEGAL->value,
            'locations' => [
                [
                    'kladr_id' => 1
                ]
            ],
            'locations_boost' => [
                [
                    'kladr_id' => 2
                ]
            ]
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/suggest/bank?' . http_build_query($data));

        $response->assertStatus(200)
            ->assertJsonStructure([
                    '*' => [
                        'value',
                        'data' => [
                        ]
                    ]
            ]);
    }

    public function test_bank_suggestion_without_required_params(): void
    {
        $user = User::factory()->create();

        $data = [
            'status' => [
                OrganizationStatusEnum::ACTIVE->value
            ],
            'type' => LegalEntityType::LEGAL->value,
            'locations' => [
                [
                    'kladr_id' => 1
                ]
            ],
            'locations_boost' => [
                [
                    'kladr_id' => 2
                ]
            ]
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/suggest/bank?' . http_build_query($data));

        $response->assertJsonValidationErrors('query');
    }

    public function test_address_suggestion(): void
    {
        $user = User::factory()->create();

        $data = [
            'query' => 'Москва',
            'language' => 'ru',
            'division' => 'ADMINISTRATIVE',
            'locations' => [
                [
                    'country_iso_code' => 'RU',
                    'region_iso_code' => 'MOS',
                    'kladr_id' => 1,
                    'region' => 'Москва',
                    'city' => 'Москва',
                    'area' => 'Москва',
                    'settlement' => 'Москва',
                    'street' => 'Москва',
                    'country' => 'Москва',
                    'region_type_full' => 'Москва',
                    'area_type_full' => 'Москва',
                    'city_type_full' => 'Москва',
                ]
            ]
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/suggest/address?' . http_build_query($data));

        $response->assertStatus(200)
            ->assertJsonStructure([
                    '*' => [
                        'value',
                        'data' => [
                        ]
                    ]
            ]);
    }

    public function test_address_suggestion_without_required_params(): void
    {
        $user = User::factory()->create();

        $data = [
            'language' => 'ru',
            'division' => 'ADMINISTRATIVE',
            'locations' => [
                [
                    'country_iso_code' => 'RU',
                    'region_iso_code' => 'MOS',
                    'kladr_id' => 1,
                    'region' => 'Москва',
                    'city' => 'Москва',
                    'area' => 'Москва',
                    'settlement' => 'Москва',
                    'street' => 'Москва',
                    'country' => 'Москва',
                    'region_type_full' => 'Москва',
                    'area_type_full' => 'Москва',
                    'city_type_full' => 'Москва',
                ]
            ]
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/suggest/address?' . http_build_query($data));

        $response->assertJsonValidationErrors('query');
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
