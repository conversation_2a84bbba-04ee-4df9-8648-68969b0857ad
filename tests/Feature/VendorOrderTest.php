<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\FilterConditionEnum;
use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\Status;
use App\Models\User;
use App\Models\VendorOrder;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class VendorOrderTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        Queue::fake();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_vendor_orders_list(): void
    {
        // Создаем заказы поставщикам для текущего кабинета
        VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем заказ для другого кабинета
        VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'number',
                        'date_from',
                        'status_id',
                        'held',
                        'waiting',
                        'legal_entity_id',
                        'contractor_id',
                        'plan_date',
                        'warehouse_id',
                        'department_id',
                        'employee_id',
                        'is_common',
                        'has_vat',
                        'price_includes_vat',
                        'comment'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_cannot_access_other_cabinet_vendor_orders(): void
    {
        // Создаем заказы в другом кабинете
        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $response->assertJsonCount(2, 'data');
    }

    public function test_index_without_required_cabinet_id(): void
    {
        $response = $this->getJson('/api/internal/vendor-orders');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_filter_by_warehouse(): void
    {
        $warehouse1 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse2 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем заказы для разных складов
        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse1->id
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse2->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'value' => [$warehouse1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($warehouse1->id, $item['warehouse_id']);
        }
    }

    public function test_index_filter_by_legal_entity(): void
    {
        $legalEntity1 = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity2 = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);

        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity1->id
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity2->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'legal_entity' => [
                    'value' => [$legalEntity1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($legalEntity1->id, $item['legal_entity_id']);
        }
    }

    public function test_index_filter_by_employee(): void
    {
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();

        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee1->id
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee2->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'value' => [$employee1->id],
                    'condition' => FilterConditionEnum::IN->value
                ],
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($employee1->id, $item['employee_id']);
        }
    }

    public function test_index_filter_by_contractor(): void
    {
        $contractor1 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor2 = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractors' => [
                    'value' => [$contractor1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($contractor1->id, $item['contractor_id']);
        }
    }

    public function test_index_filter_by_department(): void
    {
        $department1 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department2 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department1->id
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department2->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'value' => [$department1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($department1->id, $item['department_id']);
        }
    }

    public function test_index_filter_by_status(): void
    {
        $status1 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status2 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status1->id
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status2->id
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$status1->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($status1->id, $item['status_id']);
        }
    }

    public function test_index_filter_by_is_common(): void
    {
        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => true
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => false
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => ['value' => true]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertTrue($item['is_common']);
        }
    }

    public function test_index_filter_by_is_held(): void
    {
        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => true
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_held' => [
                    'value' => true
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertTrue($item['held']);
        }
    }

    public function test_index_filter_by_period(): void
    {
        $fromDate = now()->subDays(5);
        $toDate = now()->subDays(2);

        // Создаем заказы в разные периоды
        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => now()->subDays(6) // До периода
        ]);

        VendorOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => now()->subDays(3) // В периоде
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => now()->subDay() // После периода
        ]);

        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'period' => [
                    'from' => $fromDate->format('d.m.Y H:i'),
                    'to' => $toDate->format('d.m.Y H:i')
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $createdAt = \Carbon\Carbon::parse($item['created_at']);
            $this->assertTrue($createdAt->between($fromDate, $toDate));
        }
    }

    public function test_index_filter_by_search(): void
    {
        // Создаем заказы с разными номерами и комментариями
        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'TEST123',
            'comment' => 'Regular comment'
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'ABC456',
            'comment' => 'Test comment here'
        ]);

        VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'XYZ789',
            'comment' => 'Another comment'
        ]);

        // Поиск по номеру
        $response = $this->getJson('/api/internal/vendor-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'test'
                ]
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');
    }

    public function test_can_create_vendor_order(): void
    {
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'number' => 'TEST-001',
            'date_from' => now()->format('Y-m-d'),
            'status_id' => $status->id,
            'held' => true,
            'waiting' => false,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'plan_date' => now()->addDays(5)->format('Y-m-d'),
            'warehouse_id' => $warehouse->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'is_common' => true,
            'has_vat' => true,
            'price_includes_vat' => true,
            'comment' => 'Test comment'
        ];

        $response = $this->postJson('/api/internal/vendor-orders', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('vendor_orders', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'status_id' => $data['status_id'],
            'held' => $data['held'],
            'waiting' => $data['waiting'],
            'legal_entity_id' => $data['legal_entity_id'],
            'contractor_id' => $data['contractor_id'],
            'plan_date' => $data['plan_date'],
            'warehouse_id' => $data['warehouse_id'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id'],
            'is_common' => $data['is_common'],
            'has_vat' => $data['has_vat'],
            'price_includes_vat' => $data['price_includes_vat'],
            'comment' => $data['comment']
        ]);
    }

    public function test_cannot_create_vendor_order_in_another_cabinet(): void
    {
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $department = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $employee = Employee::factory()->create();
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'department_id' => $department->id,
            'employee_id' => $employee->id,
            'currency_id' => $currency->id
        ];

        $response = $this->postJson('/api/internal/vendor-orders', $data);

        $response->assertStatus(403);

        $this->assertDatabaseMissing('vendor_orders', [
            'cabinet_id' => $this->otherCabinet->id
        ]);
    }

    public function test_store_validation_errors(): void
    {
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'number' => ['not-a-string'],
            'date_from' => 'invalid-date',
            'status_id' => 'not-a-uuid',
            'held' => 'not-a-boolean',
            'waiting' => 'not-a-boolean',
            'legal_entity_id' => 'not-a-uuid',
            'contractor_id' => 'not-a-uuid',
            'plan_date' => 'invalid-date',
            'warehouse_id' => 'not-a-uuid',
            'department_id' => 'not-a-uuid',
            'employee_id' => 'not-a-uuid',
            'is_common' => 'not-a-boolean',
            'has_vat' => 'not-a-boolean',
            'price_includes_vat' => 'not-a-boolean',
            'comment' => ['not-a-string']
        ];

        $response = $this->postJson('/api/internal/vendor-orders', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'number',
                'date_from',
                'status_id',
                'held',
                'waiting',
                'legal_entity_id',
                'contractor_id',
                'plan_date',
                'warehouse_id',
                'department_id',
                'employee_id',
                'is_common',
                'has_vat',
                'price_includes_vat',
                'comment'
            ]);
    }

    public function test_can_create_vendor_order_with_minimum_required_fields(): void
    {
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
        ];

        $response = $this->postJson('/api/internal/vendor-orders', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('vendor_orders', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'legal_entity_id' => $data['legal_entity_id'],
            'contractor_id' => $data['contractor_id'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id'],
        ]);
    }

    public function test_store_required_fields_validation(): void
    {
        $response = $this->postJson('/api/internal/vendor-orders', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'legal_entity_id',
                'contractor_id',
                'department_id',
                'employee_id'
            ]);
    }

    public function test_store_with_invalid_relations(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $this->faker->uuid(), // несуществующий
            'contractor_id' => $this->faker->uuid(), // несуществующий
            'department_id' => $this->faker->uuid(), // несуществующий
            'employee_id' => $this->faker->uuid(), // несуществующий
        ];

        $response = $this->postJson('/api/internal/vendor-orders', $data);

        $response->assertNotFound();
    }

    public function test_store_with_invalid_dates(): void
    {
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'date_from' => 'invalid-date',
            'plan_date' => 'invalid-date'
        ];

        $response = $this->postJson('/api/internal/vendor-orders', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'date_from',
                'plan_date'
            ]);
    }

    public function test_can_update_vendor_order(): void
    {
        // Создаем начальный заказ
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'OLD-001',
            'comment' => 'Old comment'
        ]);

        // Создаем новые связанные сущности
        $newLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $newContractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $newWarehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $newStatus = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $newCurrency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'number' => 'NEW-001',
            'date_from' => now()->format('Y-m-d'),
            'status_id' => $newStatus->id,
            'held' => true,
            'waiting' => false,
            'legal_entity_id' => $newLegalEntity->id,
            'contractor_id' => $newContractor->id,
            'plan_date' => now()->addDays(5)->format('Y-m-d'),
            'warehouse_id' => $newWarehouse->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'is_common' => true,
            'comment' => 'Updated comment'
        ];

        $response = $this->putJson("/api/internal/vendor-orders/{$vendorOrder->id}", $data);

        $response->assertStatus(204);

        $this->assertDatabaseHas('vendor_orders', [
            'id' => $vendorOrder->id,
            'cabinet_id' => $this->cabinet->id, // cabinet_id не должен измениться
            'status_id' => $data['status_id'],
            'held' => $data['held'],
            'waiting' => $data['waiting'],
            'legal_entity_id' => $data['legal_entity_id'],
            'contractor_id' => $data['contractor_id'],
            'plan_date' => $data['plan_date'],
            'warehouse_id' => $data['warehouse_id'],
            'department_id' => $data['department_id'],
            'employee_id' => $data['employee_id'],
            'is_common' => $data['is_common'],
            'comment' => $data['comment']
        ]);
    }

    public function test_cannot_update_vendor_order_from_another_cabinet(): void
    {
        // Создаем заказ в другом кабинете
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'department_id' => Department::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'employee_id' => Employee::factory()->create()->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id
        ];

        $response = $this->putJson("/api/internal/vendor-orders/{$vendorOrder->id}", $data);

        $response->assertStatus(404);

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('vendor_orders', [
            'id' => $vendorOrder->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);
    }

    public function test_cannot_update_non_existent_vendor_order(): void
    {
        $data = [
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id
        ];

        $response = $this->putJson("/api/internal/vendor-orders/{$this->faker->uuid()}", $data);

        $response->assertStatus(404);
    }

    public function test_update_validation_errors(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'number' => ['not-a-string'],
            'date_from' => 'invalid-date',
            'status_id' => 'not-a-uuid',
            'held' => 'not-a-boolean',
            'waiting' => 'not-a-boolean',
            'legal_entity_id' => 'not-a-uuid',
            'contractor_id' => 'not-a-uuid',
            'plan_date' => 'invalid-date',
            'warehouse_id' => 'not-a-uuid',
            'department_id' => 'not-a-uuid',
            'employee_id' => 'not-a-uuid',
            'is_common' => 'not-a-boolean',
            'comment' => ['not-a-string']
        ];

        $response = $this->putJson("/api/internal/vendor-orders/{$vendorOrder->id}", $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'number',
                'date_from',
                'status_id',
                'held',
                'waiting',
                'legal_entity_id',
                'contractor_id',
                'plan_date',
                'warehouse_id',
                'department_id',
                'employee_id',
                'is_common',
                'comment'
            ]);
    }

    public function test_can_update_vendor_order_with_minimum_required_fields(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
        ];

        $response = $this->putJson("/api/internal/vendor-orders/{$vendorOrder->id}", $data);

        $response->assertStatus(204);

        $this->assertDatabaseHas('vendor_orders', array_merge(
            ['id' => $vendorOrder->id],
            $data
        ));
    }

    public function test_update_with_invalid_relations(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'legal_entity_id' => $this->faker->uuid(),
            'contractor_id' => $this->faker->uuid(),
            'department_id' => $this->faker->uuid(),
            'employee_id' => $this->faker->uuid(),
            'currency_id' => $this->faker->uuid()
        ];

        $response = $this->putJson("/api/internal/vendor-orders/{$vendorOrder->id}", $data);

        $response->assertNotFound();
    }

    public function test_update_with_invalid_dates(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'date_from' => 'invalid-date',
            'plan_date' => 'invalid-date'
        ];

        $response = $this->putJson("/api/internal/vendor-orders/{$vendorOrder->id}", $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'date_from',
                'plan_date'
            ]);
    }

    public function test_can_show_vendor_order(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson("/api/internal/vendor-orders/{$vendorOrder->id}");

        $response->assertStatus(200);

        $this->assertEquals($vendorOrder->id, $response->json('id'));
        $this->assertEquals($this->cabinet->id, $response->json('cabinet_id'));
    }

    public function test_cannot_show_vendor_order_from_another_cabinet(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson("/api/internal/vendor-orders/{$vendorOrder->id}");

        $response->assertStatus(404);
    }

    public function test_cannot_show_non_existent_vendor_order(): void
    {
        $response = $this->getJson("/api/internal/vendor-orders/{$this->faker->uuid()}");

        $response->assertStatus(404);
    }

    public function test_can_delete_vendor_order(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->deleteJson("/api/internal/vendor-orders/{$vendorOrder->id}");

        $response->assertStatus(204);

        $this->assertSoftDeleted('vendor_orders', [
            'id' => $vendorOrder->id
        ]);
    }

    public function test_cannot_delete_vendor_order_from_another_cabinet(): void
    {
        $vendorOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->deleteJson("/api/internal/vendor-orders/{$vendorOrder->id}");

        $response->assertStatus(404);

        $this->assertDatabaseHas('vendor_orders', [
            'id' => $vendorOrder->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_non_existent_vendor_order(): void
    {
        $response = $this->deleteJson("/api/internal/vendor-orders/{$this->faker->uuid()}");

        $response->assertStatus(404);
    }

    public function test_bulk_delete_validation_errors(): void
    {
        $response = $this->deleteJson('/api/internal/vendor-orders/bulk-delete', [
            'cabinet_id' => 'not-a-uuid',
            'ids' => 'not-an-array'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids'
            ]);
    }

    public function test_can_bulk_delete_vendor_orders(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->deleteJson('/api/internal/vendor-orders/bulk-delete', $data);

        $response->assertStatus(204);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseMissing('vendor_orders', [
                'id' => $vendorOrder->id
            ]);
        }
    }

    public function test_cannot_bulk_delete_vendor_orders_from_another_cabinet(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->deleteJson('/api/internal/vendor-orders/bulk-delete', $data);

        $response->assertStatus(403);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'deleted_at' => null
            ]);
        }
    }

    public function test_can_bulk_held_vendor_orders(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-held', $data);

        $response->assertStatus(204);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'held' => true
            ]);
        }
    }

    public function test_cannot_bulk_held_vendor_orders_from_another_cabinet(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => false
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-held', $data);

        $response->assertStatus(403);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'held' => false
            ]);
        }
    }

    public function test_can_bulk_unheld_vendor_orders(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-unheld', $data);

        $response->assertStatus(204);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'held' => false
            ]);
        }
    }

    public function test_cannot_bulk_unheld_vendor_orders_from_another_cabinet(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => true
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-unheld', $data);

        $response->assertStatus(403);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'held' => true
            ]);
        }
    }

    public function test_can_bulk_waiting_vendor_orders(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'waiting' => false
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-waiting', $data);

        $response->assertStatus(204);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'waiting' => true
            ]);
        }
    }

    public function test_cannot_bulk_waiting_vendor_orders_from_another_cabinet(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'waiting' => false
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-waiting', $data);

        $response->assertStatus(403);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'waiting' => false
            ]);
        }
    }

    public function test_can_bulk_unwaiting_vendor_orders(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'waiting' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-unwaiting', $data);

        $response->assertStatus(204);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'waiting' => false
            ]);
        }
    }

    public function test_cannot_bulk_unwaiting_vendor_orders_from_another_cabinet(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'waiting' => true
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->patchJson('/api/internal/vendor-orders/bulk-unwaiting', $data);

        $response->assertStatus(403);

        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'id' => $vendorOrder->id,
                'waiting' => true
            ]);
        }
    }

    public function test_bulk_copy_validation_errors(): void
    {
        $response = $this->postJson('/api/internal/vendor-orders/bulk-copy', [
            'cabinet_id' => 'not-a-uuid',
            'ids' => 'not-an-array'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids'
            ]);
    }

    public function test_can_bulk_copy_vendor_orders(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->postJson('/api/internal/vendor-orders/bulk-copy', $data);

        $response->assertStatus(204);

        // Проверяем что для каждого заказа создана копия
        foreach ($vendorOrders as $vendorOrder) {
            $this->assertDatabaseHas('vendor_orders', [
                'cabinet_id' => $vendorOrder->cabinet_id,
                'legal_entity_id' => $vendorOrder->legal_entity_id,
                'contractor_id' => $vendorOrder->contractor_id,
                'department_id' => $vendorOrder->department_id,
                'employee_id' => $vendorOrder->employee_id,
                'held' => false, // Копия не должна быть проведена
                'waiting' => false // Копия не должна быть в ожидании
            ]);
        }
    }

    public function test_cannot_bulk_copy_vendor_orders_from_another_cabinet(): void
    {
        $vendorOrders = VendorOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $vendorOrders->pluck('id')->toArray()
        ];

        $response = $this->postJson('/api/internal/vendor-orders/bulk-copy', $data);

        $response->assertStatus(403);

        // Проверяем что копии не были созданы
        $this->assertEquals(
            $vendorOrders->count(),
            VendorOrder::where('cabinet_id', $this->otherCabinet->id)->count()
        );
    }
}
