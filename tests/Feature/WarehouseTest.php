<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Warehouse;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use Illuminate\Support\Facades\DB;
use App\Enums\Api\Internal\FilterConditionEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WarehouseTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_warehouses_list(): void
    {
        // Arrange
        // Создаем 3 склада для нашего кабинета
        Warehouse::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем склад для другого кабинета
        Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'archived_at',
                        'name',
                        'cabinet_id',
                        'work_schedule_id',
                        'control_free_residuals',
                        'address_id',
                        'phone_id',
                        'responsible_employee_id',
                        'is_default',
                        'department_id',
                        'employee_id',
                        'is_common',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_warehouses(): void
    {
        // Arrange
        Warehouse::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertForbidden();
    }

    public function test_index_with_valid_parameters(): void
    {
        // Arrange
        Warehouse::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'name', 'cabinet_id']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'cabinet_id'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $response->assertJsonCount(2, 'data');
    }

    public function test_index_returns_empty_collection_when_no_warehouses(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_index_with_filters(): void
    {
        // Arrange
        $warehouse1 = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Warehouse 1',
            'is_common' => true
        ]);

        $warehouse2 = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Another Warehouse',
            'is_common' => false
        ]);

        $employee = Employee::factory()->create();
        $department = Department::factory()->create();

        // Act - фильтр по имени
        $response = $this->getJson('/api/internal/warehouses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'name' => [
                    'value' => 'Test',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'id' => $warehouse1->id,
                        'name' => 'Test Warehouse 1'
                    ]
                ]
            ]);

        // Act - фильтр по is_common
        $response = $this->getJson('/api/internal/warehouses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => ['value' => true]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'id' => $warehouse1->id,
                        'is_common' => true
                    ]
                ]
            ]);
    }

    public function test_index_without_required_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_can_create_warehouse(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Warehouse',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'responsible_employee_id' => $this->employee->id,
            'is_common' => true,
            'control_free_residuals' => true,
            'order_scheme' => [
                'on_coming_from' => now()->format('Y-m-d'),
                'on_shipment_from' => now()->format('Y-m-d'),
                'control_operational_balances' => true
            ],
            'structure' => [
                'use_premises_from' => now()->format('Y-m-d'),
                'cells' => true,
                'use_cells_from' => now()->format('Y-m-d')
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouses', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id'],
            'responsible_employee_id' => $data['responsible_employee_id'],
            'is_common' => $data['is_common'],
            'control_free_residuals' => $data['control_free_residuals']
        ]);

        $this->assertDatabaseHas('warehouse_structures', [
            'warehouse_id' => $response->json('id'),
            'use_premises_from' => $data['structure']['use_premises_from'],
            'cells' => $data['structure']['cells'],
            'use_cells_from' => $data['structure']['use_cells_from']
        ]);

        $this->assertDatabaseHas('warehouse_order_schemes', [
            'warehouse_id' => $response->json('id'),
            'on_coming_from' => $data['order_scheme']['on_coming_from'],
            'on_shipment_from' => $data['order_scheme']['on_shipment_from'],
            'control_operational_balances' => $data['order_scheme']['control_operational_balances']
        ]);
    }

    public function test_can_create_warehouse_with_minimal_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Warehouse',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouses', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id']
        ]);

        // Проверяем что структура и схема ордеров созданы с дефолтными значениями
        $this->assertDatabaseHas('warehouse_structures', [
            'warehouse_id' => $response->json('id'),
            'cells' => false
        ]);

        $this->assertDatabaseHas('warehouse_order_schemes', [
            'warehouse_id' => $response->json('id'),
            'control_operational_balances' => false
        ]);
    }

    public function test_cannot_create_warehouse_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Warehouse',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses', $data);

        // Assert
        $response->assertForbidden();
    }

    public function test_cannot_create_warehouse_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'name' => '',
            'employee_id' => 'not-a-uuid',
            'department_id' => 'not-a-uuid',
            'responsible_employee_id' => 'not-a-uuid',
            'is_common' => 'not-a-boolean',
            'control_free_residuals' => 'not-a-boolean',
            'order_scheme' => [
                'on_coming_from' => 'not-a-date',
                'on_shipment_from' => 'not-a-date',
                'control_operational_balances' => 'not-a-boolean'
            ],
            'structure' => [
                'use_premises_from' => 'not-a-date',
                'cells' => 'not-a-boolean',
                'use_cells_from' => 'not-a-date'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'employee_id',
                'department_id',
                'responsible_employee_id',
                'is_common',
                'control_free_residuals',
                'order_scheme.on_coming_from',
                'order_scheme.on_shipment_from',
                'order_scheme.control_operational_balances',
                'structure.use_premises_from',
                'structure.cells',
                'structure.use_cells_from'
            ]);
    }

    public function test_cannot_create_warehouse_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/warehouses', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'employee_id',
                'department_id'
            ]);
    }

    public function test_cannot_create_warehouse_with_premises_without_order_scheme(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Warehouse',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'structure' => [
                'use_premises_from' => now()->format('Y-m-d')
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses', $data);

        // Assert
        $response->assertStatus(500)
            ->assertJson([
                'error' => 'An error occurred while processing your request. Premises accounting is provided only for warehouses that use a warrant scheme for all warehouse operations.'
            ]);
    }

    public function test_can_update_warehouse(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Old Name',
            'department_id' => $this->department->id,
            'is_common' => false,
            'control_free_residuals' => false
        ]);

        DB::table('warehouse_order_schemes')
            ->insert([
                'warehouse_id' => $warehouse->id,
                'id' => $this->faker()->uuid,
                'control_operational_balances' => false,
            ]);

        $newDepartment = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => 'New Name',
            'employee_id' => $this->employee->id,
            'department_id' => $newDepartment->id,
            'responsible_employee_id' => $this->employee->id,
            'is_common' => true,
            'control_free_residuals' => true,
            'order_scheme' => [
                'control_operational_balances' => true
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/{$warehouse->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouses', [
            'id' => $warehouse->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => $data['name'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id'],
            'responsible_employee_id' => $data['responsible_employee_id'],
            'is_common' => $data['is_common'],
            'control_free_residuals' => $data['control_free_residuals']
        ]);

        $this->assertDatabaseHas('warehouse_order_schemes', [
            'warehouse_id' => $warehouse->id,
            'control_operational_balances' => $data['order_scheme']['control_operational_balances']
        ]);
    }

    public function test_can_update_warehouse_with_minimal_fields(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Old Name',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        $data = [
            'name' => 'New Name',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/{$warehouse->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouses', [
            'id' => $warehouse->id,
            'name' => $data['name'],
            'employee_id' => $data['employee_id'],
            'department_id' => $data['department_id']
        ]);
    }

    public function test_cannot_update_warehouse_from_other_cabinet(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => 'New Name',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/{$otherWarehouse->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('warehouses', [
            'id' => $otherWarehouse->id,
            'name' => $data['name']
        ]);
    }

    public function test_cannot_update_warehouse_with_invalid_data(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => '',
            'employee_id' => 'not-a-uuid',
            'department_id' => 'not-a-uuid',
            'responsible_employee_id' => 'not-a-uuid',
            'is_common' => 'not-a-boolean',
            'control_free_residuals' => 'not-a-boolean',
            'order_scheme' => [
                'control_operational_balances' => 'not-a-boolean'
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/{$warehouse->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'employee_id',
                'department_id',
                'responsible_employee_id',
                'is_common',
                'control_free_residuals',
                'order_scheme.control_operational_balances'
            ]);
    }

    public function test_cannot_update_warehouse_without_required_fields(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/warehouses/{$warehouse->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'employee_id',
                'department_id'
            ]);
    }

    public function test_cannot_update_non_existent_warehouse(): void
    {
        // Arrange
        $data = [
            'name' => 'New Name',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/" . $this->faker->uuid(), $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_show_warehouse(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Warehouse',
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'responsible_employee_id' => $this->employee->id,
            'is_common' => true,
            'control_free_residuals' => true
        ]);

        DB::table('warehouse_order_schemes')
            ->insert([
                'warehouse_id' => $warehouse->id,
                'id' => $this->faker()->uuid,
                'control_operational_balances' => true,
                'on_coming_from' => now()->format('Y-m-d'),
                'on_shipment_from' => now()->format('Y-m-d')
            ]);

        DB::table('warehouse_structures')
            ->insert([
                'warehouse_id' => $warehouse->id,
                'id' => $this->faker()->uuid,
                'cells' => true,
                'use_premises_from' => now()->format('Y-m-d'),
                'use_cells_from' => now()->format('Y-m-d')
            ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/{$warehouse->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'deleted_at',
                'archived_at',
                'name',
                'cabinet_id',
                'work_schedule_id',
                'control_free_residuals',
                'address_id',
                'phone_id',
                'responsible_employee_id',
                'is_default',
                'department_id',
                'employee_id',
                'is_common',
                'order_scheme' => [
                    'on_coming_from',
                    'on_shipment_from',
                    'control_operational_balances'
                ],
                'structure' => [
                    'use_premises_from',
                    'cells',
                    'use_cells_from'
                ]
            ])
            ->assertJson([
                'id' => $warehouse->id,
                'cabinet_id' => $this->cabinet->id,
                'name' => 'Test Warehouse',
                'employee_id' => $this->employee->id,
                'department_id' => $this->department->id,
                'responsible_employee_id' => $this->employee->id,
                'is_common' => true,
                'control_free_residuals' => true
            ]);
    }

    public function test_cannot_show_warehouse_from_other_cabinet(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/{$otherWarehouse->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_warehouse(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouses/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_warehouse(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        DB::table('warehouse_order_schemes')
            ->insert([
                'warehouse_id' => $warehouse->id,
                'id' => $this->faker()->uuid,
                'control_operational_balances' => true
            ]);

        DB::table('warehouse_structures')
            ->insert([
                'warehouse_id' => $warehouse->id,
                'id' => $this->faker()->uuid,
                'cells' => true
            ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/{$warehouse->id}");

        // Assert
        $response->assertStatus(204);

        // Проверяем что склад и связанные сущности удалены
        $this->assertSoftDeleted('warehouses', [
            'id' => $warehouse->id
        ]);
    }

    public function test_cannot_delete_warehouse_from_other_cabinet(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/{$otherWarehouse->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что склад не был удален
        $this->assertDatabaseHas('warehouses', [
            'id' => $otherWarehouse->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_non_existent_warehouse(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/warehouses/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }
}
