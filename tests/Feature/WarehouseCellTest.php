<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseCell;
use App\Models\WarehouseCellGroup;
use App\Models\WarehouseCellSize;
use App\Models\WarehouseStorageArea;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class WarehouseCellTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;
    private Warehouse $warehouse;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_warehouse_cells_list(): void
    {
        // Arrange
        // Создаем связанные сущности
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        // Создаем 3 ячейки для нашего склада
        $cells = WarehouseCell::factory()->count(3)->create([
            'warehouse_id' => $this->warehouse->id,
            'size_id' => $cellSize->id,
            'group_id' => $group->id
        ]);

        // Создаем связи с зоной хранения через прямую запись в БД
        foreach ($cells as $cell) {
            DB::table('warehouse_storage_area_cells')->insert([
                'storage_area_id' => $storageArea->id,
                'cell_id' => $cell->id
            ]);
        }

        // Создаем ячейку для другого склада, чтобы убедиться что не получим её
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $otherGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $otherWarehouse->id
        ]);

        WarehouseCell::factory()->create([
            'warehouse_id' => $otherWarehouse->id,
            'group_id' => $otherGroup->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'warehouse_id',
                        'group_id',
                        'type',
                        'address',
                        'description',
                        'section',
                        'line',
                        'rack',
                        'tier',
                        'position',
                        'separator',
                        'availability_level',
                        'circumvention_order',
                        'size_id',
                        'filling_volume',
                        'filling_weight',
                        'stocktake',
                        'recalculate_days',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего склада
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->warehouse->id, $item['warehouse_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_warehouse_cells(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $otherWarehouse->id
        ]);

        WarehouseCell::factory()->count(2)->create([
            'warehouse_id' => $otherWarehouse->id,
            'group_id' => $otherGroup->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells?' . http_build_query([
            'warehouse_id' => $otherWarehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/warehouses/cells?' . http_build_query([
            'warehouse_id' => 'not-a-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid',
            'sortField' => 'invalid_field',
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'warehouse_id',
                'page',
                'per_page',
                'sortDirection',
                'sortField',
                'fields.0',
                'fields.1'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        WarehouseCell::factory()->count(2)->create([
            'warehouse_id' => $this->warehouse->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/cells?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'group_id', 'warehouse_id']
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'group_id',
                        'warehouse_id'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $response->assertJsonCount(2, 'data');
    }

    public function test_index_returns_empty_collection_when_no_cells(): void
    {
        // Act
        $response = $this->getJson('/api/internal/warehouses/cells?' . http_build_query([
            'warehouse_id' => $this->warehouse->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'meta' => [
                    'total' => 0
                ]
            ]);
    }

    public function test_can_create_warehouse_cell(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'warehouse_id' => $this->warehouse->id,
            'group_id' => $group->id,
            'type' => 'storage',
            'address' => 'A-1-1-1',
            'description' => 'Test cell',
            'section' => 'A',
            'line' => '1',
            'rack' => '1',
            'tier' => '1',
            'position' => '1',
            'separator' => '-',
            'availability_level' => 1,
            'circumvention_order' => 1,
            'size_id' => $cellSize->id,
            'filling_volume' => 50,
            'filling_weight' => 50,
            'stocktake' => 1,
            'recalculate_days' => 30,
            'storage_area_id' => $storageArea->id
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $id = $response->json('id');

        $this->assertDatabaseHas('warehouse_cells', [
            'id' => $id,
            'warehouse_id' => $data['warehouse_id'],
            'group_id' => $data['group_id'],
            'type' => $data['type'],
            'address' => $data['address'],
            'description' => $data['description'],
            'section' => $data['section'],
            'line' => $data['line'],
            'rack' => $data['rack'],
            'tier' => $data['tier'],
            'position' => $data['position'],
            'separator' => $data['separator'],
            'availability_level' => $data['availability_level'],
            'circumvention_order' => $data['circumvention_order'],
            'size_id' => $data['size_id'],
            'filling_volume' => $data['filling_volume'],
            'filling_weight' => $data['filling_weight'],
            'stocktake' => $data['stocktake'],
            'recalculate_days' => $data['recalculate_days']
        ]);

        $this->assertDatabaseHas('warehouse_storage_area_cells', [
            'storage_area_id' => $data['storage_area_id'],
            'cell_id' => $id
        ]);
    }

    public function test_can_create_receiving_warehouse_cell(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $data = [
            'warehouse_id' => $this->warehouse->id,
            'group_id' => $group->id,
            'type' => 'receiving',
            'address' => 'RECEIVING-1'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_cells', [
            'id' => $response->json('id'),
            'warehouse_id' => $data['warehouse_id'],
            'type' => $data['type'],
            'address' => $data['address']
        ]);
    }

    public function test_cannot_create_warehouse_cell_in_other_cabinet(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'warehouse_id' => $otherWarehouse->id,
            'type' => 'storage',
            'address' => 'A-1-1-1'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_store_validation_errors(): void
    {
        // Arrange
        $data = [
            'warehouse_id' => 'not-a-uuid',
            'type' => 'invalid-type',
            'address' => str_repeat('a', 256),
            'filling_volume' => 101,
            'filling_weight' => 101,
            'stocktake' => 999,
            'group_id' => 'not-a-uuid',
            'size_id' => 'not-a-uuid',
            'storage_area_id' => 'not-a-uuid',
            'separator' => 'invalid-separator'
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'warehouse_id',
                'type',
                'address',
                'filling_volume',
                'filling_weight',
                'stocktake',
                'group_id',
                'size_id',
                'storage_area_id',
                'separator'
            ]);
    }

    public function test_store_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/warehouses/cells', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'warehouse_id',
                'type',
                'address'
            ]);
    }

    public function test_store_receiving_cell_excludes_storage_fields(): void
    {
        // Arrange
        $data = [
            'warehouse_id' => $this->warehouse->id,
            'type' => 'receiving',
            'address' => 'RECEIVING-1',
            // Эти поля должны быть проигнорированы для receiving типа
            'section' => 'A',
            'line' => '1',
            'rack' => '1',
            'tier' => '1',
            'position' => '1',
            'separator' => '-',
            'availability_level' => 1,
            'circumvention_order' => 1,
            'filling_volume' => 50,
            'filling_weight' => 50,
            'stocktake' => 1,
            'recalculate_days' => 30
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/cells', $data);

        // Assert
        $response->assertStatus(201);

        $this->assertDatabaseHas('warehouse_cells', [
            'id' => $response->json('id'),
            'warehouse_id' => $data['warehouse_id'],
            'type' => $data['type'],
            'address' => $data['address'],
            'section' => null,
            'line' => null,
            'rack' => null,
            'tier' => null,
            'position' => null,
            'separator' => null,
            'availability_level' => 0,
            'circumvention_order' => 0,
            'filling_volume' => 0,
            'filling_weight' => 0,
            'stocktake' => 0,
            'recalculate_days' => 0
        ]);
    }

    public function test_can_update_warehouse_cell(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $existingGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'type' => 'storage',
            'group_id' => $existingGroup->id
        ]);

        DB::table('warehouse_storage_area_cells')->insert([
            'storage_area_id' => $storageArea->id,
            'cell_id' => $cell->id
        ]);

        $updateData = [
            'group_id' => $group->id,
            'type' => 'storage',
            'address' => 'B-2-2-2',
            'description' => 'Updated cell',
            'section' => 'B',
            'line' => '2',
            'rack' => '2',
            'tier' => '2',
            'position' => '2',
            'separator' => '-',
            'availability_level' => 2,
            'circumvention_order' => 2,
            'size_id' => $cellSize->id,
            'filling_volume' => 75,
            'filling_weight' => 75,
            'stocktake' => 1,
            'recalculate_days' => 60,
            'storage_area_id' => $storageArea->id
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/{$cell->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cells', [
            'id' => $cell->id,
            'group_id' => $updateData['group_id'],
            'type' => $updateData['type'],
            'address' => $updateData['address'],
            'description' => $updateData['description'],
            'section' => $updateData['section'],
            'line' => $updateData['line'],
            'rack' => $updateData['rack'],
            'tier' => $updateData['tier'],
            'position' => $updateData['position'],
            'separator' => $updateData['separator'],
            'availability_level' => $updateData['availability_level'],
            'circumvention_order' => $updateData['circumvention_order'],
            'size_id' => $updateData['size_id'],
            'filling_volume' => $updateData['filling_volume'],
            'filling_weight' => $updateData['filling_weight'],
            'stocktake' => $updateData['stocktake'],
            'recalculate_days' => $updateData['recalculate_days']
        ]);

        $this->assertDatabaseHas('warehouse_storage_area_cells', [
            'storage_area_id' => $updateData['storage_area_id'],
            'cell_id' => $cell->id
        ]);
    }

    public function test_can_update_receiving_warehouse_cell(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'type' => 'receiving',
            'group_id' => $group->id
        ]);

        $updateData = [
            'type' => 'receiving',
            'address' => 'RECEIVING-2'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/{$cell->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_cells', [
            'id' => $cell->id,
            'type' => $updateData['type'],
            'address' => $updateData['address'],
            'section' => null,
            'line' => null,
            'rack' => null,
            'tier' => null,
            'position' => null,
            'separator' => null,
            'availability_level' => 0,
            'circumvention_order' => 0,
            'filling_volume' => 0,
            'filling_weight' => 0,
            'stocktake' => 0,
            'recalculate_days' => 0
        ]);
    }

    public function test_cannot_update_warehouse_cell_from_other_cabinet(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $otherWarehouse->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $otherWarehouse->id,
            'group_id' => $otherGroup->id
        ]);

        $updateData = [
            'type' => 'storage',
            'address' => 'A-1-1-1'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/{$cell->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_update_validation_errors(): void
    {
        // Arrange
        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'group_id' => $group->id
        ]);

        $invalidData = [
            'type' => 'invalid-type',
            'address' => str_repeat('a', 256),
            'filling_volume' => 101,
            'filling_weight' => 101,
            'stocktake' => 999,
            'group_id' => 'not-a-uuid',
            'size_id' => 'not-a-uuid',
            'separator' => 'invalid-separator'
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/{$cell->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'type',
                'address',
                'filling_volume',
                'filling_weight',
                'stocktake',
                'group_id',
                'size_id',
                'separator'
            ]);
    }

    public function test_cannot_update_non_existent_warehouse_cell(): void
    {
        // Act
        $response = $this->putJson("/api/internal/warehouses/cells/" . $this->faker->uuid(), [
            'type' => 'storage',
            'address' => 'A-1-1-1'
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_show_warehouse_cell(): void
    {
        // Arrange
        $cellSize = WarehouseCellSize::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $group = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'size_id' => $cellSize->id,
            'group_id' => $group->id,
            'type' => 'storage',
            'address' => 'A-1-1-1'
        ]);

        DB::table('warehouse_storage_area_cells')->insert([
            'storage_area_id' => $storageArea->id,
            'cell_id' => $cell->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/{$cell->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'warehouse_id',
                'group_id',
                'type',
                'address',
                'description',
                'section',
                'line',
                'rack',
                'tier',
                'position',
                'separator',
                'availability_level',
                'circumvention_order',
                'size_id',
                'filling_volume',
                'filling_weight',
                'stocktake',
                'recalculate_days',
                'size_name',
                'group_name',
                'warehouse_name',
                'storage_area_name'
            ]);

        $this->assertEquals($cell->id, $response->json('id'));
        $this->assertEquals($this->warehouse->id, $response->json('warehouse_id'));
        $this->assertEquals($cellSize->id, $response->json('size_id'));
        $this->assertEquals($group->id, $response->json('group_id'));
        $this->assertEquals('storage', $response->json('type'));
        $this->assertEquals('A-1-1-1', $response->json('address'));
    }

    public function test_cannot_show_warehouse_cell_from_other_cabinet(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherGroup = WarehouseCellGroup::factory()->create([
            'warehouse_id' => $otherWarehouse->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $otherWarehouse->id,
            'group_id' => $otherGroup->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/{$cell->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_warehouse_cell(): void
    {
        // Act
        $response = $this->getJson("/api/internal/warehouses/cells/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_warehouse_cell(): void
    {
        // Arrange
        $storageArea = WarehouseStorageArea::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $this->warehouse->id
        ]);

        DB::table('warehouse_storage_area_cells')->insert([
            'storage_area_id' => $storageArea->id,
            'cell_id' => $cell->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/{$cell->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_cells', [
            'id' => $cell->id
        ]);

        $this->assertDatabaseMissing('warehouse_storage_area_cells', [
            'cell_id' => $cell->id
        ]);
    }

    public function test_cannot_delete_warehouse_cell_from_other_cabinet(): void
    {
        // Arrange
        $otherWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $cell = WarehouseCell::factory()->create([
            'warehouse_id' => $otherWarehouse->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/{$cell->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('warehouse_cells', [
            'id' => $cell->id
        ]);
    }

    public function test_cannot_delete_non_existent_warehouse_cell(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/warehouses/cells/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }
}
