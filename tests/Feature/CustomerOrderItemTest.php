<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Product;
use App\Models\VatRate;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CustomerOrder;
use App\Models\CustomerOrderItem;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Queue;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CustomerOrderItemTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();
        Bus::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Создаем учетную валюту для кабинета
        \App\Models\CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_accouting' => true,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);
    }

    public function test_can_get_customer_order_items_list(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем 3 item для нашего заказа
        CustomerOrderItem::factory()->count(3)->create([
            'order_id' => $order->id
        ]);

        // Создаем item для другого заказа, чтобы убедиться что не получим его
        $otherOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        CustomerOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'order_id',
                        'product_id',
                        'quantity',
                        'price_in_currency',
                        'currency_id',
                        'currency_rate_to_base',
                        'price_in_base',
                        'amount_in_base',
                        'discount',
                        'vat_rate_id'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего заказа
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($order->id, $item['order_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_customer_order_items(): void
    {
        // Arrange
        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем items для заказа другого кабинета
        CustomerOrderItem::factory()->count(2)->create([
            'order_id' => $otherCabinetOrder->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders/items?' . http_build_query([
            'order_id' => $otherCabinetOrder->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/customer-orders/items?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'order_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'order_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        $order = CustomerOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        CustomerOrderItem::factory()->count(2)
            ->create(['order_id' => $order->id]);

        $response = $this->getJson('/api/internal/customer-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем количество записей
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_without_required_order_id(): void
    {
        $response = $this->getJson('/api/internal/customer-orders/items');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['order_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        $order = CustomerOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/customer-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        $order = CustomerOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/customer-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $order = CustomerOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем items с разными ценами
        $item1 = CustomerOrderItem::factory()->create([
            'order_id' => $order->id,
            'price_in_currency' => '100'
        ]);
        $item2 = CustomerOrderItem::factory()->create([
            'order_id' => $order->id,
            'price_in_currency' => '200'
        ]);
        $item3 = CustomerOrderItem::factory()->create([
            'order_id' => $order->id,
            'price_in_currency' => '150'
        ]);

        // Act - получаем отсортированный по цене список
        $response = $this->getJson('/api/internal/customer-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'sortField' => 'price_in_currency',
            'sortDirection' => 'asc',
            'fields' => ['id', 'price_in_currency']
        ]));

        // Assert
        $response->assertStatus(200);

        $prices = collect($response->json('data'))->pluck('price_in_currency')->values();
        $expectedPrices = collect([$item1->price_in_currency, $item3->price_in_currency, $item2->price_in_currency]);

        $this->assertEquals($expectedPrices, $prices);
    }

    public function test_can_create_customer_order_item(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $currency = \App\Models\CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(100, 99999),
            'currency_id' => $currency->id,
            'currency_rate_to_base' => '1.0',
            'discount' => random_int(0, 20),
            'vat_rate_id' => $vatRate->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas(
            'customer_order_items',
            array_merge(['id' => $response->json('id')], $data)
        );
    }

    public function test_cannot_create_customer_order_item_for_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'order_id' => $otherCabinetOrder->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(100, 10000),
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/items', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_customer_order_item_with_invalid_data(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'order_id' => 'invalid-uuid',
            'product_id' => 'invalid-uuid',
            'quantity' => -1, // отрицательное количество
            'price_in_currency' => 'not-a-number', // неверный формат цены
            'discount' => 'not-a-number', // неверный формат скидки
            'vat_rate_id' => 'invalid-uuid',
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/items', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'product_id',
                'quantity',
                'price_in_currency',
                'discount',
                'vat_rate_id'
            ]);
    }

    public function test_cannot_create_customer_order_item_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/customer-orders/items', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'product_id',
                'quantity'
            ]);
    }

    public function test_can_create_customer_order_item_with_minimal_required_fields(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('customer_order_items', [
            'id' => $response->json('id'),
            'order_id' => $data['order_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
        ]);
    }

    public function test_cannot_create_customer_order_item_with_product_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $otherCabinetProduct->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(100, 9999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/items', $data);

        // Assert
        $response->assertNotFound();
        $this->assertDatabaseMissing('customer_order_items', $data);
    }

    public function test_cannot_create_customer_order_item_with_vat_rate_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(100, 9999999),
            'vat_rate_id' => $vatRate->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/items', $data);

        // Assert
        $response->assertNotFound();
        $this->assertDatabaseMissing('customer_order_items', $data);
    }

    public function test_can_update_customer_order_item(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $orderItem = CustomerOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(100, 99999999),
            'discount' => random_int(0, 20),
            'vat_rate_id' => $vatRate->id,
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/items/{$orderItem->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('customer_order_items', array_merge(
            ['id' => $orderItem->id],
            $updateData
        ));
    }

    public function test_cannot_update_customer_order_item_from_other_cabinet(): void
    {
        // Arrange
        $otherOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherOrderItem = CustomerOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(100, 99999999),
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/items/{$otherOrderItem->id}", $updateData);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseMissing('customer_order_items', array_merge(
            ['id' => $otherOrderItem->id],
            $updateData
        ));
    }

    public function test_cannot_update_customer_order_item_with_invalid_data(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $orderItem = CustomerOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        $invalidData = [
            'quantity' => -1, // отрицательное количество
            'price_in_currency' => 'not-a-number', // неверный формат цены
            'discount' => 'not-a-number', // неверный формат скидки
            'vat_rate_id' => 'invalid-uuid',
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/items/{$orderItem->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'quantity',
                'price_in_currency',
                'discount',
                'vat_rate_id'
            ]);
    }

    public function test_cannot_update_non_existent_customer_order_item(): void
    {
        // Act
        $response = $this->putJson("/api/internal/customer-orders/items/" . $this->faker->uuid(), [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(100, 99999999),
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_customer_order_item_without_required_fields(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $orderItem = CustomerOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/customer-orders/items/{$orderItem->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['quantity']);
    }

    public function test_cannot_update_customer_order_item_with_vat_rate_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $orderItem = CustomerOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        $otherCabinetVatRate = VatRate::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price_in_currency' => random_int(0, 99999999),
            'vat_rate_id' => $otherCabinetVatRate->id
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/items/{$orderItem->id}", $updateData);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseMissing('customer_order_items', array_merge(
            ['id' => $orderItem->id],
            $updateData
        ));
    }

    public function test_can_show_customer_order_item(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $orderItem = CustomerOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/customer-orders/items/{$orderItem->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'order_id',
                'product_id',
                'quantity',
                'price_in_currency',
                'currency_id',
                'currency_rate_to_base',
                'price_in_base',
                'amount_in_base',
                'discount',
                'vat_rate_id'
            ]);

        $this->assertEquals($orderItem->id, $response->json('id'));
        $this->assertEquals($order->id, $response->json('order_id'));
    }

    public function test_cannot_show_customer_order_item_from_other_cabinet(): void
    {
        // Arrange
        $otherOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherOrderItem = CustomerOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/customer-orders/items/{$otherOrderItem->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_customer_order_item(): void
    {
        // Act
        $response = $this->getJson("/api/internal/customer-orders/items/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_customer_order_item(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $orderItem = CustomerOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/items/{$orderItem->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('customer_order_items', [
            'id' => $orderItem->id
        ]);
    }

    public function test_cannot_delete_customer_order_item_from_other_cabinet(): void
    {
        // Arrange
        $otherOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherOrderItem = CustomerOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/items/{$otherOrderItem->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('customer_order_items', [
            'id' => $otherOrderItem->id
        ]);
    }

    public function test_cannot_delete_non_existent_customer_order_item(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/items/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }
}
