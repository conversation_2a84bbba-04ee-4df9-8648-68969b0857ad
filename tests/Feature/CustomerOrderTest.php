<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\CustomerOrder;
use App\Models\CustomerOrderItem;
use App\Models\Department;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\Product;
use App\Models\SalesChannel;
use App\Models\Status;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class CustomerOrderTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();
        Bus::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_customer_orders_list(): void
    {
        // Arrange
        CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure();

        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_cannot_access_other_cabinet_customer_orders(): void
    {
        // Arrange
        CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => 'not-a-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_filter_warehouses_in(): void
    {
        // Arrange
        $warehouse1 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse2 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse3 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем склад в другом кабинете
        $otherWarehouse = Warehouse::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        // Создаем заказы с разными складами
        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse2->id
        ]);

        $order3 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse3->id
        ]);

        // Создаем заказ в другом кабинете с целевым складом
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'warehouse_id' => $otherWarehouse->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'condition' => 'IN',
                    'value' => [$warehouse1->id, $warehouse2->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data');

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertContains($order2->id, $orderIds);
        $this->assertNotContains($order3->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_warehouses_not_in(): void
    {
        // Arrange
        $warehouse1 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse2 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse3 = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse2->id
        ]);

        $order3 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse3->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'condition' => 'NOT_IN',
                    'value' => [$warehouse1->id, $warehouse2->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertNotContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertContains($order3->id, $orderIds);
    }

    public function test_index_filter_warehouses_empty(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $orderWithWarehouse = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $warehouse->id
        ]);

        $orderWithoutWarehouse = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'warehouses' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertNotContains($orderWithWarehouse->id, $orderIds);
        $this->assertContains($orderWithoutWarehouse->id, $orderIds);
    }

    public function test_index_filter_contractor_owners_in(): void
    {
        // Arrange
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();

        // Создаем контрагентов в текущем кабинете
        $contractor1 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee1->id
        ]);

        // Создаем контрагента в другом кабинете с тем же employee_id
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $employee1->id
        ]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'contractor_id' => $otherContractor->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'condition' => 'IN',
                    'value' => [$employee1->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_legal_entity_in(): void
    {
        // Arrange
        $legalEntity1 = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $legalEntity2 = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем юр.лицо в другом кабинете
        $otherLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity2->id
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'legal_entity_id' => $otherLegalEntity->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'legal_entity_ids' => [
                    'condition' => 'IN',
                    'value' => [$legalEntity1->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_employee_owners_in(): void
    {
        // Arrange
        $employee1 = Employee::factory()->create();
        $employee2 = Employee::factory()->create();

        // Создаем сотрудника в другом кабинете
        $otherEmployee = Employee::factory()->create();

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $employee2->id
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $employee1->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => 'IN',
                    'value' => [$employee1->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_statuses_in(): void
    {
        // Arrange
        $status1 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status2 = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем статус в другом кабинете
        $otherStatus = Status::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status2->id
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'status_id' => $otherStatus->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'condition' => 'IN',
                    'value' => [$status1->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_is_held(): void
    {
        // Arrange
        $heldOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => true
        ]);

        $notHeldOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => true
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_held' => [
                    'value' => true
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($heldOrder->id, $orderIds);
        $this->assertNotContains($notHeldOrder->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_period(): void
    {
        // Arrange
        $orderInPeriod = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => now()
        ]);

        $orderBeforePeriod = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => now()->subDays(10)
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'created_at' => now()
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'period' => [
                    'from' => now()->subDay()->format('d.m.Y H:i'),
                    'to' => now()->addDay()->format('d.m.Y H:i')
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($orderInPeriod->id, $orderIds);
        $this->assertNotContains($orderBeforePeriod->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_search(): void
    {
        // Arrange
        $orderToFind = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'TEST123ШВШВШ'
        ]);

        $otherOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'OTHER456'
        ]);

        // Создаем заказ в другом кабинете с похожим номером
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'number' => 'TEST789'
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'TEST'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $this->assertEquals($orderToFind->number, $response->json('data.0.number'));
    }

    public function test_index_filter_contractor_owners_not_in(): void
    {
        $contractor1 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $contractor2 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $contractor3 = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor2->id
        ]);

        $order3 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'contractor_id' => $contractor3->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'condition' => 'NOT_IN',
                    'value' => [$contractor1->employee_id, $contractor2->employee_id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertNotContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertContains($order3->id, $orderIds);
    }

    public function test_index_filter_contractor_owners_empty(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);

        CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'contractor_owners' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }

    public function test_index_filter_sales_channels_in(): void
    {
        // Arrange
        $salesChannel1 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel2 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем канал продаж в другом кабинете
        $otherSalesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel2->id
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'sales_channel_id' => $otherSalesChannel->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'condition' => 'IN',
                    'value' => [$salesChannel1->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_sales_channels_not_in(): void
    {
        // Arrange
        $salesChannel1 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel2 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel3 = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel2->id
        ]);

        $order3 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel3->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'condition' => 'NOT_IN',
                    'value' => [$salesChannel1->id, $salesChannel2->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertNotContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertContains($order3->id, $orderIds);
    }

    public function test_index_filter_sales_channels_empty(): void
    {
        // Arrange
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $orderWithChannel = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => $salesChannel->id
        ]);

        $orderWithoutChannel = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'sales_channel_id' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'sales_channels' => [
                    'condition' => 'EMPTY'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertNotContains($orderWithChannel->id, $orderIds);
        $this->assertContains($orderWithoutChannel->id, $orderIds);
    }

    public function test_index_filter_department_owners_in(): void
    {
        // Arrange
        $department1 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department2 = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $order1 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department1->id
        ]);

        $order2 = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $department2->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => 'IN',
                    'value' => [$department1->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
    }

    public function test_index_filter_products_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $product2 = Product::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем продукт в другом кабинете
        $otherProduct = Product::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $order1 = CustomerOrder::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $order2 = CustomerOrder::factory()->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        CustomerOrderItem::factory()->create([
            'order_id' => $order1->id,
            'product_id' => $product1->id
        ]);

        CustomerOrderItem::factory()->create([
            'order_id' => $order2->id,
            'product_id' => $product2->id
        ]);

        // Создаем позицию заказа в другом кабинете
        CustomerOrderItem::factory()->create([
            'order_id' => $otherCabinetOrder->id,
            'product_id' => $otherProduct->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'products' => [
                    'condition' => 'IN',
                    'value' => [$product1->id]
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($order1->id, $orderIds);
        $this->assertNotContains($order2->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_plan_date(): void
    {
        // Arrange
        $orderInPeriod = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'plan_date' => now()
        ]);

        $orderOutOfPeriod = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'plan_date' => now()->subDays(10)
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'plan_date' => [
                    'from' => now()->subDay()->format('d.m.Y H:i'),
                    'to' => now()->addDay()->format('d.m.Y H:i')
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($orderInPeriod->id, $orderIds);
        $this->assertNotContains($orderOutOfPeriod->id, $orderIds);
    }

    public function test_index_filter_updated_at(): void
    {
        // Arrange
        $orderInPeriod = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => now()
        ]);

        $orderOutOfPeriod = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => now()->subDays(10)
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'updated_at' => [
                    'from' => now()->subDay()->format('d.m.Y H:i'),
                    'to' => now()->addDay()->format('d.m.Y H:i')
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($orderInPeriod->id, $orderIds);
        $this->assertNotContains($orderOutOfPeriod->id, $orderIds);
    }

    public function test_index_filter_payment_status(): void
    {
        // Arrange
        $orderPaid = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'payment_status' => 'paid'
        ]);

        $orderUnpaid = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'payment_status' => 'unpaid'
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'payment_status' => 'paid'
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'payment_status' => [
                    'value' => 'paid'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($orderPaid->id, $orderIds);
        $this->assertNotContains($orderUnpaid->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_index_filter_is_common(): void
    {
        // Arrange
        $commonOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => true
        ]);

        $notCommonOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'is_common' => false
        ]);

        // Создаем заказ в другом кабинете
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'is_common' => true
        ]);

        // Act
        $response = $this->getJson('/api/internal/customer-orders?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'is_common' => [
                    'value' => true
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $orderIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($commonOrder->id, $orderIds);
        $this->assertNotContains($notCommonOrder->id, $orderIds);
        $this->assertNotContains($otherCabinetOrder->id, $orderIds);
    }

    public function test_can_create_customer_order(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'number' => 'TEST-001',
            'date_from' => now()->format('Y-m-d'),
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'plan_date' => now()->addDays(5)->format('Y-m-d'),
            'sales_channel_id' => $salesChannel->id,
            'warehouse_id' => $warehouse->id,
            'comment' => 'Test comment',
            'delivery_info' => [
                'comment' => 'Delivery comment',
                'post_code' => '123456',
                'country' => 'Russia',
                'region' => 'Moscow',
                'city' => 'Moscow',
                'street' => 'Test street',
                'house' => '1',
                'office' => '101',
                'other' => 'Additional info'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('customer_orders', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sales_channel_id' => $salesChannel->id,
            'warehouse_id' => $warehouse->id
        ]);
    }

    public function test_cannot_create_customer_order_with_legal_entity_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $otherCabinetLegalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $department->id,
            'currency_id' => $currency->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_customer_order_with_contractor_from_other_cabinet(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherCabinetContractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $otherCabinetContractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $department->id,
            'currency_id' => $currency->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_customer_order_with_department_from_other_cabinet(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherCabinetDepartment = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $otherCabinetDepartment->id,
            'currency_id' => $currency->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_customer_order_with_sales_channel_from_other_cabinet(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherCabinetSalesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $department->id,
            'currency_id' => $currency->id,
            'sales_channel_id' => $otherCabinetSalesChannel->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_customer_order_with_warehouse_from_other_cabinet(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $department = Department::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $otherCabinetWarehouse = Warehouse::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $department->id,
            'currency_id' => $currency->id,
            'warehouse_id' => $otherCabinetWarehouse->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_customer_order_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/customer-orders', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'legal_entity_id',
                'contractor_id',
                'employee_id',
                'department_id'
            ]);
    }

    public function test_cannot_create_customer_order_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'invalid-uuid',
            'number' => str_repeat('a', 256), // слишком длинный номер
            'date_from' => 'invalid-date',
            'legal_entity_id' => 'invalid-uuid',
            'contractor_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'plan_date' => 'invalid-date',
            'sales_channel_id' => 'invalid-uuid',
            'warehouse_id' => 'invalid-uuid',
            'delivery_info' => [
                'post_code' => str_repeat('a', 256),
                'country' => str_repeat('a', 256),
                'region' => str_repeat('a', 256),
                'city' => str_repeat('a', 256),
                'street' => str_repeat('a', 256),
                'house' => str_repeat('a', 256),
                'office' => str_repeat('a', 256),
                'other' => str_repeat('a', 256)
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'date_from',
                'legal_entity_id',
                'contractor_id',
                'employee_id',
                'department_id',
                'plan_date',
                'sales_channel_id',
                'warehouse_id',
                'delivery_info.post_code',
                'delivery_info.country',
                'delivery_info.region',
                'delivery_info.city',
                'delivery_info.street',
                'delivery_info.house',
                'delivery_info.office',
                'delivery_info.other'
            ]);
    }

    public function test_can_create_customer_order_with_minimal_required_fields(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('customer_orders', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);
    }

    public function test_can_update_customer_order(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id
        ]);

        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'date_from' => now()->format('Y-m-d'),
            'status_id' => $status->id,
            'held' => true,
            'reserve' => true,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'plan_date' => now()->addDays(5)->format('Y-m-d'),
            'sales_channel_id' => $salesChannel->id,
            'warehouse_id' => $warehouse->id,
            'comment' => 'Updated comment',
            'delivery_info' => [
                'comment' => 'Updated delivery comment',
                'post_code' => '654321',
                'country' => 'Updated Russia',
                'region' => 'Updated Moscow',
                'city' => 'Updated Moscow City',
                'street' => 'Updated Test street',
                'house' => '2',
                'office' => '202',
                'other' => 'Updated additional info'
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$order->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('customer_orders', [
            'id' => $order->id,
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sales_channel_id' => $salesChannel->id,
            'warehouse_id' => $warehouse->id,
            'comment' => 'Updated comment',
            'held' => true,
            'reserve' => true
        ]);
    }

    public function test_cannot_update_customer_order_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'cabinet_id' => $this->otherCabinet->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$otherCabinetOrder->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_non_existent_customer_order(): void
    {
        // Act
        $response = $this->putJson("/api/internal/customer-orders/" . $this->faker->uuid(), [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_customer_order_with_invalid_data(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'cabinet_id' => 'invalid-uuid',
            'number' => str_repeat('a', 256),
            'date_from' => 'invalid-date',
            'status_id' => 'invalid-uuid',
            'legal_entity_id' => 'invalid-uuid',
            'contractor_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'plan_date' => 'invalid-date',
            'sales_channel_id' => 'invalid-uuid',
            'warehouse_id' => 'invalid-uuid',
            'delivery_info' => [
                'post_code' => str_repeat('a', 256),
                'country' => str_repeat('a', 256),
                'region' => str_repeat('a', 256),
                'city' => str_repeat('a', 256),
                'street' => str_repeat('a', 256),
                'house' => str_repeat('a', 256),
                'office' => str_repeat('a', 256),
                'other' => str_repeat('a', 256)
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$order->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'date_from',
                'status_id',
                'legal_entity_id',
                'contractor_id',
                'employee_id',
                'department_id',
                'plan_date',
                'sales_channel_id',
                'warehouse_id',
                'delivery_info.post_code',
                'delivery_info.country',
                'delivery_info.region',
                'delivery_info.city',
                'delivery_info.street',
                'delivery_info.house',
                'delivery_info.office',
                'delivery_info.other'
            ]);
    }

    public function test_cannot_update_customer_order_with_legal_entity_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetLegalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => $otherCabinetLegalEntity->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$order->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_customer_order_with_contractor_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetContractor = Contractor::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => $otherCabinetContractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$order->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_customer_order_with_department_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetDepartment = Department::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'employee_id' => $this->employee->id,
            'department_id' => $otherCabinetDepartment->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$order->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_customer_order_with_sales_channel_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetSalesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'sales_channel_id' => $otherCabinetSalesChannel->id
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$order->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_customer_order_with_warehouse_from_other_cabinet(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetWarehouse = Warehouse::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $updateData = [
            'cabinet_id' => $this->cabinet->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'contractor_id' => Contractor::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'warehouse_id' => $otherCabinetWarehouse->id
        ];

        // Act
        $response = $this->putJson("/api/internal/customer-orders/{$order->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_show_customer_order(): void
    {
        // Arrange
        $legalEntity = LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $salesChannel = SalesChannel::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $status = Status::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'number' => 'TEST-001',
            'date_from' => now(),
            'status_id' => $status->id,
            'held' => true,
            'reserve' => true,
            'legal_entity_id' => $legalEntity->id,
            'contractor_id' => $contractor->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'sales_channel_id' => $salesChannel->id,
            'warehouse_id' => $warehouse->id,
            'comment' => 'Test comment'
        ]);

        // Создаем позиции заказа
        CustomerOrderItem::factory()->count(3)->create([
            'order_id' => $order->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/customer-orders/{$order->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'number',
                'date_from',
                'status_id',
                'held',
                'reserve',
                'legal_entity_id',
                'contractor_id',
                'employee_id',
                'department_id',
                'sales_channel_id',
                'warehouse_id',
                'comment',
                'contractor',
                'status',
                'warehouse',
                'files',
                'delivery_info'
            ]);

        $this->assertEquals($order->id, $response->json('id'));
        $this->assertEquals($this->cabinet->id, $response->json('cabinet_id'));
        $this->assertEquals($legalEntity->id, $response->json('legal_entity_id'));
        $this->assertEquals($contractor->id, $response->json('contractor_id'));
        $this->assertEquals($this->employee->id, $response->json('employee_id'));
        $this->assertEquals($this->department->id, $response->json('department_id'));
        $this->assertEquals($salesChannel->id, $response->json('sales_channel_id'));
        $this->assertEquals($warehouse->id, $response->json('warehouse_id'));
        $this->assertEquals('Test comment', $response->json('comment'));
        $this->assertTrue($response->json('held'));
        $this->assertTrue($response->json('reserve'));
    }

    public function test_cannot_show_customer_order_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/customer-orders/{$otherCabinetOrder->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_customer_order(): void
    {
        // Act
        $response = $this->getJson("/api/internal/customer-orders/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_customer_order(): void
    {
        // Arrange
        $order = CustomerOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем позиции заказа
        CustomerOrderItem::factory()->count(3)->create([
            'order_id' => $order->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/{$order->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertSoftDeleted('customer_orders', [
            'id' => $order->id
        ]);
    }

    public function test_cannot_delete_customer_order_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrder = CustomerOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/{$otherCabinetOrder->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('customer_orders', [
            'id' => $otherCabinetOrder->id,
            'deleted_at' => null
        ]);
    }

    public function test_cannot_delete_non_existent_customer_order(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_bulk_delete_customer_orders(): void
    {
        // Arrange
        $orders = CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем позиции для каждого заказа
        foreach ($orders as $order) {
            CustomerOrderItem::factory()->count(2)->create([
                'order_id' => $order->id
            ]);
        }

        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/bulk-delete", [
            'ids' => $orders->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(204);

        foreach ($orders as $order) {
            $this->assertSoftDeleted('customer_orders', [
                'id' => $order->id
            ]);
        }
    }

    public function test_cannot_bulk_delete_customer_orders_from_other_cabinet(): void
    {
        // Arrange
        $orders = CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/bulk-delete", [
            'ids' => $orders->pluck('id')->toArray(),
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(404);

        foreach ($orders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'deleted_at' => null
            ]);
        }
    }

    public function test_cannot_bulk_delete_with_empty_ids(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/bulk-delete", [
            'ids' => []
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_cannot_bulk_delete_with_invalid_ids(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/bulk-delete", [
            'ids' => ['invalid-uuid']
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids.0']);
    }

    public function test_bulk_delete_with_mixed_cabinet_orders(): void
    {
        // Arrange
        $ownOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $allIds = array_merge(
            $ownOrders->pluck('id')->toArray(),
            $otherCabinetOrders->pluck('id')->toArray()
        );

        // Act
        $response = $this->deleteJson("/api/internal/customer-orders/bulk-delete", [
            'ids' => $allIds,
            'cabinet_id' => $this->cabinet->id
        ]);

        // Assert
        $response->assertStatus(404);

        // Проверяем что ни один заказ не был удален
        foreach ($ownOrders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'deleted_at' => null
            ]);
        }

        foreach ($otherCabinetOrders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'deleted_at' => null
            ]);
        }
    }

    public function test_can_bulk_held_customer_orders(): void
    {
        // Arrange
        $orders = CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $orders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-held', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($orders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'held' => true
            ]);
        }
    }

    public function test_cannot_bulk_held_customer_orders_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => false
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $otherCabinetOrders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-held', $data);

        // Assert
        $response->assertStatus(404);

        foreach ($otherCabinetOrders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'held' => false
            ]);
        }
    }

    public function test_can_bulk_unheld_customer_orders(): void
    {
        // Arrange
        $orders = CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $orders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-unheld', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($orders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'held' => false
            ]);
        }
    }

    public function test_cannot_bulk_unheld_customer_orders_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $otherCabinetOrders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-unheld', $data);

        // Assert
        $response->assertStatus(404);

        foreach ($otherCabinetOrders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'held' => true
            ]);
        }
    }

    public function test_can_bulk_reserve_customer_orders(): void
    {
        // Arrange
        $orders = CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'reserve' => false
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $orders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-reserve', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($orders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'reserve' => true
            ]);
        }
    }

    public function test_cannot_bulk_reserve_customer_orders_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'reserve' => false
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $otherCabinetOrders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-reserve', $data);

        // Assert
        $response->assertStatus(404);

        foreach ($otherCabinetOrders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'reserve' => false
            ]);
        }
    }

    public function test_can_bulk_unreserve_customer_orders(): void
    {
        // Arrange
        $orders = CustomerOrder::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id,
            'reserve' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $orders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-unreserve', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($orders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'reserve' => false
            ]);
        }
    }

    public function test_cannot_bulk_unreserve_customer_orders_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'reserve' => true
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $otherCabinetOrders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->patchJson('/api/internal/customer-orders/bulk-unreserve', $data);

        // Assert
        $response->assertStatus(404);

        foreach ($otherCabinetOrders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id,
                'reserve' => true
            ]);
        }
    }

    public function test_can_bulk_copy_customer_orders(): void
    {
        // Arrange
        $orders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем позиции для каждого заказа
        foreach ($orders as $order) {
            CustomerOrderItem::factory()->count(2)->create([
                'order_id' => $order->id
            ]);
        }

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $orders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/bulk-copy', $data);

        // Assert
        $response->assertStatus(204);

        // Проверяем что исходные заказы остались без изменений
        foreach ($orders as $order) {
            $this->assertDatabaseHas('customer_orders', [
                'id' => $order->id
            ]);
        }

        // Проверяем что созданы новые заказы
        $this->assertEquals(
            count($orders) * 2, // Исходные + скопированные
            CustomerOrder::where('cabinet_id', $this->cabinet->id)->count()
        );

        // Проверяем что для новых заказов скопированы позиции
        $this->assertEquals(
            count($orders) * 2 * 2, // (Исходные + скопированные заказы) * позиции в каждом
            CustomerOrderItem::whereIn('order_id', CustomerOrder::where('cabinet_id', $this->cabinet->id)->pluck('id'))->count()
        );
    }

    public function test_cannot_bulk_copy_customer_orders_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $otherCabinetOrders->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/customer-orders/bulk-copy', $data);

        // Assert
        $response->assertStatus(404);

        // Проверяем что новые заказы не были созданы
        $this->assertEquals(0, CustomerOrder::where('cabinet_id', $this->cabinet->id)->count());
    }

    public function test_bulk_operations_with_mixed_cabinet_orders(): void
    {
        // Arrange
        $ownOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'held' => false,
            'reserve' => false
        ]);

        $otherCabinetOrders = CustomerOrder::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
            'held' => false,
            'reserve' => false
        ]);

        $mixedIds = array_merge(
            $ownOrders->pluck('id')->toArray(),
            $otherCabinetOrders->pluck('id')->toArray()
        );

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $mixedIds
        ];

        // Тестируем все массовые операции
        $operations = [
            'bulk-held',
            'bulk-unheld',
            'bulk-reserve',
            'bulk-unreserve',
            'bulk-copy',
            'bulk-delete'
        ];

        foreach ($operations as $operation) {
            // Act
            $method = match ($operation) {
                'bulk-copy' => 'postJson',
                'bulk-delete' => 'deleteJson',
                default => 'patchJson'
            };

            $response = $this->$method("/api/internal/customer-orders/{$operation}", $data);

            // Assert
            $response->assertStatus(404);

            // Проверяем что состояние заказов не изменилось
            foreach ($ownOrders as $order) {
                $this->assertDatabaseHas('customer_orders', [
                    'id' => $order->id,
                    'held' => false,
                    'reserve' => false,
                    'deleted_at' => null
                ]);
            }

            foreach ($otherCabinetOrders as $order) {
                $this->assertDatabaseHas('customer_orders', [
                    'id' => $order->id,
                    'held' => false,
                    'reserve' => false,
                    'deleted_at' => null
                ]);
            }
        }
    }
}
