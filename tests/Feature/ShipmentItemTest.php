<?php

namespace Tests\Feature;

use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateShipmentItemJob;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\Shipment;
use App\Models\ShipmentItem;
use App\Models\User;
use App\Models\VatRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ShipmentItemTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->anotherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_shipment_items_list(): void
    {
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем 3 item для нашей отгрузки
        ShipmentItem::factory()->count(3)->create([
            'shipment_id' => $shipment->id
        ]);

        // Создаем item для другой отгрузки, чтобы убедиться что не получим его
        $otherShipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        ShipmentItem::factory()->create([
            'shipment_id' => $otherShipment->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/shipments/items?' . http_build_query([
            'shipment_id' => $shipment->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'shipment_id',
                        'product_id',
                        'quantity',
                        'price',
                        'vat_rate_id',
                        'total_price',
                        'total_cost',
                        'cost',
                        'profit',
                        'recidual'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашей отгрузки
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($shipment->id, $item['shipment_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_shipment_items(): void
    {
        // Arrange
        $otherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Создаем items для отгрузки другого кабинета
        ShipmentItem::factory()->count(2)->create([
            'shipment_id' => $otherCabinetShipment->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/shipments/items?' . http_build_query([
            'shipment_id' => $otherCabinetShipment->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/shipments/items?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'shipment_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'shipment_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ShipmentItem::factory()->count(2)->create([
            'shipment_id' => $shipment->id
        ]);

        $response = $this->getJson('/api/internal/shipments/items?' . http_build_query([
            'shipment_id' => $shipment->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем количество записей
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_without_required_shipment_id(): void
    {
        $response = $this->getJson('/api/internal/shipments/items');

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['shipment_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/shipments/items?' . http_build_query([
            'shipment_id' => $shipment->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем items с разными ценами
        $item1 = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'price' => 100
        ]);
        $item2 = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'price' => 200
        ]);
        $item3 = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'price' => 150
        ]);

        // Act - получаем отсортированный по цене список
        $response = $this->getJson('/api/internal/shipments/items?' . http_build_query([
            'shipment_id' => $shipment->id,
            'sortField' => 'price',
            'sortDirection' => 'asc',
            'fields' => ['id', 'price']
        ]));

        // Assert
        $response->assertOk();

        $prices = collect($response->json('data'))->pluck('price')->values();
        $expectedPrices = collect([$item1->price, $item3->price, $item2->price]);

        $this->assertEquals($expectedPrices, $prices);
    }

    public function test_can_create_shipment_item(): void
    {
        Queue::fake();
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'shipment_id' => $shipment->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => $this->faker->numberBetween(1, 99999999),
            'vat_rate_id' => $vatRate->id,
            'discount' => $this->faker->numberBetween(0, 100),
        ];

        // Act
        $response = $this->postJson('/api/internal/shipments/items', $data);

        // Assert
        $response->assertCreated()
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('shipment_items', [
            'id' => $response->json('id'),
            'shipment_id' => $data['shipment_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
            'price' => $data['price'],
            'vat_rate_id' => $data['vat_rate_id'],
        ]);

        // Проверяем что total_price в shipment обновился
        $this->assertDatabaseHas('shipments', [
            'id' => $shipment->id,
            'total_price' => $data['price']
        ]);

        // Проверяем что FIFO job был запущен
        Queue::assertPushed(HandleFifoJob::class);
    }

    public function test_cannot_create_duplicate_shipment_item(): void
    {
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем первую позицию
        ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'product_id' => $product->id
        ]);

        // Пытаемся создать дубликат
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'shipment_id' => $shipment->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => $this->faker->numberBetween(1, 99999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/shipments/items', $data);

        // Assert
        $response->assertStatus(500)
            ->assertJson(['error' => 'An error occurred while processing your request. Item already exists']);
    }

    public function test_cannot_create_shipment_item_for_other_cabinet(): void
    {
        // Arrange
        $otherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'shipment_id' => $otherCabinetShipment->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => $this->faker->numberBetween(1, 99999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/shipments/items', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_shipment_item_with_invalid_data(): void
    {
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'cabinet_id' => $this->cabinet->id,
            'shipment_id' => $shipment->id,
            'product_id' => 'invalid-uuid',
            'quantity' => -1, // отрицательное количество
            'price' => 'not-a-number', // неверный формат цены
            'vat_rate_id' => 'invalid-uuid',
            'discount' => 'not-a-number',
        ];

        // Act
        $response = $this->postJson('/api/internal/shipments/items', $invalidData);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'product_id',
                'quantity',
                'price',
                'vat_rate_id',
                'discount'
            ]);
    }

    public function test_cannot_create_shipment_item_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/shipments/items', []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'shipment_id',
                'product_id',
                'quantity'
            ]);
    }

    public function test_can_create_shipment_item_with_minimum_required_fields(): void
    {
        // Arrange
        Queue::fake();
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'shipment_id' => $shipment->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
        ];

        // Act
        $response = $this->postJson('/api/internal/shipments/items', $data);

        // Assert
        $response->assertCreated()
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('shipment_items', [
            'id' => $response->json('id'),
            'shipment_id' => $data['shipment_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
        ]);

        Queue::assertPushed(HandleFifoJob::class);
    }

    public function test_can_update_shipment_item(): void
    {
        Queue::fake();

        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'total_price' => 1000
        ]);

        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'quantity' => 1,
            'price' => 1000,
            'total_price' => 1000
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => 2000,
            'vat_rate_id' => $vatRate->id,
            'discount' => $this->faker->numberBetween(0, 100),
        ];

        // Act
        $response = $this->putJson("/api/internal/shipments/items/{$shipmentItem->id}", $updateData);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('shipment_items', [
            'id' => $shipmentItem->id,
            'quantity' => $updateData['quantity'],
            'price' => $updateData['price'],
            'vat_rate_id' => $updateData['vat_rate_id'],
            'discount' => $updateData['discount'],
        ]);

        // Проверяем что total_price в shipment обновился
        $this->assertDatabaseHas('shipments', [
            'id' => $shipment->id,
            'total_price' => 2000 // Новая цена
        ]);

        Queue::assertPushed(RecalculationAfterUpdateShipmentItemJob::class);
    }

    public function test_cannot_update_shipment_item_from_another_cabinet(): void
    {
        // Arrange
        $otherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $otherCabinetShipment->id
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => $this->faker->numberBetween(1, 99999999),
        ];

        // Act
        $response = $this->putJson("/api/internal/shipments/items/{$shipmentItem->id}", $updateData);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('shipment_items', [
            'id' => $shipmentItem->id,
            'quantity' => $shipmentItem->quantity,
            'price' => $shipmentItem->price,
        ]);
    }

    public function test_cannot_update_non_existent_shipment_item(): void
    {
        // Act
        $response = $this->putJson("/api/internal/shipments/items/" . $this->faker->uuid(), [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => $this->faker->numberBetween(1, 99999999),
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_shipment_item_with_invalid_data(): void
    {
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id
        ]);

        $invalidData = [
            'quantity' => -1, // отрицательное количество
            'price' => 'not-a-number', // неверный формат цены
            'vat_rate_id' => 'invalid-uuid',
            'discount' => 'not-a-number',
        ];

        // Act
        $response = $this->putJson("/api/internal/shipments/items/{$shipmentItem->id}", $invalidData);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'quantity',
                'price',
                'vat_rate_id',
                'discount'
            ]);
    }

    public function test_can_update_shipment_item_with_minimum_required_fields(): void
    {
        Queue::fake();

        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'total_price' => 1000
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'quantity' => 1,
            'price' => 1000,
            'total_price' => 1000,
            'vat_rate_id' => VatRate::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'discount' => 10
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
        ];

        // Act
        $response = $this->putJson("/api/internal/shipments/items/{$shipmentItem->id}", $updateData);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('shipment_items', [
            'id' => $shipmentItem->id,
            'quantity' => $updateData['quantity'],
            'vat_rate_id' => null,
            'discount' => 0,
        ]);

        Queue::assertPushed(RecalculationAfterUpdateShipmentItemJob::class);
    }

    public function test_cannot_update_shipment_item_without_required_fields(): void
    {
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/shipments/items/{$shipmentItem->id}", []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['quantity']); // Только quantity обязательное поле при обновлении
    }

    public function test_can_show_shipment_item(): void
    {
        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Product',
            'weight' => 1.5,
            'volume' => 2.0
        ]);

        $vatRate = VatRate::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'rate' => 20
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'product_id' => $product->id,
            'vat_rate_id' => $vatRate->id,
            'quantity' => 5,
            'price' => 1000,
            'total_price' => 5000
        ]);

        // Act
        $response = $this->getJson("/api/internal/shipments/items/{$shipmentItem->id}");

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'shipment_id',
                'product_id',
                'quantity',
                'price',
                'vat_rate_id',
                'total_price',
                'vat_rate',
                'product' => [
                    'title',
                    'weight',
                    'volume'
                ],
            ])
            ->assertJson([
                'id' => $shipmentItem->id,
                'shipment_id' => $shipment->id,
                'product_id' => $product->id,
                'vat_rate_id' => $vatRate->id,
                'quantity' => 5,
                'price' => 1000,
                'total_price' => 5000,
                'vat_rate' => 20,
                'product' => [
                    'title' => 'Test Product',
                    'weight' => '1.5',
                    'volume' => '2.0'
                ]
            ]);
    }

    public function test_cannot_show_shipment_item_from_another_cabinet(): void
    {
        // Arrange
        $otherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $otherCabinetShipment->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/shipments/items/{$shipmentItem->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_shipment_item(): void
    {
        // Act
        $response = $this->getJson("/api/internal/shipments/items/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_shipment_item(): void
    {
        Queue::fake();

        // Arrange
        $shipment = Shipment::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'total_price' => 2000
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $shipment->id,
            'price' => 1000,
            'total_price' => 1000
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/shipments/items/{$shipmentItem->id}");

        // Assert
        $response->assertNoContent();

        // Проверяем что total_price в shipment уменьшился
        $this->assertDatabaseHas('shipments', [
            'id' => $shipment->id,
            'total_price' => 1000 // 2000 - 1000
        ]);

        // Проверяем что FIFO job был запущен с флагом удаления
        Queue::assertPushed(function (HandleFifoJob $job) use ($shipmentItem) {
            return $job->delete === true;
        });
    }

    public function test_cannot_delete_shipment_item_from_another_cabinet(): void
    {
        // Arrange
        $otherCabinetShipment = Shipment::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $shipmentItem = ShipmentItem::factory()->create([
            'shipment_id' => $otherCabinetShipment->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/shipments/items/{$shipmentItem->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('shipment_items', [
            'id' => $shipmentItem->id
        ]);
    }

    public function test_cannot_delete_non_existent_shipment_item(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/shipments/items/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }
}
