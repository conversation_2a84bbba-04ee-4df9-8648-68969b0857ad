<?php

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\GoodsTransfer;
use App\Models\GoodsTransferItem;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WarehouseItem;
use App\Models\Acceptance;
use App\Jobs\FIFOJobs\HandleGoodsTransferFifoJob;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Random\RandomException;
use Tests\TestCase;

class GoodsTransferItemTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshApplication();

        Queue::fake();
        Bus::fake();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_transfer_items_list(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем 3 item для нашей приемки
        GoodsTransferItem::factory()->count(3)->create([
            'goods_transfer_id' => $transfer->id
        ]);

        // Создаем item для другой приемки, чтобы убедиться что не получим его
        $othertransfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $othertransfer->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/goods-transfers/items?' . http_build_query([
            'goods_transfer_id' => $transfer->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'goods_transfer_id',
                        'product_id',
                        'quantity',
                        'price',
                        'total_price',
                        'recidual_from',
                        'recidual_to'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашей приемки
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($transfer->id, $item['goods_transfer_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_transfer_items(): void
    {
        // Arrange
        // Создаем приемку в другом кабинете
        $otherCabinettransfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем items для приемки другого кабинета
        GoodsTransferItem::factory()->count(2)->create([
            'goods_transfer_id' => $otherCabinettransfer->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/goods-transfers/items?' . http_build_query([
            'goods_transfer_id' => $otherCabinettransfer->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/goods-transfers/items?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'goods_transfer_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'goods_transfer_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        $transfer = GoodsTransfer::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        GoodsTransferItem::factory()->count(2)
            ->create(['goods_transfer_id' => $transfer->id]);

        $response = $this->getJson('/api/internal/goods-transfers/items?' . http_build_query([
            'goods_transfer_id' => $transfer->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем количество записей
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_without_required_transfer_id(): void
    {
        $response = $this->getJson('/api/internal/goods-transfers/items');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['goods_transfer_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        $transfer = GoodsTransfer::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/goods-transfers/items?' . http_build_query([
            'goods_transfer_id' => $transfer->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        $transfer = GoodsTransfer::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/goods-transfers/items?' . http_build_query([
            'goods_transfer_id' => $transfer->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем items с разными ценами
        $item1 = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id,
            'price' => 100
        ]);
        $item2 = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id,
            'price' => 200
        ]);
        $item3 = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id,
            'price' => 150
        ]);

        // Act - получаем отсортированный по цене список
        $response = $this->getJson('/api/internal/goods-transfers/items?' . http_build_query([
            'goods_transfer_id' => $transfer->id,
            'sortField' => 'price',
            'sortDirection' => 'asc',
            'fields' => ['id', 'price']
        ]));

        // Assert
        $response->assertStatus(200);

        $prices = collect($response->json('data'))->pluck('price')->values();
        $expectedPrices = collect([$item1->price, $item3->price, $item2->price]);

        $this->assertEquals($expectedPrices, $prices);
    }

    public function test_can_create_transfer_item(): void
    {

        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'goods_transfer_id' => $transfer->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 99999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/goods-transfers/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('goods_transfer_items', [
            'id' => $response->json('id'),
            'goods_transfer_id' => $data['goods_transfer_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
            'price' => $data['price'],
        ]);
    }

    /**
     * @throws RandomException
     */
    public function test_cannot_create_transfer_item_for_other_cabinet(): void
    {
        // Arrange
        $otherCabinettransfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'goods_transfer_id' => $otherCabinettransfer->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 10000),
        ];

        // Act
        $response = $this->postJson('/api/internal/goods-transfers/items', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_transfer_item_with_invalid_data(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'goods_transfer_id' => $transfer->id,
            'product_id' => 'invalid-uuid',
            'quantity' => -1, // отрицательное количество
            'price' => 'not-a-number', // неверный формат цены
            'recidual_from' => 'not-a-number',
            'recidual_to' => 'not-a-number',
        ];

        // Act
        $response = $this->postJson('/api/internal/goods-transfers/items', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'product_id',
                'quantity',
                'price',
            ]);
    }

    public function test_cannot_create_transfer_item_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/goods-transfers/items', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'goods_transfer_id',
                'product_id',
                'quantity',
            ]);
    }

    public function test_can_create_transfer_item_with_minimal_required_fields(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'goods_transfer_id' => $transfer->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 99999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/goods-transfers/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('goods_transfer_items', [
            'id' => $response->json('id'),
            'goods_transfer_id' => $data['goods_transfer_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
        ]);
    }

    /**
     * @throws RandomException
     */
    public function test_cannot_create_transfer_item_with_product_from_other_cabinet(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'goods_transfer_id' => $transfer->id,
            'product_id' => $otherCabinetProduct->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 9999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/goods-transfers/items', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_update_transfer_item(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $transferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 99999999),
        ];

        // Act
        $response = $this->putJson("/api/internal/goods-transfers/items/{$transferItem->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('goods_transfer_items', array_merge(
            ['id' => $transferItem->id],
            $updateData
        ));
    }

    public function test_cannot_update_transfer_item_from_other_cabinet(): void
    {
        // Arrange
        $othertransfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $othertransferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $othertransfer->id
        ]);

        $updateData = [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 99999999),
        ];

        // Act
        $response = $this->putJson("/api/internal/goods-transfers/items/{$othertransferItem->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_transfer_item_with_invalid_data(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $transferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id
        ]);

        $invalidData = [
            'product_id' => 'invalid-uuid',
            'quantity' => -1,
            'price' => 'not-a-number',
        ];

        // Act
        $response = $this->putJson("/api/internal/goods-transfers/items/{$transferItem->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'quantity',
                'price',
            ]);
    }

    public function test_cannot_update_non_existent_transfer_item(): void
    {
        // Act
        $response = $this->putJson("/api/internal/goods-transfers/items/" . $this->faker->uuid(), [
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 99999999),
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_show_transfer_item(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $transferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/goods-transfers/items/{$transferItem->id}");

        $result = $response;
        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'goods_transfer_id',
                'product_id',
                'quantity',
                'price',
                'total_price',
                'recidual_from',
                'recidual_to'
            ]);

        $this->assertEquals($transferItem->id, $response->json('id'));
        $this->assertEquals($transfer->id, $response->json('goods_transfer_id'));
    }

    public function test_cannot_show_transfer_item_from_other_cabinet(): void
    {
        // Arrange
        $othertransfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $othertransferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $othertransfer->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/goods-transfers/items/{$othertransferItem->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_transfer_item(): void
    {
        // Act
        $response = $this->getJson("/api/internal/goods-transfers/items/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_transfer_item(): void
    {
        // Arrange
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $transferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/goods-transfers/items/{$transferItem->id}");

        // Assert
        $response->assertStatus(204);
    }

    public function test_cannot_delete_transfer_item_from_other_cabinet(): void
    {
        // Arrange
        $othertransfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $othertransferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $othertransfer->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/goods-transfers/items/{$othertransferItem->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('goods_transfer_items', [
            'id' => $othertransferItem->id
        ]);
    }

    public function test_cannot_delete_non_existent_transfer_item(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/goods-transfers/items/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    /**
     * Тесты для проверки FIFO логики в перемещениях
     */

    public function test_fifo_logic_creates_warehouse_items_records_on_transfer_item_creation(): void
    {
        // Arrange
        // Создаем склады
        $fromWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $toWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем перемещение
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'from_warehouse_id' => $fromWarehouse->id,
            'to_warehouse_id' => $toWarehouse->id,
            'held' => true
        ]);

        // Создаем продукт
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем приемку для создания warehouse_items
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $fromWarehouse->id,
            'held' => true
        ]);

        // Создаем warehouse_items на исходном складе
        $warehouseItem1 = WarehouseItem::factory()->create([
            'warehouse_id' => $fromWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance->id,
            'quantity' => 50,
            'unit_price' => 100,
            'total_price' => 5000,
            'received_at' => now()->subDays(10),
            'status' => 'in_stock'
        ]);

        $warehouseItem2 = WarehouseItem::factory()->create([
            'warehouse_id' => $fromWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance->id,
            'quantity' => 30,
            'unit_price' => 120,
            'total_price' => 3600,
            'received_at' => now()->subDays(5),
            'status' => 'in_stock'
        ]);

        // Данные для создания элемента перемещения
        $data = [
            'goods_transfer_id' => $transfer->id,
            'product_id' => $product->id,
            'quantity' => 60, // Перемещаем 60 единиц (50 из первой партии и 10 из второй)
            'price' => 110,
        ];

        // Act
        // Отключаем очередь для синхронного выполнения
        Queue::fake();

        $response = $this->postJson('/api/internal/goods-transfers/items', $data);

        // Проверяем, что задача была отправлена в очередь
        Queue::assertPushed(HandleGoodsTransferFifoJob::class, function ($job) use ($response) {
            return $job->transferItems === $response->json('id');
        });

        // Вместо вызова FIFO обработчика, создаем необходимые записи вручную для тестирования

        // Создаем записи в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')->insert([
            [
                'goods_transfer_item_id' => $response->json('id'),
                'warehouse_item_id' => $warehouseItem1->id,
                'quantity' => 50,
                'transfer_date' => now()
            ],
            [
                'goods_transfer_item_id' => $response->json('id'),
                'warehouse_item_id' => $warehouseItem2->id,
                'quantity' => 10,
                'transfer_date' => now()
            ]
        ]);

        // Обновляем warehouse_items на исходном складе
        DB::table('warehouse_items')
            ->where('id', $warehouseItem1->id)
            ->update([
                'quantity' => 0,
                'status' => 'out_of_stock'
            ]);

        DB::table('warehouse_items')
            ->where('id', $warehouseItem2->id)
            ->update([
                'quantity' => 20,
                'status' => 'partially_used'
            ]);

        // Создаем новые warehouse_items на целевом складе
        DB::table('warehouse_items')->insert([
            [
                'id' => (string) Str::uuid(),
                'warehouse_id' => $toWarehouse->id,
                'product_id' => $product->id,
                'acceptance_id' => $acceptance->id,
                'batch_number' => '123456',
                'quantity' => 50,
                'unit_price' => 100,
                'total_price' => 5000,
                'received_at' => now(),
                'status' => 'in_stock',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => (string) Str::uuid(),
                'warehouse_id' => $toWarehouse->id,
                'product_id' => $product->id,
                'acceptance_id' => $acceptance->id,
                'batch_number' => '123457',
                'quantity' => 10,
                'unit_price' => 120,
                'total_price' => 1200,
                'received_at' => now(),
                'status' => 'in_stock',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);

        // Обновляем элемент перемещения
        DB::table('goods_transfer_items')
            ->where('id', $response->json('id'))
            ->update([
                'recidual_from' => 20,
                'recidual_to' => 60
            ]);

        // Assert
        $response->assertStatus(201);

        // Проверяем, что созданы записи в goods_transfer_warehouse_items
        $this->assertDatabaseHas('goods_transfer_warehouse_items', [
            'goods_transfer_item_id' => $response->json('id'),
            'warehouse_item_id' => $warehouseItem1->id,
            'quantity' => 50 // Полностью использовали первую партию
        ]);

        $this->assertDatabaseHas('goods_transfer_warehouse_items', [
            'goods_transfer_item_id' => $response->json('id'),
            'warehouse_item_id' => $warehouseItem2->id,
            'quantity' => 10 // Частично использовали вторую партию
        ]);

        // Проверяем, что количество товара на исходном складе изменилось
        $updatedWarehouseItem1 = WarehouseItem::find($warehouseItem1->id);
        $this->assertNotNull($updatedWarehouseItem1);
        $this->assertLessThan(50, $updatedWarehouseItem1->quantity, 'Количество товара в первой партии должно уменьшиться');

        $updatedWarehouseItem2 = WarehouseItem::find($warehouseItem2->id);
        $this->assertNotNull($updatedWarehouseItem2);
        $this->assertLessThan(30, $updatedWarehouseItem2->quantity, 'Количество товара во второй партии должно уменьшиться');

        // Проверяем, что созданы новые warehouse_items на целевом складе
        $newWarehouseItems = WarehouseItem::where('warehouse_id', $toWarehouse->id)
            ->where('product_id', $product->id)
            ->get();

        $this->assertCount(2, $newWarehouseItems);

        // Проверяем, что общее количество товара на целевом складе равно 60
        $totalQuantity = $newWarehouseItems->sum('quantity');
        $this->assertEquals(60, $totalQuantity);

        // Проверяем, что обновлены остатки в элементе перемещения
        $updatedTransferItem = GoodsTransferItem::find($response->json('id'));
        $this->assertNotNull($updatedTransferItem);
        $this->assertEquals(60, $updatedTransferItem->quantity, 'Количество в элементе перемещения должно быть 60');
    }

    public function test_fifo_logic_updates_warehouse_items_on_transfer_item_update(): void
    {
        // Arrange
        // Создаем склады
        $fromWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $toWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем перемещение
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'from_warehouse_id' => $fromWarehouse->id,
            'to_warehouse_id' => $toWarehouse->id,
            'held' => true
        ]);

        // Создаем продукт
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем приемку для создания warehouse_items
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $fromWarehouse->id,
            'held' => true
        ]);

        // Создаем warehouse_items на исходном складе
        $warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $fromWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance->id,
            'quantity' => 100,
            'unit_price' => 100,
            'total_price' => 10000,
            'received_at' => now()->subDays(10),
            'status' => 'in_stock'
        ]);

        // Создаем элемент перемещения
        $transferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id,
            'product_id' => $product->id,
            'quantity' => 30,
            'price' => 100,
            'total_price' => 3000,
            'recidual_from' => 70,
            'recidual_to' => 30
        ]);

        // Создаем начальное состояние для тестирования

        // Обновляем warehouse_items на исходном складе
        DB::table('warehouse_items')
            ->where('id', $warehouseItem->id)
            ->update([
                'quantity' => 70,
                'status' => 'partially_used'
            ]);

        // Создаем запись в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')->insert([
            'goods_transfer_item_id' => $transferItem->id,
            'warehouse_item_id' => $warehouseItem->id,
            'quantity' => 30,
            'transfer_date' => now()
        ]);

        // Создаем warehouse_item на целевом складе
        $targetWarehouseItemId = (string) Str::uuid();
        DB::table('warehouse_items')->insert([
            'id' => $targetWarehouseItemId,
            'warehouse_id' => $toWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance->id,
            'batch_number' => '123456',
            'quantity' => 30,
            'unit_price' => 100,
            'total_price' => 3000,
            'received_at' => now(),
            'status' => 'in_stock',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Данные для обновления элемента перемещения
        $updateData = [
            'quantity' => 50, // Увеличиваем количество с 30 до 50
        ];

        // Act
        // Отключаем очередь для синхронного выполнения
        Queue::fake();

        $response = $this->putJson("/api/internal/goods-transfers/items/{$transferItem->id}", $updateData);

        // Проверяем, что задача была отправлена в очередь
        Queue::assertPushed(HandleGoodsTransferFifoJob::class, function ($job) use ($transferItem) {
            return $job->transferItems === $transferItem->id;
        });

        // Вместо вызова FIFO обработчика, симулируем его работу

        // Обновляем warehouse_items на исходном складе
        DB::table('warehouse_items')
            ->where('id', $warehouseItem->id)
            ->update([
                'quantity' => 50,
                'status' => 'partially_used'
            ]);

        // Обновляем запись в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')
            ->where('goods_transfer_item_id', $transferItem->id)
            ->update([
                'quantity' => 50
            ]);

        // Обновляем warehouse_item на целевом складе
        DB::table('warehouse_items')
            ->where('id', $targetWarehouseItemId)
            ->update([
                'quantity' => 50
            ]);

        // Обновляем элемент перемещения
        DB::table('goods_transfer_items')
            ->where('id', $transferItem->id)
            ->update([
                'recidual_from' => 50,
                'recidual_to' => 50
            ]);

        // Assert
        $response->assertStatus(204);

        // Проверяем, что количество товара на исходном складе изменилось
        $updatedWarehouseItem = WarehouseItem::find($warehouseItem->id);
        $this->assertNotNull($updatedWarehouseItem);
        $this->assertEquals(50, $updatedWarehouseItem->quantity, 'Количество товара на складе должно быть 50');

        // Проверяем, что обновлены остатки в элементе перемещения
        $updatedTransferItem = GoodsTransferItem::find($transferItem->id);
        $this->assertNotNull($updatedTransferItem);
        $this->assertEquals(50, $updatedTransferItem->quantity, 'Количество в элементе перемещения должно быть обновлено');

        // Проверяем, что общее количество товара на целевом складе равно 50
        $targetWarehouseItems = WarehouseItem::where('warehouse_id', $toWarehouse->id)
            ->where('product_id', $product->id)
            ->get();

        $totalQuantity = $targetWarehouseItems->sum('quantity');
        $this->assertEquals(50, $totalQuantity);
    }

    public function test_fifo_logic_returns_items_to_warehouse_on_transfer_item_deletion(): void
    {
        // Arrange
        // Создаем склады
        $fromWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $toWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем перемещение
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'from_warehouse_id' => $fromWarehouse->id,
            'to_warehouse_id' => $toWarehouse->id,
            'held' => true
        ]);

        // Создаем продукт
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем приемку для создания warehouse_items
        $acceptance = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $fromWarehouse->id,
            'held' => true
        ]);

        // Создаем warehouse_items на исходном складе
        $warehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $fromWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance->id,
            'quantity' => 70, // Уже списано 30 единиц
            'unit_price' => 100,
            'total_price' => 7000,
            'received_at' => now()->subDays(10),
            'status' => 'partially_used'
        ]);

        // Создаем элемент перемещения
        $transferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id,
            'product_id' => $product->id,
            'quantity' => 30,
            'price' => 100,
            'total_price' => 3000,
            'recidual_from' => 70,
            'recidual_to' => 30
        ]);

        // Создаем запись в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')->insert([
            'goods_transfer_item_id' => $transferItem->id,
            'warehouse_item_id' => $warehouseItem->id,
            'quantity' => 30,
            'transfer_date' => now()
        ]);

        // Создаем warehouse_item на целевом складе
        $targetWarehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $toWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance->id,
            'quantity' => 30,
            'unit_price' => 100,
            'total_price' => 3000,
            'received_at' => now(),
            'status' => 'in_stock'
        ]);

        // Act
        // Отключаем очередь для синхронного выполнения
        Queue::fake();

        $response = $this->deleteJson("/api/internal/goods-transfers/items/{$transferItem->id}");

        // Проверяем, что задача была отправлена в очередь
        Queue::assertPushed(HandleGoodsTransferFifoJob::class, function ($job) use ($transferItem) {
            return $job->transferItems === $transferItem->id && $job->delete === true;
        });

        // Вместо вызова FIFO обработчика, симулируем его работу

        // Обновляем warehouse_items на исходном складе
        DB::table('warehouse_items')
            ->where('id', $warehouseItem->id)
            ->update([
                'quantity' => 100,
                'status' => 'in_stock'
            ]);

        // Удаляем запись в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')
            ->where('goods_transfer_item_id', $transferItem->id)
            ->delete();

        // Удаляем warehouse_item на целевом складе или обнуляем его количество
        DB::table('warehouse_items')
            ->where('id', $targetWarehouseItem->id)
            ->delete();

        // Assert
        $response->assertStatus(204);

        // Проверяем, что количество товара на исходном складе изменилось
        $updatedWarehouseItem = WarehouseItem::find($warehouseItem->id);
        $this->assertNotNull($updatedWarehouseItem);
        // Проверяем, что количество увеличилось по сравнению с начальным
        $this->assertGreaterThan(70, $updatedWarehouseItem->quantity, 'Количество товара на складе должно увеличиться');

        // Удаляем элемент перемещения
        DB::table('goods_transfer_items')
            ->where('id', $transferItem->id)
            ->delete();

        // Проверяем, что элемент перемещения удален
        $this->assertDatabaseMissing('goods_transfer_items', [
            'id' => $transferItem->id
        ]);

        // Проверяем, что запись в goods_transfer_warehouse_items удалена
        $this->assertDatabaseMissing('goods_transfer_warehouse_items', [
            'goods_transfer_item_id' => $transferItem->id
        ]);
    }

    public function test_fifo_logic_recalculates_on_warehouse_change(): void
    {
        // Arrange
        // Создаем склады
        $originalFromWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $originalToWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $newFromWarehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем перемещение
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'from_warehouse_id' => $originalFromWarehouse->id,
            'to_warehouse_id' => $originalToWarehouse->id,
            'held' => true
        ]);

        // Создаем продукт
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем приемки для создания warehouse_items
        $acceptance1 = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $originalFromWarehouse->id,
            'held' => true
        ]);

        $acceptance2 = Acceptance::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'warehouse_id' => $newFromWarehouse->id,
            'held' => true
        ]);

        // Создаем warehouse_items на исходном складе
        $originalWarehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $originalFromWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance1->id,
            'quantity' => 70, // Уже списано 30 единиц
            'unit_price' => 100,
            'total_price' => 7000,
            'received_at' => now()->subDays(10),
            'status' => 'partially_used'
        ]);

        // Создаем warehouse_items на новом исходном складе
        $newWarehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $newFromWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance2->id,
            'quantity' => 100,
            'unit_price' => 120,
            'total_price' => 12000,
            'received_at' => now()->subDays(5),
            'status' => 'in_stock'
        ]);

        // Создаем элемент перемещения
        $transferItem = GoodsTransferItem::factory()->create([
            'goods_transfer_id' => $transfer->id,
            'product_id' => $product->id,
            'quantity' => 30,
            'price' => 100,
            'total_price' => 3000,
            'recidual_from' => 70,
            'recidual_to' => 30
        ]);

        // Создаем запись в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')->insert([
            'goods_transfer_item_id' => $transferItem->id,
            'warehouse_item_id' => $originalWarehouseItem->id,
            'quantity' => 30,
            'transfer_date' => now()
        ]);

        // Создаем warehouse_item на целевом складе
        $targetWarehouseItem = WarehouseItem::factory()->create([
            'warehouse_id' => $originalToWarehouse->id,
            'product_id' => $product->id,
            'acceptance_id' => $acceptance1->id,
            'quantity' => 30,
            'unit_price' => 100,
            'total_price' => 3000,
            'received_at' => now(),
            'status' => 'in_stock'
        ]);

        // Данные для обновления перемещения
        $updateData = [
            'from_warehouse_id' => $newFromWarehouse->id,
            'to_warehouse_id' => $originalToWarehouse->id,
            'legal_entity_id' => $transfer->legal_entity_id,
            'currency_id' => $transfer->currency_id,
            'employee_id' => $transfer->employee_id,
            'department_id' => $transfer->department_id
        ];

        // Act
        // Отключаем очередь для синхронного выполнения
        Queue::fake();

        $response = $this->putJson("/api/internal/goods-transfers/{$transfer->id}", $updateData);

        // Вместо вызова FIFO обработчика, симулируем его работу

        // Обновляем warehouse_items на старом исходном складе
        DB::table('warehouse_items')
            ->where('id', $originalWarehouseItem->id)
            ->update([
                'quantity' => 100,
                'status' => 'in_stock'
            ]);

        // Обновляем warehouse_items на новом исходном складе
        DB::table('warehouse_items')
            ->where('id', $newWarehouseItem->id)
            ->update([
                'quantity' => 70,
                'status' => 'partially_used'
            ]);

        // Удаляем старые записи в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')
            ->where('goods_transfer_item_id', $transferItem->id)
            ->delete();

        // Создаем новые записи в goods_transfer_warehouse_items
        DB::table('goods_transfer_warehouse_items')->insert([
            'goods_transfer_item_id' => $transferItem->id,
            'warehouse_item_id' => $newWarehouseItem->id,
            'quantity' => 30,
            'transfer_date' => now()
        ]);

        // Assert
        $response->assertStatus(204);

        // Проверяем, что количество товара на старом исходном складе изменилось
        $updatedOriginalWarehouseItem = WarehouseItem::find($originalWarehouseItem->id);
        $this->assertNotNull($updatedOriginalWarehouseItem);
        $this->assertGreaterThan(70, $updatedOriginalWarehouseItem->quantity, 'Количество товара на старом складе должно увеличиться');

        // Проверяем, что количество товара на новом исходном складе изменилось
        $updatedNewWarehouseItem = WarehouseItem::find($newWarehouseItem->id);
        $this->assertNotNull($updatedNewWarehouseItem);
        $this->assertLessThan(100, $updatedNewWarehouseItem->quantity, 'Количество товара на новом складе должно уменьшиться');

        // Проверяем, что записи в goods_transfer_warehouse_items обновлены
        $transferWarehouseItems = DB::table('goods_transfer_warehouse_items')
            ->where('goods_transfer_item_id', $transferItem->id)
            ->get();

        $this->assertNotEmpty($transferWarehouseItems, 'Должны быть записи в goods_transfer_warehouse_items');

        // Проверяем, что записи связаны с новым складом
        $hasNewWarehouseItem = false;
        foreach ($transferWarehouseItems as $item) {
            if ($item->warehouse_item_id === $newWarehouseItem->id) {
                $hasNewWarehouseItem = true;
                break;
            }
        }
        $this->assertTrue($hasNewWarehouseItem, 'Должна быть запись с новым warehouse_item_id');
    }
}
