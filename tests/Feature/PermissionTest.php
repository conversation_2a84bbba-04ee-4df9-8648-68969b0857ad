<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PermissionTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');
    }

    public function test_can_get_permissions_list(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->getJson('/api/internal/permissions');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'category_id',
                    'category_name',
                    'groups' => [
                        '*' => [
                            'group_id',
                            'group_name',
                            'permissions' => [
                                '*' => [
                                    'id',
                                    'require_scope',
                                    'operation'
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
    }
}
