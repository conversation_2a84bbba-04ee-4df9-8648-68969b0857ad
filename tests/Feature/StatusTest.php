<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Status;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Shipment;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\StatusType;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StatusTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->anotherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_statuses_list(): void
    {
        // Arrange
        // Создаем статусы для текущего кабинета
        Status::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем глобальные статусы (cabinet_id = null)
        Status::factory()->count(2)->create([
            'cabinet_id' => null
        ]);

        // Создаем статусы для другого кабинета
        Status::factory()->count(2)->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/statuses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'name',
                        'color',
                        'type_id',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили 5 записей (3 из кабинета + 2 глобальных)
        $response->assertJsonCount(5, 'data');

        // Проверяем что все полученные статусы принадлежат текущему кабинету или глобальные
        foreach ($response->json('data') as $status) {
            $this->assertTrue(
                $status['cabinet_id'] === $this->cabinet->id ||
                $status['cabinet_id'] === null
            );
        }
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/statuses?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        // Arrange
        Status::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/statuses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'name', 'color']
        ]));

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'color'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
    }

    public function test_index_without_required_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/statuses');

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/statuses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $status1 = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 1,
        ]);
        $status2 = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 2,
        ]);
        $status3 = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 3,
        ]);

        // Act
        $response = $this->getJson('/api/internal/statuses?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'asc',
            'fields' => ['id', 'name']
        ]));

        // Assert
        $response->assertOk();
    }

    public function test_can_create_status(): void
    {
        // Arrange
        $statusType = StatusType::factory()->create();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'color' => '#' . $this->faker->hexColor(),
            'type_id' => $statusType->id
        ];

        // Act
        $response = $this->postJson('/api/internal/statuses', $data);

        // Assert
        $response->assertCreated()
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('statuses', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'color' => $data['color'],
            'type_id' => $data['type_id']
        ]);
    }

    public function test_cannot_create_status_with_invalid_data(): void
    {
        // Arrange
        $invalidData = [
            'cabinet_id' => 'not-a-uuid',
            'name' => '', // пустое имя
            'color' => '', // пустой цвет
            'type_id' => 'invalid-uuid'
        ];

        // Act
        $response = $this->postJson('/api/internal/statuses', $invalidData);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'color',
                'type_id'
            ]);
    }

    public function test_cannot_create_status_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/statuses', []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'color',
                'type_id'
            ]);
    }

    public function test_cannot_create_status_for_another_cabinet(): void
    {
        // Arrange
        $statusType = StatusType::factory()->create();

        $data = [
            'cabinet_id' => $this->anotherCabinet->id,
            'name' => $this->faker->word(),
            'color' => '#' . $this->faker->hexColor(),
            'type_id' => $statusType->id
        ];

        // Act
        $response = $this->postJson('/api/internal/statuses', $data);

        // Assert
        $response->assertForbidden();
    }

    public function test_can_update_status(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $statusType = StatusType::factory()->create();

        $updateData = [
            'name' => 'Updated',
            'color' => '#' . $this->faker->hexColor(),
            'type_id' => $statusType->id
        ];

        // Act
        $response = $this->putJson("/api/internal/statuses/{$status->id}", $updateData);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas(
            'statuses',
            [
                'id' => $status->id,
                'name' => 'Updated'
            ]
        );
    }

    public function test_cannot_update_status_with_invalid_data(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'name' => '', // пустое имя
            'color' => '', // пустой цвет
            'type_id' => 'invalid-uuid'
        ];

        // Act
        $response = $this->putJson("/api/internal/statuses/{$status->id}", $invalidData);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'name',
                'color',
                'type_id'
            ]);
    }

    public function test_cannot_update_status_without_required_fields(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/statuses/{$status->id}", []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'name',
                'color',
                'type_id'
            ]);
    }

    public function test_cannot_update_status_from_another_cabinet(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        $updateData = [
            'name' => $this->faker->word(),
            'color' => '#' . $this->faker->hexColor(),
            'type_id' => StatusType::factory()->create()->id
        ];

        // Act
        $response = $this->putJson("/api/internal/statuses/{$status->id}", $updateData);

        // Assert
        $response->assertNotFound();

        // Проверяем что данные не изменились
        $this->assertDatabaseHas('statuses', [
            'id' => $status->id,
            'name' => $status->name,
            'color' => $status->color,
            'type_id' => $status->type_id
        ]);
    }

    public function test_cannot_update_non_existent_status(): void
    {
        // Arrange
        $updateData = [
            'name' => $this->faker->word(),
            'color' => '#' . $this->faker->hexColor(),
            'type_id' => StatusType::factory()->create()->id
        ];

        // Act
        $response = $this->putJson("/api/internal/statuses/" . $this->faker->uuid(), $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_create_status_with_minimum_required_fields(): void
    {
        // Arrange
        $statusType = StatusType::factory()->create();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'color' => '#' . $this->faker->hexColor(),
            'type_id' => $statusType->id
        ];

        // Act
        $response = $this->postJson('/api/internal/statuses', $data);

        // Assert
        $response->assertCreated()
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('statuses', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'color' => $data['color'],
            'type_id' => $data['type_id'],
        ]);
    }

    public function test_can_update_status_with_minimum_required_fields(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $statusType = StatusType::factory()->create();

        $updateData = [
            'name' => 'Updated name',
            'color' => '#' . $this->faker->hexColor(),
            'type_id' => $statusType->id
        ];

        // Act
        $response = $this->putJson("/api/internal/statuses/{$status->id}", $updateData);

        // Assert
        $response->assertNoContent();
    }

    public function test_can_show_status(): void
    {
        // Arrange
        $statusType = StatusType::factory()->create();
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Status',
            'color' => '#FF0000',
            'type_id' => $statusType->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/statuses/{$status->id}");

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'deleted_at',
                'cabinet_id',
                'name',
                'color',
                'type_id'
            ])
            ->assertJson([
                'id' => $status->id,
                'cabinet_id' => $this->cabinet->id,
                'name' => 'Test Status',
                'color' => '#FF0000',
                'type_id' => $statusType->id
            ]);
    }

    public function test_can_show_global_status(): void
    {
        // Arrange
        $statusType = StatusType::factory()->create();
        $status = Status::factory()->create([
            'cabinet_id' => null, // глобальный статус
            'name' => 'Global Status',
            'color' => '#00FF00',
            'type_id' => $statusType->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/statuses/{$status->id}");

        // Assert
        $response->assertOk()
            ->assertJson([
                'id' => $status->id,
                'cabinet_id' => null,
                'name' => 'Global Status',
                'color' => '#00FF00',
                'type_id' => $statusType->id
            ]);
    }

    public function test_cannot_show_status_from_another_cabinet(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/statuses/{$status->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_status(): void
    {
        // Act
        $response = $this->getJson("/api/internal/statuses/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_status(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/statuses/{$status->id}");

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseMissing('statuses', [
            'id' => $status->id
        ]);
    }

    public function test_cannot_delete_global_status(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => null // глобальный статус
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/statuses/{$status->id}");

        // Assert
        $response->assertForbidden();

        // Проверяем что статус не был удален
        $this->assertDatabaseHas('statuses', [
            'id' => $status->id
        ]);
    }

    public function test_cannot_delete_status_from_another_cabinet(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->anotherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/statuses/{$status->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что статус не был удален
        $this->assertDatabaseHas('statuses', [
            'id' => $status->id
        ]);
    }

    public function test_cannot_delete_non_existent_status(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/statuses/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_delete_status_in_use(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $shipment = Shipment::factory()->create([
            'status_id' => $status->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/statuses/{$status->id}");

        // Assert
        $response->assertStatus(422);
    }
}
