<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ProductCategoryTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Employee $employee;
    protected Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_product_categories_list(): void
    {
        // Arrange
        $categories = ProductCategory::factory(2)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        $categoriesOtherCabinet = ProductCategory::factory(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->cabinet->id}/categories");

        // Assert
        $response->assertOk()
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_cannot_get_product_categories_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/cabinets//categories');

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_get_product_categories_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/cabinets/invalid-uuid/categories');

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_get_product_categories_from_other_cabinet(): void
    {
        // Arrange
        ProductCategory::factory(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->otherCabinet->id}/categories");

        // Assert
        $response->assertForbidden();
    }

    public function test_returns_empty_collection_when_no_categories(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->cabinet->id}/categories");

        // Assert
        $response->assertOk()
            ->assertJsonCount(0, 'data');
    }

    public function test_can_create_product_category_with_minimal_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson("/api/internal/cabinets/{$this->cabinet->id}/category", $data);

        // Assert
        $response->assertCreated()
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('product_categories', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => $data['name'],
        ]);
    }

    public function test_can_create_product_category_with_all_fields(): void
    {
        // Arrange
        $parent = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $parent->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson("/api/internal/cabinets/{$this->cabinet->id}/category", $data);

        // Assert
        $response->assertCreated()
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('product_categories', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $parent->id,
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_create_product_category_without_required_fields(): void
    {
        // Act
        $response = $this->postJson("/api/internal/cabinets/{$this->cabinet->id}/category", []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
            ]);
    }

    public function test_cannot_create_product_category_with_invalid_cabinet_id(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'invalid-uuid',
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson("/api/internal/cabinets/{$this->cabinet->id}/category", $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_create_product_category_in_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson("/api/internal/cabinets/{$this->otherCabinet->id}/category", $data);

        // Assert
        $response->assertForbidden();

        $this->assertDatabaseMissing('product_categories', [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_create_product_category_with_parent_from_other_cabinet(): void
    {
        // Arrange
        $otherParent = ProductCategory::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $otherParent->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson("/api/internal/cabinets/{$this->cabinet->id}/category", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('product_categories', [
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $otherParent->id,
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_create_product_category_with_non_existent_parent(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $this->faker->uuid(),
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->postJson("/api/internal/cabinets/{$this->cabinet->id}/category", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('product_categories', [
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $data['parent_id'],
            'name' => $data['name'],
        ]);
    }

    public function test_can_update_product_category_with_minimal_fields(): void
    {
        // Arrange
        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $data = [
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$category->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_categories', [
            'id' => $category->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => $data['name'],
        ]);
    }

    public function test_can_update_product_category_with_all_fields(): void
    {
        // Arrange
        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $parent = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $data = [
            'parent_id' => $parent->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$category->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_categories', [
            'id' => $category->id,
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => $parent->id,
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_update_product_category_without_required_fields(): void
    {
        // Arrange
        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$category->id}", []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_update_product_category_from_other_cabinet(): void
    {
        // Arrange
        $otherCategory = ProductCategory::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $data = [
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->otherCabinet->id}/category/{$otherCategory->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('product_categories', [
            'id' => $otherCategory->id,
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_update_product_category_with_parent_from_other_cabinet(): void
    {
        // Arrange
        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $otherParent = ProductCategory::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $data = [
            'parent_id' => $otherParent->id,
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$category->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('product_categories', [
            'id' => $category->id,
            'parent_id' => $otherParent->id,
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_update_product_category_with_non_existent_parent(): void
    {
        // Arrange
        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $data = [
            'parent_id' => $this->faker->uuid(),
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$category->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('product_categories', [
            'id' => $category->id,
            'parent_id' => $data['parent_id'],
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_update_non_existent_product_category(): void
    {
        // Arrange
        $data = [
            'name' => $this->faker->word(),
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$this->faker->uuid()}", $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_show_product_category(): void
    {
        // Arrange
        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'parent_id' => null,
            'name' => $this->faker->word(),
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$category->id}");

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'cabinet_id',
                'parent_id',
                'name',
                'created_at',
                'updated_at',
            ])
            ->assertJsonPath('id', $category->id)
            ->assertJsonPath('cabinet_id', $this->cabinet->id)
            ->assertJsonPath('name', $category->name);
    }

    public function test_cannot_show_product_category_from_other_cabinet(): void
    {
        // Arrange
        $otherCategory = ProductCategory::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->otherCabinet->id}/category/{$otherCategory->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_product_category(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$this->faker->uuid()}");

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_product_category(): void
    {
        // Arrange
        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$category->id}");

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseMissing('product_categories', [
            'id' => $category->id,
        ]);
    }

    public function test_cannot_delete_product_category_from_other_cabinet(): void
    {
        // Arrange
        $otherCategory = ProductCategory::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinets/{$this->otherCabinet->id}/category/{$otherCategory->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('product_categories', [
            'id' => $otherCategory->id,
        ]);
    }

    public function test_cannot_delete_non_existent_product_category(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/cabinets/{$this->cabinet->id}/category/{$this->faker->uuid()}");

        // Assert
        $response->assertNotFound();
    }
}
