<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\NumberingType;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CabinetSettingTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        Storage::fake('s3-images');

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinetSettings = CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();
        CabinetSettings::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'numbering_type' => NumberingType::OnlyNumbers->value
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_show_cabinet_settings(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->cabinet->id}/settings");

        // Assert
        $response->assertStatus(200);
    }

    public function test_cannot_show_other_cabinet_settings(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinets/{$this->otherCabinet->id}/settings");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_cabinet_settings(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinets/" . $this->faker->uuid . "/settings");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_cabinet_settings(): void
    {
        // Arrange
        $data = [
            'numbering_type' => NumberingType::CPNumbers->value,
            'email' => $this->faker->email,
            'global_numbering' => true,
            'use_cabinet_email' => true,
            'check_stock' => true,
            'check_min_price' => true,
            'use_bin' => true,
            'use_product_series' => true,
            'auto_update_purchase_price' => true,
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/settings", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_settings', array_merge(
            ['cabinet_id' => $this->cabinet->id],
            $data
        ));
    }

    public function test_cannot_update_cabinet_settings_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'numbering_type' => 'invalid_type',
            'email' => 'invalid-email',
            'global_numbering' => 'not-a-boolean',
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/settings", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'numbering_type',
                'email',
                'global_numbering'
            ]);
    }

    public function test_cannot_update_other_cabinet_settings(): void
    {
        // Arrange
        $data = [
            'numbering_type' => NumberingType::CPNumbers->value,
            'email' => $this->faker->email
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->otherCabinet->id}/settings", $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_non_existent_cabinet_settings(): void
    {
        // Arrange
        $data = [
            'numbering_type' => NumberingType::CPNumbers->value
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/" . $this->faker->uuid . "/settings", $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_cabinet_settings_with_minimal_data(): void
    {
        // Arrange
        $data = [
            'numbering_type' => NumberingType::CPNumbers->value
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/settings", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinet_settings', [
            'cabinet_id' => $this->cabinet->id,
            'numbering_type' => NumberingType::CPNumbers->value
        ]);
    }

    public function test_cannot_update_cabinet_settings_without_required_fields(): void
    {
        // Act
        $response = $this->putJson("/api/internal/cabinets/{$this->cabinet->id}/settings", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['numbering_type']);
    }
}
