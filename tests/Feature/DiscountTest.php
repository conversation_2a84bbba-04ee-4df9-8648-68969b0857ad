<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\DiscountTypeEnum;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Discount;
use App\Models\Employee;
use App\Models\Product;
use App\Models\ContractorGroups;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DiscountTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        // Создаем основной кабинет
        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем другой кабинет для тестов мультитенантности
        $this->otherCabinet = Cabinet::factory()->create();

        // Создаем сотрудника
        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_discounts_list(): void
    {
        // Создаем скидки для текущего кабинета
        Discount::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем скидки для другого кабинета
        Discount::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'employee_id',
                        'department_id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'name',
                        'type',
                        'status',
                        'cabinet_price_id',
                        'fixed_discount',
                        'products_services',
                        'contractors',
                        'accrual_rule',
                        'writeoff_rule',
                        'max_proc_payment',
                        'accrual_writeoff'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только скидки текущего кабинета
        $this->assertCount(3, $response->json('data'));
        foreach ($response->json('data') as $discount) {
            $this->assertEquals($this->cabinet->id, $discount['cabinet_id']);
        }
    }

    public function test_cannot_get_discounts_from_other_cabinet(): void
    {
        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'cabinet_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_sorting(): void
    {
        // Создаем скидки с разными значениями для сортировки
        Discount::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'AAA Discount'
        ]);
        Discount::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'BBB Discount'
        ]);
        Discount::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'CCC Discount'
        ]);

        // Проверяем сортировку по возрастанию
        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(200);
        $titles = collect($response->json('data'))->pluck('name')->values();
        $this->assertEquals(['AAA Discount', 'BBB Discount', 'CCC Discount'], $titles->toArray());

        // Проверяем сортировку по убыванию
        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        $response->assertStatus(200);
        $titles = collect($response->json('data'))->pluck('name')->values();
        $this->assertEquals(['CCC Discount', 'BBB Discount', 'AAA Discount'], $titles->toArray());
    }

    public function test_index_with_field_selection(): void
    {
        Discount::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['id', 'name']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name'
                    ]
                ]
            ]);

        // Проверяем что другие поля не включены в ответ
        $discount = $response->json('data')[0];
        $this->assertNotContains([
            'created_at',
            'updated_at',
            'deleted_at',
            'cabinet_id',
            'type',
            'status',
            'type_product_card',
            'fixed_discount',
            'products_services',
            'сontractors',
            'accrual_rule',
            'writeoff_rule',
            'max_proc_payment',
            'accrual_writeoff'
        ], $discount);
    }

    public function test_index_pagination(): void
    {
        // Создаем 15 скидок
        Discount::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Запрашиваем первую страницу
        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $this->assertCount(10, $response->json('data'));
        $this->assertEquals(1, $response->json('meta.current_page'));
        $this->assertEquals(2, $response->json('meta.last_page'));
        $this->assertEquals(15, $response->json('meta.total'));

        // Запрашиваем вторую страницу
        $response = $this->getJson('/api/internal/discounts?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $this->assertCount(5, $response->json('data'));
        $this->assertEquals(2, $response->json('meta.current_page'));
    }

    public function test_can_create_discount(): void
    {
        $type = $this->faker->randomElement(DiscountTypeEnum::class);
        $data = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Discount',
            'type' => $type->value,
            'status' => true,
            'fixed_discount' => 10,
            'products_services' => true,
            'сontractors' => false,
            'accrual_rule' => 1,
            'writeoff_rule' => 2,
            'max_proc_payment' => 50,
            'accrual_writeoff' => true
        ];

        $response = $this->postJson('/api/internal/discounts', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('discounts', [
            'id' => $response->json('id'),
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Discount',
            'type' => $type->value,
            'status' => true,
            'fixed_discount' => 10,
            'products_services' => true,
            'contractors' => false,
            'accrual_rule' => 100,
            'writeoff_rule' => 2,
            'max_proc_payment' => 50,
            'accrual_writeoff' => true
        ]);
    }

    public function test_can_create_discount_with_related_data(): void
    {
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractorGroup = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Discount with Relations',
            'type' => DiscountTypeEnum::CUMULATIVE_DISCOUNT->value,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'status' => true,
            'products' => [
                [
                    'product_id' => $product->id,
                    'discount_id' => null
                ]
            ],
            'contractor_groups' => [
                [
                    'group_id' => $contractorGroup->id,
                    'discount_id' => null
                ]
            ],
            'savings' => [
                [
                    'amount' => 1000,
                    'procent' => 5,
                    'discount_id' => null
                ]
            ]
        ];

        $response = $this->postJson('/api/internal/discounts', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $discountId = $response->json('id');

        // Проверяем что скидка создалась
        $this->assertDatabaseHas('discounts', [
            'id' => $discountId,
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Discount with Relations'
        ]);

        // Проверяем что связанные данные создались
        $this->assertDatabaseHas('discount_products', [
            'discount_id' => $discountId,
            'product_id' => $data['products'][0]['product_id']
        ]);

        $this->assertDatabaseHas('discount_contractor_group', [
            'discount_id' => $discountId,
            'group_id' => $data['contractor_groups'][0]['group_id']
        ]);

        $this->assertDatabaseHas('discount_savings', [
            'discount_id' => $discountId,
            'amount' => $data['savings'][0]['amount'],
            'procent' => $data['savings'][0]['procent']
        ]);
    }

    public function test_cannot_create_discount_in_other_cabinet(): void
    {
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Discount',
            'type' => DiscountTypeEnum::BONUS_PROGRAM->value,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'status' => true
        ];

        $response = $this->postJson('/api/internal/discounts', $data);

        $response->assertStatus(403);

        $this->assertDatabaseMissing('discounts', [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Discount'
        ]);
    }

    public function test_cannot_create_discount_without_required_fields(): void
    {
        $response = $this->postJson('/api/internal/discounts', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'name']);
    }

    public function test_cannot_create_discount_with_invalid_data(): void
    {
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'name' => '',
            'type' => 'invalid-type',
            'status' => 'not-a-boolean',
            'fixed_discount' => 'not-a-number',
            'products_services' => 'not-a-boolean',
            'contractors' => 'not-a-boolean',
            'accrual_rule' => 'not-a-number',
            'writeoff_rule' => 'not-a-number',
            'max_proc_payment' => 'not-a-number',
            'accrual_writeoff' => 'not-a-boolean',
            'products' => [
                [
                    'id' => 'not-a-uuid',
                    'product_id' => 'not-a-uuid'
                ]
            ],
            'groups' => [
                [
                    'id' => 'not-a-uuid',
                    'group_id' => 'not-a-uuid'
                ]
            ],
            'savings' => [
                [
                    'id' => 'not-a-uuid',
                    'amount' => 'not-a-number',
                    'procent' => 'not-a-number'
                ]
            ]
        ];

        $response = $this->postJson('/api/internal/discounts', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'type',
                'status',
                'employee_id',
                'department_id',
                'fixed_discount',
                'products_services',
                'contractors',
                'accrual_rule',
                'writeoff_rule',
                'max_proc_payment',
                'accrual_writeoff',
                'products.0.product_id',
                'savings.0.amount',
                'savings.0.procent'
            ]);
    }

    public function test_can_create_discount_with_minimal_required_fields(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Minimal Discount',
            'type' => DiscountTypeEnum::BONUS_PROGRAM->value,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'status' => true
        ];

        $response = $this->postJson('/api/internal/discounts', $data);

        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('discounts', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Minimal Discount'
        ]);
    }
}
