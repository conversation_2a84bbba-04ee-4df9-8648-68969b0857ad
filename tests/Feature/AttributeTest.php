<?php

namespace Tests\Feature;

use App\Models\Attribute;
use App\Models\AttributeGroup;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AttributeTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_attributes_list(): void
    {
        // Arrange
        // Создаем атрибуты для нашего кабинета
        Attribute::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем атрибут для другого кабинета
        Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes?' . http_build_query([
            'cabinet_id' => $this->cabinet->id
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'cabinet_id',
                        'name',
                        'created_at',
                        'updated_at',
                        'attribute_groups_id',
                        'description',
                        'sort_order',
                        'status'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только атрибуты нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_get_attributes_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/attributes');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_attributes_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/attributes?' . http_build_query([
            'cabinet_id' => 'not-a-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_attributes_from_other_cabinet(): void
    {
        // Arrange
        // Создаем атрибуты для другого кабинета
        Attribute::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_get_attributes_with_pagination(): void
    {
        // Arrange
        Attribute::factory()->count(15)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/attributes?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 2,
            'per_page' => 10
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertEquals(10, $response->json('meta.per_page'));
        $this->assertEquals(15, $response->json('meta.total'));
        $this->assertCount(5, $response->json('data'));
    }

    public function test_can_get_attributes_with_sorting(): void
    {
        // Arrange
        $attributes = [
            'A attribute' => Attribute::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'A attribute'
            ]),
            'B attribute' => Attribute::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'B attribute'
            ]),
            'C attribute' => Attribute::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'name' => 'C attribute'
            ])
        ];

        // Act - получаем отсортированный по имени список
        $response = $this->getJson('/api/internal/attributes?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200);

        $names = collect($response->json('data'))->pluck('name')->values();
        $expectedNames = collect(['C attribute', 'B attribute', 'A attribute']);

        $this->assertEquals($expectedNames, $names);
    }

    public function test_can_get_attributes_with_fields(): void
    {
        Attribute::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        // Act - получаем отсортированный по имени список
        $response = $this->getJson(
            '/api/internal/attributes?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'fields' => 'name'
            ])
        );

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'name'
                    ]
                ],
                'meta'
            ]);
    }
    public function test_cannot_get_attributes_with_invalid_fields(): void
    {
        Attribute::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        // Act - получаем отсортированный по имени список
        $response = $this->getJson('/api/internal/attributes?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'fields' => ['invalid-field']
            ]));

        // Assert
        $response->assertJsonValidationErrors('fields.0');
    }

    public function test_can_create_attribute(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => true,
            'attribute_groups_id' => null
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('attributes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'description' => $data['description'],
            'sort_order' => $data['sort_order'],
            'status' => $data['status'],
        ]);
    }

    public function test_can_create_attribute_with_minimal_required_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'sort_order' => 1
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('attributes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
        ]);
    }

    public function test_cannot_create_attribute_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/attributes', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
            ]);
    }

    public function test_cannot_create_attribute_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'not-a-uuid',
            'name' => str_repeat('a', 101), // превышает максимальную длину
            'description' => str_repeat('a', 301), // превышает максимальную длину
            'sort_order' => 'not-an-integer',
            'status' => 'not-a-boolean',
            'attribute_groups_id' => 'not-a-uuid'
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'description',
                'sort_order',
                'status',
                'attribute_groups_id'
            ]);
    }

    public function test_cannot_create_attribute_for_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $this->faker->word(),
            'sort_order' => 1
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_create_attribute_with_attribute_group(): void
    {
        // Arrange

        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'attribute_groups_id' => $attributeGroup->id,
            'sort_order' => 1
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('attributes', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
            'attribute_groups_id' => $data['attribute_groups_id'],
        ]);
    }

    public function test_cannot_create_attribute_with_invalid_attribute_group(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'attribute_groups_id' => 'not-a-uuid',
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['attribute_groups_id']);
    }

    public function test_cannot_create_attribute_with_other_cabinet_attribute_group(): void
    {
        $group = AttributeGroup::factory()->create(['cabinet_id' => $this->otherCabinet->id]);
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker->word(),
            'attribute_groups_id' => $group->id,
            'sort_order' => 1
        ];

        // Act
        $response = $this->postJson('/api/internal/attributes', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_update_attribute(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => true,
            'attribute_groups_id' => null
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/{$attribute->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('attributes', array_merge(
            ['id' => $attribute->id],
            $data
        ));
    }

    public function test_can_update_attribute_with_minimal_fields(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'old description',
            'sort_order' => 1,
            'status' => true
        ]);

        $data = [
            'name' => $this->faker->word(),
            'sort_order' => 2,
            'description' => 'new description'
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/{$attribute->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('attributes', [
            'id' => $attribute->id,
            'name' => $data['name'],
            'description' => $data['description'],
            'sort_order' => $data['sort_order'],
            'status' => $attribute->status
        ]);
    }

    public function test_cannot_update_attribute_without_required_fields(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/attributes/{$attribute->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_update_attribute_with_invalid_data(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => str_repeat('a', 101), // превышает максимальную длину
            'description' => str_repeat('a', 301), // превышает максимальную длину
            'sort_order' => 'not-an-integer',
            'status' => 'not-a-boolean',
            'attribute_groups_id' => 'not-a-uuid'
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/{$attribute->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'description',
                'sort_order',
                'status',
                'attribute_groups_id'
            ]);
    }

    public function test_cannot_update_non_existent_attribute(): void
    {
        // Act
        $response = $this->putJson("/api/internal/attributes/" . $this->faker->uuid(), [
            'name' => $this->faker->word(),
            'sort_order' => 1
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_attribute_from_other_cabinet(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => $this->faker->word(),
            'sort_order' => 1
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/{$attribute->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('attributes', [
            'id' => $attribute->id,
            'name' => $attribute->name // проверяем что имя не изменилось
        ]);
    }

    public function test_can_update_attribute_with_attribute_group(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $attributeGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'name' => $this->faker->word(),
            'attribute_groups_id' => $attributeGroup->id,
            'sort_order' => 1
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/{$attribute->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('attributes', [
            'id' => $attribute->id,
            'name' => $data['name'],
            'attribute_groups_id' => $data['attribute_groups_id']
        ]);
    }

    public function test_cannot_update_attribute_with_other_cabinet_attribute_group(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetGroup = AttributeGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'name' => $this->faker->word(),
            'attribute_groups_id' => $otherCabinetGroup->id,
            'sort_order' => 1
        ];

        // Act
        $response = $this->putJson("/api/internal/attributes/{$attribute->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('attributes', [
            'id' => $attribute->id,
            'name' => $attribute->name, // проверяем что имя не изменилось
            'attribute_groups_id' => $attribute->attribute_groups_id // проверяем что группа не изменилась
        ]);
    }

    public function test_can_delete_attribute(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/attributes/{$attribute->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('attributes', [
            'id' => $attribute->id
        ]);
    }

    public function test_cannot_delete_non_existent_attribute(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/attributes/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_delete_attribute_from_other_cabinet(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/attributes/{$attribute->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('attributes', [
            'id' => $attribute->id
        ]);
    }

    public function test_can_show_attribute(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Attribute',
            'description' => 'Test Description',
            'sort_order' => 1,
            'status' => true
        ]);

        // Act
        $response = $this->getJson("/api/internal/attributes/{$attribute->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'cabinet_id',
                'name',
                'description',
                'attribute_groups_id',
                'sort_order',
                'status',
                'created_at',
                'updated_at'
            ])
            ->assertJson([
                'id' => $attribute->id,
                'cabinet_id' => $this->cabinet->id,
                'name' => 'Test Attribute',
                'description' => 'Test Description',
                'sort_order' => 1,
                'status' => true
            ]);
    }

    public function test_cannot_show_non_existent_attribute(): void
    {
        // Act
        $response = $this->getJson("/api/internal/attributes/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_show_attribute_from_other_cabinet(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/attributes/{$attribute->id}");

        // Assert
        $response->assertNotFound();
    }
}
