<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\User;
use App\Models\WarehouseGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class WarehouseGroupTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_warehouse_groups_list(): void
    {
        // Arrange
        // Создаем 3 группы для нашего кабинета
        WarehouseGroup::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем группу для другого кабинета
        WarehouseGroup::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/warehouses/groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'name',
                        'cabinet_id',
                        'parent_id',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего кабинета
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_can_create_warehouse_group(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Group',
        ];

        // Act
        $response = $this->postJson('/api/internal/warehouses/groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('warehouse_groups', [
            'id' => $response->json('id'),
            'cabinet_id' => $data['cabinet_id'],
            'name' => $data['name'],
        ]);
    }

    public function test_can_show_warehouse_group(): void
    {
        // Arrange
        $group = WarehouseGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Group',
        ]);

        // Act
        $response = $this->getJson("/api/internal/warehouses/groups/{$group->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'name',
                'cabinet_id',
                'parent_id'
            ])
            ->assertJson([
                'id' => $group->id,
                'cabinet_id' => $this->cabinet->id,
                'name' => 'Test Group',
            ]);
    }

    public function test_can_update_warehouse_group(): void
    {
        // Arrange
        $group = WarehouseGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Old Name',
        ]);

        $data = [
            'name' => 'Updated Name',
        ];

        // Act
        $response = $this->putJson("/api/internal/warehouses/groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('warehouse_groups', [
            'id' => $group->id,
            'name' => $data['name'],
        ]);
    }

    public function test_can_delete_warehouse_group(): void
    {
        // Arrange
        $group = WarehouseGroup::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/warehouses/groups/{$group->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('warehouse_groups', [
            'id' => $group->id
        ]);
    }
}
