<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Product;
use App\Models\BinItem;
use App\Models\Employee;
use Illuminate\Support\Carbon;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BinTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_cannot_get_bin_items_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin');

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_bin_items_with_invalid_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin?' . http_build_query([
            'cabinet_id' => 'not-a-uuid'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_bin_items_from_other_cabinet(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_get_bin_items_with_pagination(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'cabinet_id',
                        'record_id',
                        'table_name',
                        'record_name',
                        'deleted_at',
                        'created_at',
                        'updated_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        // Act
        $response = $this->getJson('/api/internal/bin?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_cannot_bulk_delete_without_required_fields(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/bin', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids'
            ]);
    }

    public function test_cannot_bulk_delete_with_invalid_data(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/bin', [
            'cabinet_id' => 'not-a-uuid',
            'ids' => ['not-a-uuid']
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids.0'
            ]);
    }

    public function test_cannot_bulk_delete_from_other_cabinet(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/bin', [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => [$this->faker->uuid()]
        ]);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_bulk_delete(): void
    {
        // Arrange
        $binItems = [];
        for ($i = 0; $i < 3; $i++) {
            $binItems[] = [
                'id' => $this->faker->uuid(),
                'cabinet_id' => $this->cabinet->id,
                'record_id' => $this->faker->uuid(),
                'table_name' => 'products',
                'record_name' => $this->faker->word(),
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => now()
            ];
        }
        DB::table('bin_items')->insert($binItems);

        // Act
        $response = $this->deleteJson('/api/internal/bin', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => array_column($binItems, 'id')
        ]);

        // Assert
        $response->assertStatus(204);

        // Проверяем что записи удалены из bin_items
        foreach ($binItems as $item) {
            $this->assertDatabaseMissing('bin_items', [
                'id' => $item['id']
            ]);
        }
    }

    public function test_cannot_bulk_recover_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/bin', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids'
            ]);
    }

    public function test_cannot_bulk_recover_with_invalid_data(): void
    {
        // Act
        $response = $this->postJson('/api/internal/bin', [
            'cabinet_id' => 'not-a-uuid',
            'ids' => ['not-a-uuid']
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids.0'
            ]);
    }

    public function test_cannot_bulk_recover_from_other_cabinet(): void
    {
        // Act
        $response = $this->postJson('/api/internal/bin', [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => [$this->faker->uuid()]
        ]);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_bulk_recover(): void
    {
        // Arrange
        $products = Product::factory()
            ->count(10)
            ->create([
                'cabinet_id' => $this->cabinet->id,
                'deleted_at' => Carbon::now(),
            ]);

        $binItems = BinItem::factory()
            ->count(10)
            ->sequence(fn ($seq) => [
                'cabinet_id' => $this->cabinet->id,
                'table_name' => 'products',
                'record_id' => $products[$seq->index]->id,
            ])
            ->create()
            ->pluck('id')
            ->toArray();

        // Act
        $response = $this->postJson('/api/internal/bin', [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $binItems,
        ]);

        // Assert
        $response->assertStatus(204);

        // Проверяем, что записи удалены из bin_items
        $this->assertDatabaseMissing('bin_items', [
            'id' => $binItems, // Массив ID можно передать сразу
        ]);

        // Проверяем, что продукты восстановлены
        $this->assertDatabaseHas('products', [
            'id' => $products->pluck('id')->toArray(),
            'deleted_at' => null,
        ]);
    }
}
