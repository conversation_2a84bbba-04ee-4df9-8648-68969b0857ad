<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Events\CabinetCreated;
use App\Models\CabinetEmployee;
use Illuminate\Support\Facades\Event;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CabinetTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->user = $user;

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
    }

    public function test_can_get_all_cabinets(): void
    {
        // Arrange
        $ownCabinets = Cabinet::factory()->count(2)->create([
            'user_id' => $this->user->id
        ])->pluck('id')->toArray();

        $invitedCabinets = Cabinet::factory()
            ->count(3)
            ->create()
            ->pluck('id')
            ->toArray();

        $cabinets = array_merge($ownCabinets, $invitedCabinets);
        $employee = Employee::factory()
            ->count(5)
            ->create(['user_id' => $this->user->id])
            ->pluck('id')
            ->toArray();

        CabinetEmployee::factory()
            ->count(5)
            ->sequence(fn ($seq) => [
                'cabinet_id' => $cabinets[$seq->index],
                'employee_id' => $employee[$seq->index],
            ])
            ->create()
            ->pluck('id')
            ->toArray();

        // Создаем чужие кабинеты
        Cabinet::factory()->count(2)->create();

        // Act
        $response = $this->getJson('/api/internal/cabinets?' . http_build_query([
            'filter' => 'all'
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'user_id',
                    'name',
                    'created_at',
                    'updated_at',
                    'deleted_at'
                ]
            ]);

        // Проверяем что получили только свои и приглашенные кабинеты (5 штук)
        $response->assertJsonCount(5);
    }

    public function test_can_get_own_cabinets(): void
    {
        // Arrange
        $ownCabinets = Cabinet::factory()
            ->count(2)
            ->create([
                'user_id' => $this->user->id
            ])
            ->pluck('id')
            ->toArray();

        $employee = Employee::factory()
            ->count(2)
            ->create(['user_id' => $this->user->id])
            ->pluck('id')
            ->toArray();

        CabinetEmployee::factory()
            ->count(2)
            ->sequence(fn ($seq) => [
                'cabinet_id' => $ownCabinets[$seq->index],
                'employee_id' => $employee[$seq->index],
            ])
            ->create()
            ->pluck('id')
            ->toArray();

        // Act
        $response = $this->getJson('/api/internal/cabinets?' . http_build_query([
            'filter' => 'own'
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                    '*' => [
                        'id',
                        'user_id',
                        'name',
                        'created_at',
                        'updated_at'
                    ]
            ]);

        // Проверяем что получили только свои кабинеты (2 штуки)
        $response->assertJsonCount(2);

        // Проверяем что все полученные кабинеты принадлежат пользователю
        foreach ($response->json() as $cabinet) {
            $this->assertEquals($this->user->id, $cabinet['user_id']);
        }
    }

    public function test_can_get_invited_cabinets(): void
    {
        // Arrange
        // Создаем собственные кабинеты
        $ownCabinets = Cabinet::factory()->count(2)->create([
            'user_id' => $this->user->id
        ]);

        // Создаем кабинеты, куда пользователь приглашен
        $invitedCabinets = Cabinet::factory()
            ->count(3)
            ->create()
            ->pluck('id')
            ->toArray();

        $employee = Employee::factory()
            ->count(3)
            ->create(['user_id' => $this->user->id])
            ->pluck('id')
            ->toArray();

        CabinetEmployee::factory()
            ->count(3)
            ->sequence(fn ($seq) => [
                'cabinet_id' => $invitedCabinets[$seq->index],
                'employee_id' => $employee[$seq->index],
            ])
            ->create()
            ->pluck('id')
            ->toArray();

        // Act
        $response = $this->getJson('/api/internal/cabinets?' . http_build_query([
            'filter' => 'invited'
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                    '*' => [
                        'id',
                        'user_id',
                        'name',
                        'created_at',
                        'updated_at'
                    ]
            ]);

        // Проверяем что получили только приглашенные кабинеты (3 штуки)
        $response->assertJsonCount(3);

        // Проверяем что все полученные кабинеты не принадлежат пользователю
        foreach ($response->json() as $cabinet) {
            $this->assertNotEquals($this->user->id, $cabinet['user_id']);
        }
    }

    public function test_index_without_or_incorrect_filter(): void
    {
        // Arrange
        $ownCabinets = Cabinet::factory()->count(2)->create([
            'user_id' => $this->user->id
        ])->pluck('id')->toArray();

        $invitedCabinets = Cabinet::factory()
            ->count(3)
            ->create()
            ->pluck('id')
            ->toArray();

        $cabinets = array_merge($ownCabinets, $invitedCabinets);
        $employee = Employee::factory()
            ->count(5)
            ->create(['user_id' => $this->user->id])
            ->pluck('id')
            ->toArray();

        CabinetEmployee::factory()
            ->count(5)
            ->sequence(fn ($seq) => [
                'cabinet_id' => $cabinets[$seq->index],
                'employee_id' => $employee[$seq->index],
            ])
            ->create()
            ->pluck('id')
            ->toArray();

        // Создаем чужие кабинеты
        Cabinet::factory()->count(2)->create();

        // Act
        $response = $this->getJson('/api/internal/cabinets?');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'user_id',
                    'name',
                    'created_at',
                    'updated_at',
                    'deleted_at'
                ]
            ]);

        // Проверяем что получили только свои и приглашенные кабинеты (5 штук)
        $response->assertJsonCount(5);

        $response = $this->getJson('/api/internal/cabinets?' . http_build_query(
            ['filter' => 'incorrect-filter']
        ));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'user_id',
                    'name',
                    'created_at',
                    'updated_at',
                    'deleted_at'
                ]
            ]);

        // Проверяем что получили только свои и приглашенные кабинеты (5 штук)
        $response->assertJsonCount(5);
    }

    public function test_can_create_cabinet(): void
    {
        // Arrange
        $data = [
            'name' => $this->faker->company()
        ];

        Event::fake();
        // Act
        $response = $this->postJson('/api/internal/cabinets', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('cabinets', [
            'id' => $response->json('id'),
            'user_id' => $this->user->id,
            'name' => $data['name']
        ]);

        Event::assertDispatched(CabinetCreated::class);
    }

    public function test_cannot_create_cabinet_without_name(): void
    {
        // Act
        $response = $this->postJson('/api/internal/cabinets', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_create_cabinet_with_too_long_name(): void
    {
        // Arrange
        $data = [
            'name' => str_repeat('a', 101) // 101 символ, максимум 100
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinets', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_create_cabinet_with_non_string_name(): void
    {
        // Arrange
        $data = [
            'name' => ['array instead of string']
        ];

        // Act
        $response = $this->postJson('/api/internal/cabinets', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_can_update_cabinet(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Old Name'
        ]);

        $employee = Employee::factory()
            ->create(['user_id' => $this->user->id]);

        CabinetEmployee::factory()
            ->create(
                [
                    'cabinet_id' => $cabinet->id,
                    'employee_id' => $employee->id,
                ]
            );

        $data = [
            'name' => 'New Cabinet Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$cabinet->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('cabinets', [
            'id' => $cabinet->id,
            'user_id' => $this->user->id,
            'name' => $data['name']
        ]);
    }

    public function test_cannot_update_cabinet_without_name(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$cabinet->id}", []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);

        $this->assertDatabaseHas('cabinets', [
            'id' => $cabinet->id,
            'name' => $cabinet->name // Проверяем что имя не изменилось
        ]);
    }

    public function test_cannot_update_cabinet_with_too_long_name(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);

        $data = [
            'name' => str_repeat('a', 101) // 101 символ, максимум 100
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$cabinet->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);

        $this->assertDatabaseHas('cabinets', [
            'id' => $cabinet->id,
            'name' => $cabinet->name // Проверяем что имя не изменилось
        ]);
    }

    public function test_cannot_update_non_existent_cabinet(): void
    {
        // Act
        $response = $this->putJson("/api/internal/cabinets/" . $this->faker->uuid(), [
            'name' => 'New Name'
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_cabinet_of_other_user(): void
    {
        // Arrange
        $otherCabinet = Cabinet::factory()->create();

        $data = [
            'name' => 'New Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$otherCabinet->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('cabinets', [
            'id' => $otherCabinet->id,
            'name' => $otherCabinet->name // Проверяем что имя не изменилось
        ]);
    }

    public function test_cannot_update_cabinet_with_non_string_name(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);

        $data = [
            'name' => ['array instead of string']
        ];

        // Act
        $response = $this->putJson("/api/internal/cabinets/{$cabinet->id}", $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);

        $this->assertDatabaseHas('cabinets', [
            'id' => $cabinet->id,
            'name' => $cabinet->name // Проверяем что имя не изменилось
        ]);
    }

    public function test_can_show_cabinet(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);

        $employee = Employee::factory()->create(['user_id' => $this->user->id]);

        CabinetEmployee::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $employee->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/{$cabinet->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'user_id',
                'name',
                'created_at',
                'updated_at',
                'deleted_at'
            ]);

        $this->assertEquals($cabinet->id, $response->json('id'));
        $this->assertEquals($cabinet->name, $response->json('name'));
        $this->assertEquals($this->user->id, $response->json('user_id'));
    }

    public function test_can_show_invited_cabinet(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $this->employee->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/cabinets/{$cabinet->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'user_id',
                'name',
                'created_at',
                'updated_at',
                'deleted_at'
            ]);

        $this->assertEquals($cabinet->id, $response->json('id'));
    }

    public function test_cannot_show_cabinet_of_other_user(): void
    {
        // Arrange
        $otherCabinet = Cabinet::factory()->create();

        // Act
        $response = $this->getJson("/api/internal/cabinets/{$otherCabinet->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_cabinet(): void
    {
        // Act
        $response = $this->getJson("/api/internal/cabinets/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_cabinet(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id
        ]);

        $employee = Employee::factory()->create(['user_id' => $this->user->id]);

        CabinetEmployee::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $employee->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinets/{$cabinet->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('cabinets', [
            'id' => $cabinet->id
        ]);
    }

    public function test_cannot_delete_cabinet_of_other_user(): void
    {
        // Arrange
        $otherCabinet = Cabinet::factory()->create();

        // Act
        $response = $this->deleteJson("/api/internal/cabinets/{$otherCabinet->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('cabinets', [
            'id' => $otherCabinet->id
        ]);
    }

    public function test_cannot_delete_non_existent_cabinet(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/cabinets/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_delete_invited_cabinet(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'cabinet_id' => $cabinet->id,
            'employee_id' => $this->employee->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/cabinets/{$cabinet->id}");

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('cabinets', [
            'id' => $cabinet->id
        ]);
    }
}
