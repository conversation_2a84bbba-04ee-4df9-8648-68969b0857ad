<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('barcodes', function (Blueprint $table) {

            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->uuid('barcodable_id');                  // полиморфные связи с таблицами
            $table->string('barcodable_type', 255);         // имя таблицы products packing ...
            $table->tinyInteger('type');                    // тип штрихкода EAN8, EAN13 ...
            $table->string('value');                      // штрихкод 2000...132
            $table->integer('sort')->default(0);            // сортировка
            $table->boolean('is_generated')->default(0);    // сортировка
            $table->unique(['cabinet_id', 'type', 'value']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('barcodes');
    }
};
