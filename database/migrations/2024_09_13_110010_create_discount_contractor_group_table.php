<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discount_contractor_group', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('discount_id')->constrained('discounts')->onDelete('cascade');
            $table->foreignUuid('group_id')->constrained('contractor_groups')->onDelete('cascade');

            // Уникальный индекс для предотвращения дублирования связей
            $table->unique(['discount_id', 'group_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discount_contractor_group');
    }
};
