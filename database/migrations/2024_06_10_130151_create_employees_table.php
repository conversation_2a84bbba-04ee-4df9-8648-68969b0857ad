<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->dateTime('archived_at')->nullable();

            $table->foreignId('user_id')->constrained();
            $table->string('lastname', 255)->nullable();
            $table->string('firstname', 255);
            $table->string('patronymic', 255)->nullable();
            $table->string('telephone', 255)->nullable();
            $table->string('email', 255);
            $table->string('status', 255)->default('active');

            $table->string('job_number')->nullable();
            $table->string('citizenship')->nullable();
            $table->string('gender')->nullable();
            $table->string('inn')->nullable();
            $table->string('id_card')->nullable();

            $table->string('passport_series')->nullable();
            $table->string('passport_number')->nullable();
            $table->date('passport_issue_date')->nullable();
            $table->string('who_issued_passport')->nullable();
            $table->string('division_code')->nullable();
            $table->string('registration_address')->nullable();
            $table->string('temporary_registration_address')->nullable();

            $table->string('driver_license_series')->nullable();
            $table->string('driver_license_number')->nullable();
            $table->string('driver_license_issue_date')->nullable();
            $table->string('driver_license_expiration_date')->nullable();
            $table->string('driver_license_category')->nullable();

            $table->string('military_card')->nullable();
            $table->date('hire_date')->nullable();
            $table->date('dismissal_date')->nullable();
            $table->string('position')->nullable();
            $table->string('salary')->nullable();
            $table->string('labor_fund')->nullable();
            $table->string('planned_advance')->nullable();
            $table->json('work_schedule')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
