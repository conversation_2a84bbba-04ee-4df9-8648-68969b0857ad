<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->uuid('documentable_id')->primary();
            $table->timestamps();
            $table->softDeletes();
            $table->string('documentable_type');
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete(); //костылизация
            $table->uuid('tree_id')->index(); // Идентификатор дерева

            $table->unique(['documentable_id', 'documentable_type']);
            $table->unique(['tree_id', 'documentable_id', 'documentable_type']);

            $table->integer('lft')->index();
            $table->integer('rgt')->index();
            $table->uuid('parent_id')->nullable();// Родитель узла
        });

        Schema::table('documents', function (Blueprint $table) {
            $table->foreign('parent_id')->references('documentable_id')->on('documents')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
