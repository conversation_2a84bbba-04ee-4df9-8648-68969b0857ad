<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_transaction_services', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('ozon_transaction_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->decimal('price', 20, 4)->nullable()->default(0);
            $table->unique(['ozon_transaction_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_transaction_services');
    }
};
