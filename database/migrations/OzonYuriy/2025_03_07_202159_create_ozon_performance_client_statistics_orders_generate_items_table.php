<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_performance_client_statistics_orders_generate_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('ozon_performance_client_statistics_orders_generate_id')->constrained('ozon_performance_client_statistics_orders_generate')->cascadeOnDelete();

            $table->string('campaign_id')->nullable();

            $table->date('date')->nullable();

            $table->unsignedBigInteger('orderId')->default(0);
            $table->string('order_number')->nullable();
            $table->string('sku')->nullable();
            $table->string('advSku')->nullable();
            $table->string('offerId')->nullable();
            $table->string('title')->nullable();
            $table->unsignedBigInteger('quantity')->default(0);
            $table->decimal('price', 20, 2)->default(0);

            $table->decimal('salePrice', 20, 2)->default(0);
            $table->decimal('cost', 10, 2)->default(0);
            $table->decimal('bid', 10, 2)->default(0);
            $table->decimal('bidValue', 10, 2)->default(0);
            $table->decimal('moneySpent', 20, 2)->default(0);

            $table->unique(['campaign_id', 'ozon_performance_client_statistics_orders_generate_id']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_performance_client_statistics_orders_generate_items');
    }
};
