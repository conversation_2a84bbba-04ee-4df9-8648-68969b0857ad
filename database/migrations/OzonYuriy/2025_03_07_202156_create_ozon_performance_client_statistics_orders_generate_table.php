<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_performance_client_statistics_orders_generate', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->nullable()->constrained();
            // $table->foreignUuid('ozon_performance_credential_id')->constrained();
            // $table->foreignUuid('ozon_performance_campaign_id')->constrained();

            $table->string('campaign_id')->nullable();
            $table->string('title')->nullable();

            $table->decimal('moneySpent', 20, 2)->default(0);
            $table->decimal('corrections', 20, 2)->default(0);

            $table->unique(['campaign_id', 'title']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_performance_client_statistics_orders_generate');
    }
};
