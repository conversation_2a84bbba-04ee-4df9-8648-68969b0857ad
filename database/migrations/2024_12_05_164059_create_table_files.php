<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('path');
            $table->unsignedInteger('size');
            $table->string('mime_type');
            $table->boolean('is_private')->default(false);

            $table->foreignUuid('employee_id')->constrained();

            $table->string('type')->nullable();
        });

        Schema::create('file_relations', function (Blueprint $table) {
            $table->foreignUuid('file_id')->constrained()->cascadeOnDelete();
            $table->uuid('related_id');
            $table->string('related_type');

            $table->unique(['file_id', 'related_id', 'related_type']);
        });

        Schema::table('legal_heads', function (Blueprint $table) {
            $table->foreignUuid('head_signature_image_id')->nullable()->references('id')->on('files'); //файлы
            $table->foreignUuid('accountant_signature_image_id')->nullable()->references('id')->on('files');
            $table->foreignUuid('stamp_image_id')->nullable()->references('id')->on('files');
        });
        Schema::table('legal_entities', function (Blueprint $table) {
            $table->foreignUuid('logo_image_id')->nullable()->references('id')->on('files');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('legal_heads', function (Blueprint $table) {
            $table->dropForeign(['head_signature_image_id']);
            $table->dropForeign(['accountant_signature_image_id']);
            $table->dropForeign(['stamp_image_id']);
            $table->dropColumn(['head_signature_image_id', 'accountant_signature_image_id', 'stamp_image_id']);
        });
        Schema::table('legal_entities', function (Blueprint $table) {
            $table->dropForeign(['logo_image_id']);
            $table->dropColumn('logo_image_id');
        });
        Schema::dropIfExists('file_relations');
        Schema::dropIfExists('files');
    }
};
