<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contractors', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->softDeletes();
            $table->timestamps();
            $table->dateTime('archived_at')->nullable();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->boolean('shared_access')->default(0);           // Общий доступ
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();

            $table->string('title');                                // Наименование
            $table->foreignUuid('status_id')->constrained();        // Статус

            $table->boolean('is_buyer')->default(1);                   // Покупатель
            $table->boolean('is_supplier')->default(0);                // Поставщик
            $table->string('phone', 15)->nullable();
            $table->string('fax')->nullable();                      // Факс
            $table->string('email')->nullable();                    // Элетронный адрес

            $table->text('description')->nullable();                // Комментарий
            $table->string('code')->nullable();                     // Код
            $table->string('external_code')->nullable();            // Внешний код
            $table->string('discounts_and_prices')->nullable();     // Скидки и цены  -Цена продажи
            $table->string('discount_card_number')->nullable();     // Номер диск. карты
            // Файлы
            // Наименование
            // Размер
            // Дата добавления
            // Сотрудник

            $table->boolean('is_default')->default(false);
        });

        // Schema::create('contractors_files', function (Blueprint $table) {
        //     $table->uuid('id')->primary();
        //     $table->foreignUuid('contractor_id')->constrained()->cascadeOnDelete();
        //     $table->string('title')->nullable();
        //     $table->string('url');
        //     $table->string('size');
        //     $table->string('mime_type');
        //     $table->string('type');
        //     $table->softDeletes();
        //     $table->timestamps();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contractors_files');
        Schema::dropIfExists('contractors');
    }
};
