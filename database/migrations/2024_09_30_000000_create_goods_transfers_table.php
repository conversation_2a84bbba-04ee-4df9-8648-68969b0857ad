<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goods_transfers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->boolean('is_common')->default(false);

            $table->string('number')->nullable();
            $table->dateTime('date_from');

            $table->foreignUuid('status_id')->nullable()->constrained();
            $table->boolean('held')->default(true);

            $table->foreignUuid('legal_entity_id')->constrained();
            $table->foreignUuid('to_warehouse_id')->references('id')->on('warehouses');
            $table->foreignUuid('from_warehouse_id')->references('id')->on('warehouses');
            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');
            $table->string('currency_value')->default(1);

            $table->text('comment')->nullable();

            $table->string('overhead_cost')->default('0');
            $table->string('total_price')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goods_transfers');
    }
};
