<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->dateTime('archived_at')->nullable();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->string('title');
            $table->string('short_title');
            $table->tinyInteger('type');
            $table->text('description')->nullable();
            $table->string('short_description', 100)->nullable();
            $table->boolean('discounts_retail_sales')->default(false);
            $table->foreignUuid('product_group_id')->nullable()->constrained();
            $table->foreignUuid('country_id')->nullable()->constrained();
            $table->string('article')->nullable();
            $table->string('code')->nullable();
            $table->bigInteger('inner_code')->nullable();
            $table->string('external_code')->nullable();
            $table->foreignUuid('measurement_unit_id')->nullable()->constrained();
            $table->foreignUuid('brand_id')->nullable()->constrained();
            $table->string('min_price')->default(0);
            $table->foreignUuid('min_price_currency_id')->nullable()->references('id')->on('cabinet_currencies');
            $table->string('purchase_price')->default(0);
            $table->foreignUuid('purchase_price_currency_id')->nullable()->references('id')->on('cabinet_currencies');
            $table->decimal('length', 8, 2)->default(0);
            $table->decimal('width', 8, 2)->default(0);
            $table->decimal('height', 8, 2)->default(0);
            $table->decimal('weight')->default(0);
            $table->decimal('volume')->default(0);
            $table->foreignUuid('tax_id')->nullable()->references('id')->on('profit_tax_rates');
            $table->string('tax_system')->nullable();
            $table->string('indication_subject_calculation')->nullable();
            $table->tinyInteger('threshold_type')->nullable();
            $table->integer('threshold_count')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->unique(['cabinet_id','code']);
            $table->foreignUuid('employee_id')->nullable()->constrained();
            $table->foreignUuid('department_id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['cabinet_id']);
        });

        Schema::dropIfExists('products');
    }
};
