<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tnwed_codes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_type_id')->constrained()->cascadeOnDelete();
            $table->string('code');                                 // Коды ТН ВЭД
            $table->softDeletes();
            $table->timestamps();
        });
    }

    // Коды ТН ВЭД - Код Товарной номенклатуры внешнеэкономической деятельности.
    // Используется Информационной системой маркировки и прослеживаемости для работы с кодами маркировки.

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tnwed_codes');
    }
};
