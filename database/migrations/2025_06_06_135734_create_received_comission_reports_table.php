<?php

use App\Enums\Api\Internal\ComissionTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('received_comission_reports', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('number');
            $table->foreignUuid('cabinet_id')->constrained();
            $table->foreignUuid('employee_id')->nullable()->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->boolean('is_common')->default(false);

            $table->foreignUuid('status_id')->nullable()->constrained();

            $table->dateTime('date_from');

            $table->boolean('is_held')->default(true);
            $table->boolean('is_printed')->default(false);

            $table->foreignUuid('legal_entity_id')->constrained();
            $table->foreignUuid('contractor_id')->constrained();

            $table->string('incoming_number')->nullable();
            $table->date('incoming_date')->nullable();

            $table->foreignUuid('sales_channel_id')->nullable()->constrained();
            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');

            $table->dateTime('period_from');
            $table->dateTime('period_to');

            $table->foreignUuid('contract_id')->constrained();

            $table->string('comission_type')->default(ComissionTypeEnum::NONE->value);
            $table->string('comission_value')->default(0);
            $table->string('other_services_price')->default('0');

            $table->string('sum')->default('0');
            $table->text('comment')->nullable();
        });

        Schema::create('received_comission_report_realized_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('product_id')->constrained();
            $table->foreignUuid('report_id')->references('id')->on('received_comission_reports');
            $table->unsignedBigInteger('quantity')->default(0);
            $table->string('price');

            $table->foreignUuid('vat_rate_id')->constrained();
            $table->string('summary_price')->default('0');
            $table->string('comission_value')->default('0');
        });

        Schema::create('received_comission_report_return_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('report_id')->references('id')->on('received_comission_reports');
            $table->foreignUuid('product_id')->constrained();
            $table->unsignedBigInteger('quantity')->default(0);
            $table->string('price');

            $table->foreignUuid('vat_rate_id')->constrained();
            $table->string('summary_price')->default('0');
            $table->string('comission_return_value')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('received_comission_report_return_items');
        Schema::dropIfExists('received_comission_reports');
    }
};
