<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('legal_details', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignUuid('legal_entity_id')->constrained()->cascadeOnDelete();

            $table->string('taxation_type')->nullable();
            $table->foreignUuid('tax_rate')->nullable()->references('id')->on('profit_tax_rates');
            $table->foreignUuid('vat_rate')->nullable();

            $table->string('type');
            $table->string('prefix')->nullable();

            $table->string('inn', 12)->nullable();
            $table->string('kpp', 9)->nullable();
            $table->string('ogrn', 15)->nullable();
            $table->string('okpo', 10)->nullable();

            $table->string('full_name')->nullable();

            //Для ИП
            $table->string('firstname')->nullable();
            $table->string('patronymic')->nullable();
            $table->string('lastname')->nullable();

            $table->string('ogrnip')->nullable();
            $table->string('certificate_number')->nullable();
            $table->date('certificate_date')->nullable();

            $table->timestamps();
            $table->unique('legal_entity_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_details');
    }
};
