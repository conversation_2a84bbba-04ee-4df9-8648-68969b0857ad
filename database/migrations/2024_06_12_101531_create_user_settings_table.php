<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_settings', function (Blueprint $table) {
            $table->id();
            $table->string('image')->nullable();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('inn', 12)->nullable();                          // ИНН
            $table->string('language')->nullable();                         // Язык
            $table->string('printing_documents')->default(0);               // Печать документов
            $table->tinyInteger('additional_fields')->default(0);           // Число доп. полей в строке
            $table->string('start_screen')->nullable();                     // Стартовый экран
            $table->boolean('update_reports_automatically')->default(0);    // Обновлять отчеты автоматически
            $table->string('signature_sent_emails')->nullable();            // Подпись в отправляемых письмах
            $table->string('discount')->nullable();                         // Язык
            $table->softDeletes();
            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_settings', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });

        Schema::dropIfExists('user_settings');
    }
};
