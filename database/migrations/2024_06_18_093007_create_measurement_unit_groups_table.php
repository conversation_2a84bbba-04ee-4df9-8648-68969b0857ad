<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('measurement_unit_groups', static function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            $table->string('name');
            $table->string('tech_type');
            $table->boolean('is_system')->default(false);
            $table->foreignUuid('cabinet_id')->nullable()->constrained()->cascadeOnDelete();

            $table->unique(['tech_type', 'cabinet_id'], 'unique_tech_type_per_cabinet');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('measurement_unit_groups');
    }
};
