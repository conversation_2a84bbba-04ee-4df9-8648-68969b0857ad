<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('measurement_units', function (Blueprint $table) {
            $table->string('code')->nullable();
            $table->string('name')->nullable()->change();
            $table->string('short_name')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('measurement_units', function (Blueprint $table) {
            $table->dropColumn('code');
            $table->string('name')->change();
            $table->dropColumn('short_name');
        });
    }
};
