<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cabinet_measurement_system_groups', static function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('cabinet_id')->references('id')->on('cabinets')->onDelete('cascade');
            $table->foreignUuid('group_id')->references('id')->on('measurement_unit_groups')->onDelete('cascade');
            $table->foreignUuid('system_group_id')->references('id')->on('measurement_unit_groups')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cabinet_measurement_system_groups');
    }
};
