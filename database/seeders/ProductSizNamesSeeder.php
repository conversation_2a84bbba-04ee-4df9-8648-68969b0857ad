<?php

namespace Database\Seeders;

use App\Models\ProductSizNames;
use Illuminate\Database\Seeder;

class ProductSizNamesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            'Маска лицевая для защиты дыхательных путей, одноразового (многоразового) использования',
            'Респиратор общего применения',
            'Респиратор хирургический',
            'Средство назальное для защиты от загрязненного воздуха, местного действия',
            'Перчатки смотровые (процедурные)',
            'Перчатки смотровые (процедурные) полиизопреновые, опудренные, стерильные',
            'Набор гигиенической одежды для посетителей',
            'Комбинезон гигиенический для посетителей',
        ];

        foreach($data as $item) {

            ProductSizNames::create(['name' => $item]);

        }
    }
}
