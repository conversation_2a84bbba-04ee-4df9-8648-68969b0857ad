<?php

namespace Database\Seeders;

use App\Models\ProductSizTypes;
use Illuminate\Database\Seeder;

class ProductSizTypesSeeder extends Seeder
{
    public function dataSiz($data, $id): void
    {

        foreach($data as $items) {

            ProductSizTypes::create([
                'product_siz_name_id' => $id,
                'code' => $items[0],
                'title' => $items[1],
            ]);

        }

    }


    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $data1 = [
            [
                '2400001323807',
                'маска лицевая для защиты дыхательных путей, многоразового использования',
            ],
            [
                '2400003675805',
                'маска лицевая для защиты дыхательных путей, одноразового использования',
            ]
        ];

        $data2 = [
            [
                '2400001807703',
                'респиратор общего применения',
            ]
        ];

        $data3 = [
            [
                '2400001818303',
                'респиратор хирургический',
            ],
            [
                '2400002186203',
                'респиратор хирургический антибактериальный',
            ]
        ];

        $data4 = [
            [
                '2400001368105',
                'средство назальное для защиты от загрязненного воздуха, местного действия',
            ]
        ];

        $data5 = [
            [
                '2400001225408',
                'перчатки смотровые (процедурные) из латекса гевеи, неопудренные, нестерильные',
            ],
            ['2400001225606',
            'перчатки смотровые (процедурные) из латекса гевеи, опудренные',],
            ['2400001226108',
            'перчатки смотровые (процедурные) из латекса гевеи, неопудренные, стерильные',],
            ['2400001393503',
            'перчатки смотровые (процедурные) из полихлоропрена, неопудренные',],
            ['2400001858309',
            'перчатки смотровые (процедурные) нитриловые, неопудренные, нестерильные',],
            ['2400001858507',
            'перчатки смотровые (процедурные) нитриловые, опудренные',],
            ['2400002052805',
            'перчатки смотровые (процедурные) виниловые, неопудренные',],
            ['2400002052904',
            'перчатки смотровые (процедурные) виниловые, опудренные',],
            ['2400002984502',
            'перчатки смотровые (процедурные) из гваюлового латекса, неопудренные',],
            ['2400003117107',
            'перчатки смотровые (процедурные) из этиленвинилацетата, неопудренные, стерильные',],
            ['2400003117206',
            'перчатки смотровые (процедурные) из этиленвинилацетата, неопудренные, нестерильные',],
            ['2400003207907',
            'перчатки смотровые (процедурные) нитриловые, неопудренные, антибактериальные',],
            ['2400003215308',
            'перчатки смотровые (процедурные) полиизопреновые, неопудренные',],
            ['2400003297700',
            'перчатки смотровые (процедурные) нитриловые, неопудренные, стерильные',],
            ['2400003356704',
            'перчатки смотровые (процедурные) виниловые, неопудренные, стерильные',],
            ['2400003356803',
            'перчатки смотровые (процедурные) виниловые, опудренные, стерильные',],
            ['2400003433108',
            'перчатки смотровые (процедурные) из латекса гевеи, опудренные, стерильные',],
            ['2400003492303',
            'перчатки смотровые (процедурные) полиизопреновые, опудренные',],
            ['2400003495700',
            'перчатки смотровые (процедурные) из полихлоропрена, неопудренные, стерильные',],
            ['2400003495809',
            'перчатки смотровые (процедурные) из полихлоропрена, неопудренные, стерильные',],
            ['2400003495908',
            'перчатки смотровые (процедурные) нитриловые, опудренные, стерильные',],
            ['2400003496004',
            'перчатки смотровые (процедурные) полиизопреновые, неопудренные, стерильные',],
            ['2400003496103',
            'перчатки смотровые (процедурные) полиизопреновые, опудренные, стерильные',]
        ];

        $data6 = [
            ['2400001226306', 'перчатки хирургические из латекса гевеи, неопудренные',],
            ['2400001226405', 'перчатки хирургические из латекса гевеи, опудренные',],
            ['2400001393107', 'перчатки хирургические из полихлоропрена, неопудренные',],
            ['2400001393602', 'перчатки смотровые (процедурные) из полихлоропрена, опудренные',],
            ['2400001565306', 'перчатки хирургические из блоксополимера стирола, неопудренные, антибактериальные',],
            ['2400001857203', 'перчатки хирургические нитриловые, опудренные',],
            ['2400001857005', 'перчатки хирургические нитриловые, неопудренные',],
            ['2400002015909', 'перчатки хирургические полиизопреновые, неопудренные',],
            ['2400002016005', 'перчатки хирургические полиизопреновые, неопудренные, антибактериальные',],
            ['2400002016104', 'перчатки хирургические полиизопреновые, опудренные',],
            ['2400003161209', 'перчатки хирургические из блоксополимера стирола, неопудренные',],
            ['2400003227806', 'перчатки хирургические полимерно-композитные, неопудренные',],
            ['2400003237409', 'перчатки хирургические полимерно-композитные, неопудренные',],
            ['2400003263408', 'перчатки хирургические из латекса гевеи, неопудренные, антибактериальные',],
            ['2400003356902', 'перчатки хирургические из гваюлового латекса, неопудренные',],
            ['2400003356902', 'перчатки хирургические из полихлоропрена, опудренные',]
        ];

        $data7 = [
            [
                '2400002886806',
                'набор гигиенической одежды для посетителей',
            ]
        ];

        $data8 = [
            [
                '2400002886707',
                'комбинезон гигиенический для посетителей',
            ]
        ];

        $this->dataSiz($data1, 1);
        $this->dataSiz($data2, 2);
        $this->dataSiz($data3, 3);
        $this->dataSiz($data4, 4);
        $this->dataSiz($data5, 5);
        $this->dataSiz($data6, 6);
        $this->dataSiz($data7, 7);
        $this->dataSiz($data8, 8);

    }
}
