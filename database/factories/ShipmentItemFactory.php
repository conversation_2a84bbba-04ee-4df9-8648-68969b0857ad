<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Product;
use App\Models\Shipment;
use App\Models\VatRate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShipmentItem>
 */
class ShipmentItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cabinet = Cabinet::factory()->create();
        return [
            'shipment_id' => Shipment::factory()->create(['cabinet_id' => $cabinet->id]),
            'product_id' => Product::factory()->create(['cabinet_id' => $cabinet->id]),
            'quantity' => random_int(1, 100000),
            'price' => $this->faker->randomFloat(2, 0, 99999999),
            'cost' => $this->faker->randomFloat(2, 0, 99999999),
            'total_cost' => $this->faker->randomFloat(2, 0, 9999999999999),
            'total_price' => $this->faker->randomFloat(2, 0, 9999999999999),
            'profit' => $this->faker->randomFloat(2, 0, 9999999999999),
            'vat_rate_id' => VatRate::factory()->create(['cabinet_id' => $cabinet->id]),
            'discount' => random_int(0, 100),
        ];
    }
}
