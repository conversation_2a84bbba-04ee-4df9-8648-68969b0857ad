<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\Product;
use App\Models\VatRate;
use App\Models\VendorOrder;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VendorOrderItem>
 */
class VendorOrderItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = random_int(1, 999999);
        $priceInCurrency = $this->faker->randomFloat(2, 1, 1000);
        $currencyRateToBase = $this->faker->randomFloat(4, 0.1, 10);
        $discount = random_int(0, 100);
        $cabinet = Cabinet::factory()->create();
        
        return [
            'order_id' => VendorOrder::factory()->create([
                'cabinet_id' => $cabinet->id
            ]),
            'product_id' => Product::factory()->create([
                'cabinet_id' => $cabinet->id
            ]),
            'quantity' => $quantity,
            'currency_id' => CabinetCurrency::factory()->create([
                'cabinet_id' => $cabinet->id
            ]),
            'price_in_currency' => (string)$priceInCurrency,
            'currency_rate_to_base' => (string)$currencyRateToBase,
            'price_in_base' => (string)($priceInCurrency * $currencyRateToBase),
            'amount_in_base' => (string)($priceInCurrency * $currencyRateToBase * $quantity),
            'vat_rate_id' => VatRate::factory()->create([
                'cabinet_id' => $cabinet->id
            ]),
            'discount' => (string)$discount,
        ];
    }
}
