<?php

namespace Database\Factories;

use App\Enums\Api\Internal\NumberingType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CabinetSettings>
 */
class CabinetSettingsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => $this->faker->uuid,
            'numbering_type' => NumberingType::CPNumbers
        ];
    }
}
