<?php

namespace Database\Factories;

use App\Models\Document;
use App\Models\IncomingPayment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\IncomingPaymentItem>
 */
class IncomingPaymentItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        $document = Document::factory()->create();

        return [
            'incoming_payment_id' => IncomingPayment::factory(),
            'document_id' => $document->documentable_id,
            'paid_in' => $this->faker->boolean
        ];
    }
}
