<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\StatusType;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Status>
 */
class StatusFactory extends Factory
{
    use HasOrderedUuid;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'name' => $this->faker->word,
            'color' => $this->faker->hexColor,
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
            'cabinet_id' => Cabinet::factory(),
            'type_id' => StatusType::factory()
        ];
    }
}
