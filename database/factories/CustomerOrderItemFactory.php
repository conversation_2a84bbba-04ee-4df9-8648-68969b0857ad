<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\VatRate;
use App\Models\CustomerOrder;
use App\Models\CabinetCurrency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerOrderItem>
 */
class CustomerOrderItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $priceInCurrency = random_int(100, 999999);
        $currencyRate = '1.0';
        $quantity = random_int(1, 999);
        $priceInBase = bcmul((string)$priceInCurrency, $currencyRate, 2);
        $amountInBase = bcmul($priceInBase, (string)$quantity, 2);
        
        return [
            'order_id' => CustomerOrder::factory(),
            'product_id' => Product::factory(),
            'quantity' => $quantity,
            'vat_rate_id' => VatRate::factory(),
            'discount' => random_int(0, 20),
            'currency_id' => CabinetCurrency::factory(),
            'price_in_currency' => (string)$priceInCurrency,
            'currency_rate_to_base' => $currencyRate,
            'price_in_base' => $priceInBase,
            'amount_in_base' => $amountInBase,
        ];
    }
}
