<?php

namespace Database\Factories;

use Str;
use App\Models\Cabinet;
use App\Models\Category;
use App\Models\Employee;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\MeasurementUnit;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseItem>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => Str::orderedUuid()->toString(),
            'cabinet_id' => Cabinet::factory(),
            'title' => $this->faker->word(),
            'short_title' => $this->faker->word(),
            'type' => 1,
            'description' => $this->faker->text(),
            'short_description' => $this->faker->word,
            'discounts_retail_sales' => true,
            'product_group_id' => null,
            'country_id' => null,
            'article' => 1,
            'code' => $this->faker->unique()->randomNumber(),
            'inner_code' => 1,
            'external_code' => 1,
            'measurement_unit_id' => MeasurementUnit::factory(),
            'brand_id' => null,
            'min_price' => random_int(500, 800),
            'min_price_currency_id' => null,
            'purchase_price' =>  random_int(500, 800),
            'purchase_price_currency_id' => null,
            'length' => 7,
            'width' => 9,
            'height' => 9,
            'volume' => 19,
            'tax_system' => 1,
            'indication_subject_calculation' => 'asd',
            'contractor_id' => Contractor::factory(),
            'category_id' => Category::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory()
        ];

    }
}
