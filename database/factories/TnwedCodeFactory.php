<?php

namespace Database\Factories;

use App\Models\TnwedCode;
use App\Models\ProductTypes;
use Illuminate\Database\Eloquent\Factories\Factory;

class TnwedCodeFactory extends Factory
{
    protected $model = TnwedCode::class;

    public function definition(): array
    {
        return [
            'product_type_id' => ProductTypes::factory(),
            'code' => $this->faker->countryCode,
        ];
    }
}
