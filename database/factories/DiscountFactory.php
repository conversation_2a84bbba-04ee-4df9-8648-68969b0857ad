<?php

namespace Database\Factories;

use App\Enums\Api\Internal\DiscountTypeEnum;
use App\Models\Cabinet;
use App\Models\CabinetPrice;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;
use Random\RandomException;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Discount>
 */
class DiscountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     * @throws RandomException
     */
    public function definition(): array
    {
        return [
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->word,
            'type' => $this->faker->randomElement(DiscountTypeEnum::cases())->value,
            'status' => $this->faker->boolean,
            'cabinet_price_id' => CabinetPrice::factory(),
            'fixed_discount' => random_int(0, 100),
            'products_services' => $this->faker->boolean,
            'contractors' => $this->faker->boolean,
            'accrual_rule' => 1,
            'writeoff_rule' => 1,
            'max_proc_payment' => 0,
            'accrual_writeoff' => $this->faker->boolean,
        ];
    }
}
