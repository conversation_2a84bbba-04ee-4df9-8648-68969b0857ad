<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Acceptance;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Document>
 */
class DocumentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'documentable_id' => Acceptance::factory(),
            'documentable_type' => 'acceptances',
            'cabinet_id' => Cabinet::factory(),
            'tree_id' => $this->faker->uuid,
            'lft' => random_int(0,1000),
            'rgt' => random_int(0,1000)
        ];
    }
}
