<?php

namespace Database\Factories;

use App\Models\Document;
use App\Models\OutgoingPayment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\OutgoingPaymentItem>
 */
class OutgoingPaymentItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $document = Document::factory()->create();

        return [
            'outgoing_payment_id' => OutgoingPayment::factory(),
            'document_id' => $document->documentable_id,
            'paid_in' => $this->faker->boolean
        ];
    }
}
