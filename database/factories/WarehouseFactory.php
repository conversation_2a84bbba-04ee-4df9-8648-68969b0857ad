<?php

namespace Database\Factories;

use Str;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\Department;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseItem>
 */
class WarehouseFactory extends Factory
{
    protected $model = Warehouse::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => Str::orderedUuid()->toString(),
            'name' => $this->faker->name,
            'cabinet_id' => Cabinet::factory(),
            'work_schedule_id' => null,
            'control_free_residuals' => $this->faker->boolean(),
            'address_id' => null,
            'phone_id' => null,
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'is_common' => false
        ];
    }
}
