<?php

namespace Database\Factories;

use App\Models\Cabinet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseWorkSchedule>
 */
class WarehouseWorkScheduleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->word,
            'description' => $this->faker->text,
            'cabinet_id' => Cabinet::factory(),
            'date_from' => $this->faker->date,
            'date_to' => $this->faker->date,
            'filling_type' => random_int(1, 2),
            'cycle_day_lenght' => random_int(1, 100),
            'cycle_day_from' => $this->faker->date,
            'keep_holidays' => $this->faker->boolean,
            'holiday_schedule' => '{"start": "08:00","end": "12:00"}',
            'filling_template' => json_encode([
             "Monday" =>
                 [
                     "start"   => "08:00",
                     "end"     => "12:00"
                 ],
                 [
                     "start"   =>   "13:00",
                     "end" => "17:00"
                 ],
            "Tuesday" =>
             [
                 "start"   => "09:00",
                 "end" => "15:00"
             ]
            ]),
        ];
    }
}
