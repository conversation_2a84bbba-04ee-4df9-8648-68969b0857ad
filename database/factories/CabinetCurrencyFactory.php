<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CabinetCurrency>
 */
class CabinetCurrencyFactory extends Factory
{
    use HasOrderedUuid;

    public function definition(): array
    {
        return [
            'id' => $this->generateUuid(),
            'cabinet_id' => Cabinet::factory(),
            'currency_id' => null,
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
            'archived_at' => null,
            'is_accouting' => false,
            'external_id' => $this->faker->uuid,
            'num_code' => $this->faker->randomNumber(3),
            'char_code' => $this->faker->currencyCode,
            'short_name' => $this->faker->currencyCode,
            'name' => $this->faker->currencyCode,
            'type' => random_int(1, 2),
            'markup' => $this->faker->randomFloat(2, 0, 100),
            'nominal' => $this->faker->randomNumber(2),
            'value' => $this->faker->randomFloat(2, 0, 100),
            'is_reverse' => $this->faker->boolean,
            'pluralization' => null,
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'is_common' => false,
            'is_other' => false
        ];
    }
}
