<?php

namespace Database\Factories;

use App\Models\ProductSizNames;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductSizTypes>
 */
class ProductSizTypesFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_siz_name_id' => ProductSizNames::factory(),
            'code' => $this->faker->word,
            'title' => $this->faker->word
        ];
    }
}
