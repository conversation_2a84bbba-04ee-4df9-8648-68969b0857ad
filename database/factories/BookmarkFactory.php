<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Enums\Api\Internal\ResourcesEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Bookmark>
 */
class BookmarkFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'employee_id' => Employee::factory(),
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->word,
            'entity' => $this->faker->randomElement(ResourcesEnum::cases())->value,
            'filters' => json_encode($this->faker->word)
        ];
    }
}
