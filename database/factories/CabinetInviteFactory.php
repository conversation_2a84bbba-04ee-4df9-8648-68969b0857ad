<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use Illuminate\Support\Str;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Enums\Api\Internal\CabinetInviteStatusEnum;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CabinetInvite>
 */
class CabinetInviteFactory extends Factory
{
    use HasOrderedUuid;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'email' => fake()->email,
            'status' => CabinetInviteStatusEnum::WAITING,
            'token' => Str::random(16),
            'role_id' => null,
        ];
    }
}
