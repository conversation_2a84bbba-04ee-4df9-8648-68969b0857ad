<?php

namespace Database\Factories;

use App\Models\Contractor;
use App\Enums\Api\Internal\LegalEntityType;
use App\Enums\Api\Internal\LegalEntityTaxation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContractorDetail>
 */
class ContractorDetailFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'contractor_id' => Contractor::factory(),
            'taxation_type' => LegalEntityTaxation::osno->value,
            'type' => LegalEntityType::LEGAL->value,
            'inn' => (string)random_int(1, *********999),
            'kpp' => (string)random_int(1, *********),
            'ogrn' => (string)random_int(1, *********999999),
            'okpo' => (string)random_int(1, *********9),
            'full_name' => $this->faker->firstName . ' ' . $this->faker->name,
            'firstname' => $this->faker->firstName,
            'patronymic' => $this->faker->lastName,
            'lastname' => $this->faker->lastName,
            'ogrnip' => $this->faker->postcode,
            'certificate_number' => $this->faker->postcode,
            'certificate_date' => $this->faker->date,
        ];
    }
}
