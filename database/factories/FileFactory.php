<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\File>
 */
class FileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->name,
            'path' => $this->faker->filePath(),
            'size' => random_int(0,999999),
            'mime_type' => $this->faker->mimeType(),
            'is_private' => $this->faker->boolean,
            'employee_id' => Employee::factory(),
            'type' => $this->faker->randomElement(['file','image'])
        ];
    }
}
