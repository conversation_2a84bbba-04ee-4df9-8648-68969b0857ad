<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\AttributeGroups;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttributeGroupsFactory extends Factory
{
    protected $model = AttributeGroups::class;

    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->name,
            'description' => $this->faker->text,
            'sort_order' => random_int(0, 100000),
            'status' => $this->faker->boolean,
        ];
    }
}
