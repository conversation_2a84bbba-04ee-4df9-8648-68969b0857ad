<?php

namespace Database\Factories;

use App\Models\MeasurementUnitGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

class MeasurementUnitGroupFactory extends Factory
{
    protected $model = MeasurementUnitGroup::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->word(),
            'tech_type' => $this->faker->unique()->word
        ];
    }
}
