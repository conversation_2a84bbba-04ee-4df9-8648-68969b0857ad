<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Cabinet;
use App\Models\CabinetPrice;
use App\Models\CabinetCurrency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductPrice>
 */
class ProductPriceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'cabinet_price_id' => CabinetPrice::factory(),
            'cabinet_id' => Cabinet::factory(),
            'amount' => random_int(1,100000),
            'cabinet_currency_id' => CabinetCurrency::factory(),
            'sort' => random_int(1,100),
        ];
    }
}
