<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Country>
 */
class CountryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->name,
            'full_name' => $this->faker->country,
            'code' => $this->faker->countryCode,
            'iso2' => $this->faker->countryCode,
            'iso3' => $this->faker->countryCode,
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory()
        ];
    }
}
