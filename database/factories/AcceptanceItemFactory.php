<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Acceptance;
use Illuminate\Support\Str;
use App\Models\AcceptanceItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class AcceptanceItemFactory extends Factory
{
    protected $model = AcceptanceItem::class;

    public function definition(): array
    {
        $quantity = mt_rand(1, 5000);
        $price = mt_rand(1, 100000);
        $totalPrice = $quantity * $price;

        return [
            'id' => Str::orderedUuid()->toString(),
            'acceptance_id' => Acceptance::factory(),
            'product_id' => Product::factory(),
            'quantity' => $quantity,
            'price' => $price,
            'vat_rate_id' => null,
            'discount' => 0,
            'total_price' => $totalPrice,
            'country_id' => null,
            'gtd_number' => mt_rand(1, 100000)
        ];
    }
}
