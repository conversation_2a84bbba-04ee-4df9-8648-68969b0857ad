<?php

namespace Database\Factories;

use App\Models\Acceptance;
use App\Models\Cabinet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Barcode>
 */
class BarcodeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'barcodable_id' => Acceptance::factory(),
            'barcodable_type' => 'acceptance',
            'type' => random_int(0, 9),
            'value' => random_int(0, 9999999999999),
            'sort' => random_int(0, 100000),
            'is_generated' => $this->faker->boolean
        ];
    }
}
