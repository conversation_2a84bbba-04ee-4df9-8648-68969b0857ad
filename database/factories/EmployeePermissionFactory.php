<?php

namespace Database\Factories;

use App\Models\Employee;
use App\Models\Permission;
use App\Enums\Api\Internal\PermissionScopeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmployeePermission>
 */
class EmployeePermissionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'employee_id' => Employee::factory(),
            'permission_id' => Permission::factory(),
            'scope' => $this->faker->randomElement(PermissionScopeEnum::class)->value
        ];
    }
}
