<?php

namespace Database\Factories;

use App\Models\Contractor;
use App\Models\ContractorGroups;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContractorGroup>
 */
class ContractorGroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'group_id' => ContractorGroups::factory(),
            'contractor_id' => Contractor::factory()
        ];
    }
}
