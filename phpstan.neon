includes:
    - vendor/larastan/larastan/extension.neon

parameters:

    paths:
        - app/

    # Level 9 is the highest level
    level: 6

    ignoreErrors:
        - '#Access to an undefined property .*#'
        - identifier: missingType.iterableValue
        - identifier: missingType.generics
#        - '#PHPDoc tag @var#'
#        - '#PHPDoc tag @array<>#'
#
#    excludePaths:
#        - ./*/*/FileToBeExcluded.php
#
#    checkMissingIterableValueType: false
