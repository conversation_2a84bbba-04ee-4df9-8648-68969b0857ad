# PrecisionCalculator Trait

Трейт для точных денежных и числовых расчетов с использованием BcMath. Обеспечивает максимальную точность для бухгалтерских расчетов.

## Особенности

- **Максимальная точность**: 50 знаков после запятой (настраивается)
- **Поддержка разных типов**: принимает string, int и float, возвращает string
- **Автоматическая конвертация**: int и float автоматически преобразуются в строки
- **Автоматическая нормализация**: результаты очищаются от лишних нулей
- **Бухгалтерские функции**: НДС, скидки, проценты, конвертация валют
- **Безопасность**: защита от ошибок округления float

## Установка

Подключите трейт в ваш класс:

```php
<?php

use App\Traits\PrecisionCalculator;

class YourClass
{
    use PrecisionCalculator;
    
    // Ваши методы
}
```

## Поддерживаемые типы данных

Трейт автоматически обрабатывает следующие типы входных данных:

- **string**: `'123.456'` - рекомендуемый тип для максимальной точности
- **int**: `123` - автоматически конвертируется в `'123'`
- **float**: `123.456` - конвертируется в строку с сохранением точности

```php
// Все эти вызовы корректны и результаты нормализованы:
$result1 = $this->add('123.456', '789.012'); // '912.468' (не '912.468000000...')
$result2 = $this->add(123, 789); // '912' (не '912.000000...')
$result3 = $this->multiply(100, 1.5); // '150' (не '150.000000...')
$result4 = $this->divide(100, 4); // '25' (не '25.000000...')
$result5 = $this->rublesToKopecks(123.45); // '12345' (не '12345.000000...')
```

**Важно**: Все методы возвращают результат в виде строки для сохранения максимальной точности. Результаты автоматически нормализуются - лишние нули удаляются.

## Основные методы

### Арифметические операции

Поддерживает string, int и float в качестве входных параметров:

```php
// Сложение (разные типы)
$result = $this->add('123.456', 789); // string + int
$result = $this->add(123.456, 789.012); // float + float
$result = $this->add('123.456', '789.012'); // string + string

// Вычитание
$result = $this->subtract(1000, 250.75); // int - float = '749.25'

// Умножение
$result = $this->multiply('12.5', 8); // string * int = '100.0'

// Деление
$result = $this->divide(100, 3); // int / int = '33.333333333333333333333333333333333333333333333333'
```

### Сравнение чисел

```php
// Сравнение разных типов (-1, 0, 1)
$comparison = $this->compare(10.5, '10.50'); // 0 (равны)
$comparison = $this->compare(100, 99.99); // 1 (больше)

// Проверка равенства
$isEqual = $this->equals('0.1', 0.1); // true
$isEqual = $this->equals(100, '100.00'); // true

// Больше/меньше
$isGreater = $this->greaterThan(15.5, '15.49'); // true
$isLess = $this->lessThan(10, 10.01); // true
```

### Денежные операции

```php
// Конвертация рублей в копейки (принимает любой тип)
$kopecks = $this->rublesToKopecks('123.45'); // '12345'
$kopecks = $this->rublesToKopecks(123.45); // '12345'
$kopecks = $this->rublesToKopecks(123); // '12300'

// Конвертация копеек в рубли
$rubles = $this->kopecksToRubles(12345); // '123.45'
$rubles = $this->kopecksToRubles('12345'); // '123.45'

// Форматирование для отображения
$formatted = $this->formatMoney('1234567.89'); // '1 234 567.89'
```

### Проценты и скидки

```php
// Расчет процента от суммы
$percent = $this->calculatePercentage('1000', '15'); // '150'

// Применение скидки
$discounted = $this->applyDiscount('1000', '15'); // '850'

// Добавление процента (например, НДС)
$withTax = $this->addPercentage('1000', '20'); // '1200'
```

### НДС

```php
// Расчет НДС
$vat = $this->calculateVat('10000', '20');
/*
[
    'amount_without_vat' => '10000',
    'vat_amount' => '2000',
    'total_amount' => '12000',
    'vat_rate' => '20'
]
*/

// Выделение НДС из суммы с НДС
$extracted = $this->extractVat('12000', '20');
/*
[
    'amount_without_vat' => '10000',
    'vat_amount' => '2000',
    'total_amount' => '12000',
    'vat_rate' => '20'
]
*/
```

### Работа с массивами

Поддерживает массивы со смешанными типами данных:

```php
// Массив с разными типами
$amounts = ['100.50', 200.75, 300]; // string, float, int

// Сумма
$total = $this->sum($amounts); // '601.25'

// Среднее значение
$average = $this->average($amounts); // '200.41666666666666666666666666666666666666666666666667'

// Минимум и максимум
$min = $this->min($amounts); // '100.50'
$max = $this->max($amounts); // '300'
```

### Сложные проценты

```php
// Расчет сложных процентов
$final = $this->compoundInterest('100000', '12.5', '5');
// Результат: сумма через 5 лет при 12.5% годовых
```

### Утилиты

```php
// Абсолютное значение
$abs = $this->abs('-123.45'); // '123.45'

// Проверки
$isZero = $this->isZero('0.00'); // true
$isPositive = $this->isPositive('123'); // true
$isNegative = $this->isNegative('-45'); // true

// Валидация
$isValid = $this->isValidNumber('123.45'); // true

// Нормализация (удаление лишних нулей) - выполняется автоматически
$normalized = $this->normalize('123.4500'); // '123.45'
```

### Автоматическая нормализация

Все методы автоматически удаляют лишние нули из результатов:

```php
// ❌ Раньше могло быть:
// '1231.23123000000000000000000000000000000000000000000000'

// ✅ Теперь всегда:
$result = $this->multiply('1231.23123', '1'); // '1231.23123'
$result = $this->add('100.50', '200.50'); // '301' (не '301.00')
$result = $this->divide('100', '4'); // '25' (не '25.000000...')
$result = $this->normalize('0.00000'); // '0' (не '0.00000')
```

## Настройка точности

```php
// Установка пользовательской точности
$this->setPrecision(20);

// Получение текущей точности
$precision = $this->getPrecision(); // 50 (по умолчанию)
```

## Пример использования в модели

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Traits\PrecisionCalculator;

class Invoice extends Model
{
    use PrecisionCalculator;
    
    public function calculateTotal(): string
    {
        $subtotal = '0';
        
        foreach ($this->items as $item) {
            $itemTotal = $this->multiply($item->price, $item->quantity);
            $itemWithDiscount = $this->applyDiscount($itemTotal, $item->discount);
            $subtotal = $this->add($subtotal, $itemWithDiscount);
        }
        
        // Добавляем НДС
        $vatCalculation = $this->calculateVat($subtotal, '20');
        
        return $vatCalculation['total_amount'];
    }
    
    public function getFormattedTotalAttribute(): string
    {
        return $this->formatMoney($this->calculateTotal());
    }
}
```

## Важные замечания

1. **Поддерживаются string, int и float** - все автоматически конвертируются в строки
2. **Результат всегда string** - для максимальной точности
3. **Автоматическая нормализация** - лишние нули удаляются из всех результатов
4. **Float обрабатывается умно** - простые числа остаются чистыми, сложные нормализуются
5. **Никаких лишних нулей** - результаты всегда в читаемом виде
6. **Проверяйте входные данные** с помощью `isValidNumber()` при необходимости

## Сравнение с обычными операциями

```php
// ❌ Неточно (float)
$result = 0.1 + 0.2; // 0.30000000000000004
$isEqual = (0.1 + 0.2) === 0.3; // false

// ✅ Точно (BcMath)
$result = $this->add('0.1', '0.2'); // '0.3'
$isEqual = $this->equals($result, '0.3'); // true
```

Этот трейт обеспечивает максимальную точность для всех бухгалтерских расчетов и исключает ошибки округления, характерные для операций с float.
