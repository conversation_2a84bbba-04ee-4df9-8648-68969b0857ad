# Руководство по генерации API ресурсов

Этот документ содержит детальные инструкции для генерации API ресурсов Laravel в соответствии с архитектурой и
паттернами проекта.

## Общие принципы

### 1. Соответствие данным из БД/сервисов

- Ресурсы должны точно соответствовать структуре данных, получаемых из сервисов
- Все поля в ресурсах должны присутствовать в данных из БД или быть вычисляемыми
- Никаких "выдуманных" полей - только то, что реально возвращается

### 2. Разделение на Index и Show ресурсы

- **Index ресурсы**: для списков данных, содержат базовые поля
- **Show ресурсы**: для детального отображения, содержат полную информацию
- **Collection классы**: для управления коллекциями с метаданными

### 3. Использование только для GET запросов

- Ресурсы применяются только для отображения данных
- Используются для генерации OpenAPI через Scramble

## Архитектура ресурсов

### Структура директорий

```
app/Http/Resources/
├── {Domain}/
│   ├── {Entity}IndexCollection.php
│   ├── {Entity}IndexResource.php
│   └── {Entity}ShowResource.php
├── {EntityResource}.php (для простых случаев)
└── {EntityCollection}.php

app/Modules/Marketplaces/Http/Resources/
├── Marketplaces/
│   └── Ozon/
│       ├── FBO/
│       └── FBS/
└── Wildberries/
```

### Паттерны именования

- **Коллекции**: `{Entity}IndexCollection`
- **Ресурсы списка**: `{Entity}IndexResource`
- **Ресурсы детального отображения**: `{Entity}ShowResource`
- **Простые ресурсы**: `{Entity}Resource`

## Определение типов данных

### Соответствие миграций и ресурсов

| Тип в миграции          | Тип в ресурсе | Комментарий             |
|-------------------------|---------------|-------------------------|
| `uuid`                  | `string`      | Всегда строка           |
| `string`                | `string`      | Прямое соответствие     |
| `text`                  | `string`      | Любой текст как строка  |
| `boolean`               | `boolean`     | Прямое соответствие     |
| `integer`, `bigInteger` | `integer`     | Целые числа             |
| `decimal`               | `string`      | Для сохранения точности |
| `date`, `dateTime`      | `string`      | В формате ISO           |
| `json`                  | `array`       | JSON как массив         |

### Особые случаи

#### Цены и денежные значения

```php
// Миграция
$table->decimal('price', 20, 6)->default(0);
$table->unsignedBigInteger('total_price')->default(0);

// Ресурс
'price' => (string) $this->price,
'total_price' => (string) $this->total_price,
```

#### Nullable поля

```php
// Миграция
$table->string('comment')->nullable();

// Ресурс
'comment' => $this->comment,
```

#### Валюта и курсы

```php
// Всегда строка для точности
'currency_value' => $this->currency_value ? (string) $this->currency_value : '0',
```

## Шаблоны ресурсов

### 1. Index Resource (для списков)

```php
<?php

namespace App\Http\Resources\{Domain}\{Entity};

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class {Entity}IndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_common Флаг общего доступа */
            'is_common' => (bool) $this->is_common,
            
            // Основные поля сущности
            /** @var string $title Название */
            'title' => (string) $this->title,
            /** @var string $status Статус */
            'status' => $this->status,
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
        ];
    }
}
```

### 2. Show Resource (для детального отображения)

```php
<?php

namespace App\Http\Resources\{Domain}\{Entity};

use App\Http\Resources\FileResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class {Entity}ShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_common Флаг общего доступа */
            'is_common' => (bool) $this->is_common,
            
            // Основные поля сущности
            /** @var string $title Название */
            'title' => (string) $this->title,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,

            // Связанные данные
            /** @var array{id: string, title: string} $contractor Контрагент */
            'contractor' => $this->contractor ? [
                'id' => $this->contractor['id'],
                'title' => $this->contractor['title'],
            ] : [],
            
            /** @var array{id: string, name: string}|null $status Статус */
            'status' => $this->status ? [
                'id' => $this->status['id'],
                'name' => $this->status['name'],
            ] : [],

            /** @var array{id: string, name: string}|null $warehouse Склад */
            'warehouse' => $this->warehouse ? [
                'id' => $this->warehouse['id'],
                'name' => $this->warehouse['name']
            ] : [],

            /** @var AnonymousResourceCollection<FileResource>|null $files Файлы */
            'files' => $this->files ? FileResource::collection($this->files) : [],
        ];
    }
}
```

### 3. Index Collection (для коллекций с метаданными)

```php
<?php

namespace App\Http\Resources\{Domain}\{Entity};

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class {Entity}IndexCollection extends ResourceCollection
{
    public $collects = {Entity}IndexResource::class;

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => $this->additional['meta'] ? [
                /** @var int $current_page Текущая страница */
                'current_page' => $this->additional['meta']['current_page'],
                /** @var int $per_page Количество элементов на странице */
                'per_page' => $this->additional['meta']['per_page'],
                /** @var int $total Общее количество элементов */
                'total' => $this->additional['meta']['total'],
                /** @var int $last_page Последняя страница */
                'last_page' => $this->additional['meta']['last_page']
            ] : []
        ];
    }
}
```

## Работа со связанными данными

### Условное включение данных

```php
// Использование when() для условного включения
'contractor' => $this->when(!empty($this->contractor), [
    'id' => $this->contractor['id'],
    'title' => $this->contractor['title'],
]),

// Проверка на null
'status' => $this->status ? [
    'id' => $this->status['id'],
    'name' => $this->status['name'],
] : [],
```

### Коллекции связанных данных

```php
// Использование других ресурсов для связанных данных
'files' => $this->files ? FileResource::collection($this->files) : [],
'items' => $this->items ? {Entity}ItemResource::collection($this->items) : [],
```

### Массивы данных

```php
// Обработка массивов данных
'items' => collect($this->items ?? [])->map(function ($item) {
    return [
        'id' => $item->id ?? null,
        'name' => $item->name ?? null,
        'quantity' => (int) ($item->quantity ?? 0),
        'price' => (string) ($item->price ?? 0),
    ];
})->toArray(),
```

## Комментарии и документация

### Обязательные комментарии

```php
/** @var string $id Уникальный идентификатор записи */
/** @var string $created_at Дата и время создания записи */
/** @var string $updated_at Дата и время последнего обновления записи */
/** @var string|null $deleted_at Дата и время удаления */
/** @var bool $is_common Флаг общего доступа */
/** @var array{id: string, title: string} $contractor Контрагент */
/** @var AnonymousResourceCollection<FileResource>|null $files Файлы */
```

### Типы для связанных данных

```php
/** @var array{id: string, title: string} $contractor Контрагент */
/** @var array{id: string, name: string}|null $status Статус */
/** @var AnonymousResourceCollection<FileResource>|null $files Файлы */
```

## Использование в контроллерах

### Метод index()

```php
/**
 * @response {Entity}IndexCollection<{Entity}IndexResource>
 */
public function index({Entity}IndexRequest $request): ?JsonResponse
{
    $data = $this->service->index($request->toDTO());
    $collection = new {Entity}IndexCollection($data['data']);
    return $this->successResponse($collection->additional(['meta' => $data['meta']]));
}
```

### Метод show()

```php
/**
 * @response {Entity}ShowResource
 */
public function show(Request $request, string $id): JsonResponse
{
    $data = $this->service->show($id);
    return $this->successResponse({Entity}ShowResource::make($data));
}
```

## Особенности для модулей маркетплейсов

### Структура Ozon ресурсов

```php
// Для сложных вложенных структур
'delivery' => $this->when(!empty($this->delivery), [
    'address' => $this->delivery->address ?? null,
    'delivery_type' => $this->delivery->delivery_type ?? null,
    'warehouse' => $this->delivery->warehouse ?? null,
]),
```

### Условное включение элементов

```php
'items' => $this->when($this->items, function () {
    return OzonOrderItemResource::collection($this->items);
}),
```

## Валидация и проверки

### Перед созданием ресурса

1. Проверить миграцию сущности для определения типов данных
2. Изучить структуру данных из сервиса/репозитория
3. Определить необходимость разделения на Index/Show
4. Проверить связанные данные и их структуру

### Контрольный список

- [ ] Все поля соответствуют данным из БД/сервиса
- [ ] Типы данных корректно определены из миграций
- [ ] Денежные значения приведены к строке
- [ ] Добавлены PHPDoc комментарии
- [ ] Связанные данные обработаны правильно
- [ ] Создан соответствующий Collection класс
- [ ] Проверена работа с nullable полями

## Примеры для распространенных сущностей

### Товары (Products)

```php
'article' => $this->article,
'title' => (string) $this->title,
'price' => (string) $this->price,
'weight' => $this->weight ? (string) $this->weight : null,
'dimensions' => $this->when(!empty($this->dimensions), [
    'length' => (string) $this->length,
    'width' => (string) $this->width,
    'height' => (string) $this->height,
]),
```

### Заказы (Orders)

```php
'number' => (string) $this->number,
'date_from' => $this->date_from,
'status' => $this->status,
'total_price' => (string) $this->total_price,
'currency_value' => $this->currency_value ? (string) $this->currency_value : null,
'has_vat' => (bool) $this->has_vat,
'price_includes_vat' => (bool) $this->price_includes_vat,
```

### Контрагенты (Contractors)

```php
'title' => (string) $this->title,
'phone' => $this->phone,
'email' => $this->email,
'is_buyer' => (bool) $this->is_buyer,
'is_supplier' => (bool) $this->is_supplier,
'shared_access' => (bool) $this->shared_access,
```

Это руководство обеспечивает последовательный подход к созданию API ресурсов в соответствии с архитектурой проекта.
